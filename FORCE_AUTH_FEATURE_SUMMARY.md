# 强制认证后访问功能实现总结

## 功能描述
在身份设置页面新增了"强制认证后访问"开关，该功能参考了现有的"用户认证"开关的实现逻辑。

### 联动逻辑
- 当用户认证开关关闭时，强制认证后访问开关会自动关闭
- 当用户认证开关关闭时，强制认证后访问开关会被隐藏（使用 `v-show="isVerify"`）
- 只有在用户认证开关开启的情况下，强制认证后访问开关才会显示

### 联动逻辑
- 当用户认证开关关闭时，强制认证后访问开关会自动关闭
- 当用户认证开关关闭时，强制认证后访问开关会被禁用（不可点击）
- 只有在用户认证开关开启的情况下，强制认证后访问开关才能被操作

## 修改的文件

### 1. src/components/Identity/doctor.vue
- **模板部分**：在用户认证开关下方添加了强制认证后访问开关
- **脚本部分**：
  - 添加了 `isForceAuth` 响应式变量
  - 添加了 `forceAuthValue` 用于存储强制认证配置数据
  - 添加了 `switchForceAuth` 函数处理开关切换
  - 添加了 `updateForceAuthInfo` 函数更新强制认证配置
  - 在 `init` 函数中添加了获取强制认证配置的逻辑
  - 导入了新的接口函数 `getAccessAfterMandatoryCertification` 和 `updateAccessAfterMandatoryCertification`

### 2. src/request/api.js
- 添加了一个新的API端点：
  - `accessAfterMandatoryCertification: '/config/accessAfterMandatoryCertification'` - 获取强制认证后访问配置

### 3. src/request/service.js
- 添加了一个新的服务函数：
  - `getAccessAfterMandatoryCertification()` - 获取强制认证后访问配置的GET请求

## 实现逻辑

### 数据获取
- 在页面初始化时，通过 `getAccessAfterMandatoryCertification()` 接口获取强制认证后访问的配置数据
- 根据返回的 `checked` 字段（1表示开启，0表示关闭）设置开关状态

### 数据保存
- 当用户切换开关时，触发 `switchForceAuth` 函数
- 该函数构造参数对象，包含当前配置数据和新的 `checked` 值
- 调用 `updateInfo` 函数保存配置（复用现有的更新逻辑）
- 保存成功后重新初始化页面数据

### 接口调用模式
- 获取数据：GET `/config/accessAfterMandatoryCertification`
- 保存数据：复用现有的 `updatePerfectInfo` 接口
- 参数格式与用户认证开关保持一致

## 样式和交互
- 开关样式与现有的用户认证开关完全一致
- 使用相同的颜色主题（激活色：#2f92ee，非激活色：#f0f2f5）
- 布局位置：位于用户认证开关和完善信息开关之间

## 测试状态
- 项目已成功编译启动
- 开发服务器运行在 http://localhost:8080/
- 所有语法检查通过，无编译错误

## 注意事项
- 后端需要实现对应的 `/config/accessAfterMandatoryCertification` 接口
- 接口返回格式应与现有的认证接口保持一致
- 数据库表结构需要支持强制认证后访问的配置存储
- 保存操作复用现有的 `updatePerfectInfo` 接口，无需额外实现保存接口
