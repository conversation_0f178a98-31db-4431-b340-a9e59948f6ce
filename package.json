{"name": "yxd_backstage", "version": "1.0.0", "private": false, "scripts": {"dev": "cross-env VUE_APP_TBA=dev vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.0.10", "ali-oss": "^6.15.2", "angular-expressions": "^1.1.8", "axios": "^0.21.1", "clipboard": "^2.0.11", "core-js": "~3.9.1", "cos-js-sdk-v5": "^1.4.21", "dayjs": "^1.10.6", "docxtemplater": "^3.37.2", "docxtemplater-image-module-free": "^1.1.1", "echarts": "^5.1.2", "echarts-wordcloud": "^2.1.0", "element-plus": "^1.0.2-beta.70", "file-saver": "^2.0.5", "hls.js": "^1.5.11", "html2canvas": "^1.4.1", "image-size": "^1.0.2", "jspdf": "^2.5.1", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "medsci-avue-form-design": "^0.3.3", "ms-form": "^2.4.8", "pizzip": "^3.1.4", "prismjs": "^1.29.0", "qiankun": "^2.6.3", "sortablejs": "^1.13.0", "vue": "~3.0.7", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.0.0", "vue-loader-v16": "^16.0.0-beta.5.4", "vue-prism-editor": "^1.3.0", "vue-router": "~4.0.4", "vue-schart": "^2.0.0", "vuex": "~4.0.0", "zhihu-particle": "^0.0.9"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.11", "@vue/cli-plugin-eslint": "~4.5.11", "@vue/cli-plugin-router": "~4.5.11", "@vue/cli-plugin-vuex": "~4.5.11", "@vue/cli-service": "~4.5.11", "@vue/compiler-sfc": "~3.0.7", "babel-eslint": "~10.1.0", "cross-env": "^7.0.3", "eslint": "~6.8.0", "eslint-plugin-vue": "~7.7.0", "sass": "~1.32.8", "sass-loader": "~8.0.2", "vue-cli-plugin-element-plus": "0.0.13", "webpack": "4.44.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}