[class*=" el-icon-lx"],
[class^="el-icon-lx"] {
  font-family: lx-iconfont !important;
}
.el-icon-my-index {
  background: url("../img/icon/index.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-index::before {
  content: "首页";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-peixun {
  background: url("../img/icon/peixun.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-peixun::before {
  content: "培训";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-yunying {
  background: url("../img/icon/peixun.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-yunying::before {
  content: "运营";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-store {
  background: url("../img/icon/jifen.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-store::before {
  content: "积分商城";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-data {
  background: url("../img/icon/shujv.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-data::before {
  content: "数据";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-user {
  background: url("../img/icon/user.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-user::before {
  content: "数据";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-setting {
  background: url("../img/icon/shezhi.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-setting::before {
  content: "数据";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-quanxian {
  background: url("../img/icon/quanxian.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-quanxian::before {
  content: "数据";
  font-size: 14px;
  visibility: hidden;
}
.el-icon-my-help {
  background: url("../img/icon/help.png")
    center no-repeat;
  background-size: contain;
}
.el-icon-my-help::before {
  content: "数据";
  font-size: 14px;
  visibility: hidden;
}
