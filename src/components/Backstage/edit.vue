<template>
  <div class="yxd_backstage_edit">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <h3>编辑权限</h3>
      </div>
      <div class="col">
        <span>组名称</span>
        <div>{{info.title}}</div>
        <!-- <el-input v-model="info.title"></el-input> -->
      </div>
      <div class="col sp">
        <span>组权限</span>
        <el-tree
          v-show="listData.length!==0"
          ref="tree"
          :data="listData"
          show-checkbox
          node-key="id"
          :check-strictly="true"
          :default-expand-all="true"
          :default-checked-keys="checkArray"
          :props="defaultProps"
          @check-change="handleNodeChange"
          @check="getChecked"
        >
        </el-tree>
        <span v-show="listData.length===0">(暂无数据)请先给父级用户组添加权限后，再添加子级权限</span>
      </div>
      <div class="btn">
        <el-button @click.stop="cancel">取消</el-button>
        <el-button @click.stop="save" type="primary">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { adminMenu, bundleAdminMenu, sysAdminMenuTree } from "@/request/service.js";
import { ElMessage } from "element-plus";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "backstage-edit",
  mixins: [initScroll],
  data() {
    return {
      name: "",
      listData: [],
      checkArray: [],
      allArray: [],
      defaultProps: {
        children: "childMenu",
        label: "title",
      },
      listCheck: [],
    };
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        roleId: this.info.id,
      };
     
      adminMenu(params).then((res) => {
        if (res.status == 200) {
          this.checkArray = res.data.selectedMenu;
          this.listData = res.data.menuTree;
          this.initScroll();
        }
      });
    },
    goBack() {
      this.$emit("before");
    },
    save() {
      let params = {
        menuIds: this.checkArray,
        roleId: this.info.id,
        title: this.info.title,
      };
      bundleAdminMenu(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("保存成功！");
          // 此处要刷新当前菜单状态
          let param = {
            title: "",
          };
          sysAdminMenuTree(param).then((res) => {
            this.$store.commit("setMenuInfo", res.data);
          });
          // this.goBack();
          window.location.reload();
        }
      });
    },
    cancel() {
      this.goBack();
    },
    handleNodeChange(data, checked, deep) {
      if (this.checkArray.includes(data.id)) {
        if (!checked) {
          this.checkArray = this.checkArray.filter((id) => {
            return id != data.id;
          });
        }
      } else {
        if (checked) {
          this.checkArray.push(data.id);
        }
      }
      if (data.childMenu.length > 0) {
        return false;
      }
    
    },
    getChecked(data){
      const node = this.$refs.tree.getNode(data.id);
      this.setNode(node);
    },
    setNode(node){
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node,node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node,node.checked);
      }
    },
    setChildrenNode(node,state){
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i],state);
      }
    },
    setParentNode(node){
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    }
  },
};
</script>

<style scoped lang="scss">
.container{
  padding-bottom:20px ;
}
.yxd_backstage_edit {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }
  .col {
    display: flex;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 20px;
    .el-input {
      width: 20%;
    }
    span {
      width: 80px;
      white-space: nowrap;
    }
  }
  .sp {
    align-items: flex-start;
  }
  .btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 50px;
  }
}
</style>
