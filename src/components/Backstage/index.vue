<template>
  <div class="yxd_backstage">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <h3>后台模块权限</h3>
      </div>
      <p class="txt">
        这是赋给用户组的后台权限。
      </p>
      <p class="txt">
        用户组 '项目管理员' 默认拥有所有模块的所有操作权限。
      </p>
      <div class="table">
        <el-table 
          border
          :data="tableData"
          row-key="id"
          tooltip-effect="dark"
          style="width: 100%"
          default-expand-all
          :tree-props="{children: 'childRoles', hasChildren: 'hasChildren'}"
        >
          <el-table-column prop="id" label="组ID" width="200" align="center">
          </el-table-column>
          <el-table-column prop="title" label="组名称" align="center">
          </el-table-column>
          <el-table-column label="权限" align="center">
            <template #default="scope">
              <el-button
                size="mini"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { adminQuery } from "@/request/service.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "backstage-index",
  mixins: [initScroll],
  data() {
    return {
      tableData: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    goEdit(row) {
      this.$emit("edit", row);
    },
    handleEdit(index, row) {
      this.goEdit(row);
    },
    init() {
      let params = {
        title: "",
      };
      adminQuery(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.initScroll();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container{
  padding-bottom: 20px;
}
.yxd_backstage {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .col {
    margin-top: 20px;
    span {
      display: block;
      margin-bottom: 20px;
    }
  }
  .table {
    ::v-deep {
       td:nth-child(1) {
        text-align: left;
        padding-left: 20px;
      }
    }
  }
}
</style>
