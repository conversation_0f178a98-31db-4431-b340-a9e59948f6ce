<template>
  <div class="yxd_classify">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div>
        <span>下表中的序号，为当前分类板块，在前台首页的展示顺序。上下拖动记录，可调整展示顺序。提交成功后，前台生效。</span>
         <div class="operate">
            <el-button
              @click="quit()"
              >返回</el-button
            >
            <el-button
              type="primary"
              @click="updateList"
              >提交</el-button
            >
          </div>
      </div>
      <div class="table">
        <el-table 
        border 
        :data="tableData" 
        tooltip-effect="dark"
        row-key="sort"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :default-sort = "{prop: 'date', order: 'descending'}"
        >
          <el-table-column prop="sort" label="序号" width="240" align="center" type="index">
          </el-table-column>
          <el-table-column
            prop="id"
            label="分类ID"
            width="240"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="tagName"
            label="分类名称"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="tagType"
            :formatter="formatTagName"
            label="分类类型"
            width="300"
            align="center"
            sortable
          >
          </el-table-column>
        </el-table>
      </div>
      
      <div class="pagination">
        <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination> -->
      </div>
    </div>
  </div>
</template>

<script>
import Sortable from "sortablejs";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
// import { ElMessage } from "element-plus";
import { addTag, deleteTag, listTag, updateTag, updateSort } from "@/request/service.js";
import classify  from  '@/constant/classify'
const { classifyType } = classify
export default {
  name: "classify",
  mixins: [initModule,initScroll],
  props:{
    type:{
      type: String,
      default: '',
    },
  },
  data() {
    return {
      categoryValue: "",
      categoryDialog: "",
      categoryList: [
        {
          label: "资讯",
          value: "article",
        },
        {
          label: "课程",
          value: "course",
        },
        {
          label: "直播",
          value: "live_info",
        },
        {
          label: "病例",
          value: "case",
        },
        {
          label: "调研",
          value: "survey",
        },
        {
          label: "医讯达",
          value: "eda",
        },
        {
          label: "指南",
          value: "eda",
        },
        {
          label: "期刊",
          value: "tool_impact_factor",
        },
        {
          label: "UGC",
          value: "ugc",
        },
      ],
      titleValue:'',
      title: "",
      textarea: "",
      editVisible: false,
      deleteVisible: false,
      ChildrenVisible: false,
      currentChose: {},
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      state:'',
      ids:[],
      titleChildren:''
    };
  },
  mounted() {
    this.rowDrop();
    this.init();
  },
  methods: {
    init() {
      this.tableData=[]
      let params = {
        pageIndex: 1,
        pageSize: 100,
        tagName: this.title,
        tagType: this.type,
      };
      listTag(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll()
        }
      });
    },
    rowDrop() {
      const tbody = document.querySelector(".el-table__body-wrapper tbody");
      let that = this;
      Sortable.create(tbody, {
      filter: ".el-table__row--level-1,.el-table__row--level-2",
        // fallbackOnBody: true,
        // group: {
        //   name: 'shared',
        //   pull: false,
        // },

        onEnd: function(evt) {
          //拖拽结束发生该事件
          that.tableData.splice(
            evt.newIndex,
            0,
            that.tableData.splice(evt.oldIndex, 1)[0]
          );
          var newArray = that.tableData.slice(0);
          that.tableData = [];
          that.$nextTick(function() {
            that.tableData = newArray;
             this.ids = [];
            that.tableData.forEach((d) => {
              this.ids.push(d.id);
            });
          });
        },
      });
    },
    // 提交分类列表
    updateList(){
        updateSort(this.ids).then((res) => {
              if (res.status == 200) {
                this.goBack() 
              }
            });
    },
    goBack() {
      this.$emit('back')
    },
    quit(){
      this.$confirm('排序还未保存,是否退出?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('back')
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });
    },
    // 格式化分类类别
    formatTagName(row, column, cellValue){
       const obj = classifyType.find(item=>item.name === cellValue)
       return obj ? obj.value :'未知'
    }
  },
};
</script>

<style scoped lang="scss">
.yxd_classify {
  .operate {
    float: right;
     ::v-deep {
       .el-button {
         width: 120px;
         margin-bottom: 30px;
      }
    }
  }
  .edit-main {
    .col {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-input {
        width: 30%;
      }
      .el-textarea {
        width: 30%;
      }
      .tl {
        width: 70px;
        white-space: nowrap;
        margin-right: 20px;
        text-align: right;
      }
    }
  }
}
</style>
