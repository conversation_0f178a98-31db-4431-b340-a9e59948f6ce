<template>
  <div class="containers">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item
        label="选择课程"
        prop="courseTitle"
        :rule="{ required: true, message: '请输入活动名称', trigger: 'blur' }"
      >
        <el-button
          @click="checkAssign('kecheng')"
          :disabled="search"
          v-if="!form.courseTitle"
          >选择课程</el-button
        >
        <div
          v-else
          style="
            display: flex;
            justify-content: space-between;
            width: 350px;
            color: #666666;
          "
        >
          {{ form.courseTitle }}
          <el-button
            style="border: none; color: #409eff; font-size: 14px; padding: 0"
            :disabled="search"
            @click="checkAssign('kecheng')"
            >修改</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="任务名称">
        <el-input
          v-model="form.title"
          style="width: 350px"
          placeholder="请输入内容"
        >
          <template #default="scope">
            {{ scope.row.title }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="任务模式">
        <el-input
          v-model="title"
          value="解锁模式"
          disabled
          style="width: 350px"
          placeholder="请输入内容"
        ></el-input>
      </el-form-item>
      <el-form-item label="完成条件">
        观看至全部时长的
        <el-input-number
          v-model="form.duration"
          @change="handleChange"
          :disabled="search"
          precision="0"
          :min="1"
          label="大于0小于100的整数"
        ></el-input-number>
        %
      </el-form-item>
      <el-form-item label="倍速播放">
        <el-switch
          v-model="form.speedAdjust"
          active-value="1"
          inactive-value="0"
          active-text="开"
          inactive-text="关"
        >
        </el-switch>
      </el-form-item>
      <el-form-item label="进度条控制">
        <el-select
          v-model="form.progressAdjust"
          style="width: 350px"
          placeholder="请选择"
          :disabled="search"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设置考试">
        <el-switch
          v-model="form.needExam"
          :disabled="search"
          active-value="1"
          inactive-value="0"
          active-text="开"
          inactive-text="关"
          @change="changeNeed"
        >
        </el-switch>
        <el-button
          style="display: block; margin-top: 20px"
          v-if="!form.examTitle && form.needExam == '1'"
          :disabled="search"
          @click="checkAssign('kaoshi')"
          >选择考试</el-button
        >
        <div
          v-if="form.examTitle"
          style="
            display: flex;
            justify-content: space-between;
            width: 350px;
            color: #666666;
          "
        >
          {{ form.examTitle }}
          <el-button
            style="border: none; color: #409eff; font-size: 14px; padding: 0"
            :disabled="search"
            @click="checkAssign('kaoshi')"
            >修改</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="任务有效期">
        <el-date-picker
          :disabled="search && timeUpdata"
          style="width: 350px"
          v-model="form.date"
          type="datetimerange"
          :minTime="new Date()"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <div class="btn">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="onSubmit">保存</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
  <!-- 选择试卷 -->
  <el-dialog v-model="dialog" :title="title">
    <el-table
      :data="tableData"
      style="width: 100%"
      @row-click="singleElection"
      class="tables_deep"
      highlight-current-row
    >
      <el-table-column align="center" width="55" label="">
        <template #default="scope">
          <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
          <el-radio
            class="radio"
            v-model="templateSelection"
            @change="change(scope.row.courseId ? 'course' : 'exam')"
            :label="scope.row.courseId || scope.row.examId"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :prop="type && type == 'kecheng' ? 'courseId' : 'examId'"
        :label="type && type == 'kecheng' ? '课程ID' : '考试ID'"
      >
      </el-table-column>
      <el-table-column
        align="center"
        :prop="type && type == 'kecheng' ? 'courseTitle' : 'examTitle'"
        :label="type && type == 'kecheng' ? '课程名称' : '考试名称'"
      >
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="checkCancel">取消</el-button>
        <el-button type="primary" @click="check()"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  courseList,
  taskAdd,
  taskExamList,
  taskDetail,
  edittask,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
export default {
  name: "addAssign",
  data() {
    return {
      pickerOptions: (time) => {
        return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
      },
      form: {
        progressAdjust: 1,
      },

      options: [
        { value: 0, label: "未看部分禁止拖拽" },
        { value: 1, label: "禁止拖拽" },
      ],
      title: "解锁模式",
      dialog: false,
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: "",
      templateSelection: "",
      courseTitle: "",
      search: false,
      timeUpdata: false,
    };
  },
  async mounted() {
    if (this.$route.query.id) {
      if (this.$route.query.state && this.$route.query.state != 0) {
        this.search = true;
      }
      let res = await taskDetail(this.$route.query.id);
      res.data.needExam = res.data.needExam ? "1" : "0";
      res.data.speedAdjust = res.data.speedAdjust ? "1" : "0";
      this.form = res.data;

      if (
        res.data.startTime == "1970-01-01 00:00:00" &&
        res.data.endTime == "9999-01-01 23:59:59"
      ) {
        this.form.date = [];
      } else {
        this.form.date = [res.data.startTime, res.data.endTime];
      }
      let date1 = new Date(res.data.startTime);
      let date2 = new Date();
      if (date1.getTime() > date2.getTime()) {
        this.timeUpdata = false;
      } else {
        this.timeUpdata = true;
      }
    }
  },
  methods: {
    singleElection(record,index){
       if (this.type == "kecheng") {
            this.form.courseTitle = record.courseTitle;
            this.form.title = record.courseTitle;
            this.form.courseId = record.courseId;
            this.templateSelection = record.courseId;
          
      } else {
            this.form.examId = record.examId;
            this.form.examTitle = record.examTitle;
            this.templateSelection =record.examId;
      }
    },
    changeNeed(e) {
      if (e == 0) {
        this.form.examTitle = "";
        this.form.examId = "";
      }
    },
    checkCancel() {
      this.dialog = false;
      this.templateSelection = "";
    },
    // 取消
    cancel() {
      this.$router.push(`/course?name=second`);
    },
    // 提交
    async onSubmit() {
      if (!this.form.courseId) {
        ElMessage.error("请选择一个课程");
        return;
      }

      if (!this.form.title) {
        ElMessage.error("请输入任务名称");
        return;
      }
      if (!this.form.duration) {
        ElMessage.error("完成条件必须是大于0小于100的整数");
        return;
      }
      if (parseInt(this.form.needExam) && !this.form.examId) {
        ElMessage.error("请选择一个考试");
        return;
      }
      if (this.form.date && this.form.date.length > 0) {
        this.form.startTime = this.form.date[0];
        this.form.endTime = this.form.date[1];
      } else {
        this.form.startTime = "1970-01-01 00:00:00";
        this.form.endTime = "9999-01-01 23:59:59";
      }
      this.form.type = 0;
      this.form.needExam = parseInt(this.form.needExam) ? "1" : "0";
      this.form.speedAdjust = parseInt(this.form.speedAdjust) ? "1" : "0";

      try {
        if (!this.$route.query.id) {
          let res = await taskAdd(this.form);
          if (res.status == 200) {
            this.$router.push(`/course?name=second`);
          }
        } else {
          this.form.id = this.$route.query.id;
          let res = await edittask(this.form);
          if (res.status == 200) {
            this.$router.push(`/course?name=second`);
          }
        }
      } catch (error) {}
    },
    handleChange(val) {
      if (val > 100) {
        ElMessage.error("完成条件必须是大于0小于100的整数");
      }
    },
    async init() {
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
      };
      let res = await courseList(params);
      this.tableData = res.data;
      this.total = res.totalSize;
    },
    async init1() {
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
      };
      let res = await taskExamList(params);
      this.tableData = res.data;
      this.total = res.totalSize;
    },
    async checkAssign(type) {
      this.type = type;
      this.dialog = true;
      if (this.type == "kecheng") {
        this.title = "选择课程";
        await this.init();
      } else {
        this.title = "选择考试";
        await this.init1();
      }
    },
    check() {
      if (this.type == "kecheng") {
        this.dialog = false;
        this.tableData.forEach((element) => {
          if (this.templateSelection == element.courseId) {
            this.form.courseTitle = element.courseTitle;
            this.form.title = element.courseTitle;
            this.form.courseId = element.courseId;
            this.templateSelection = "";
          }
        });
      } else {
        this.dialog = false;
        this.tableData.forEach((element) => {
          if (this.templateSelection == element.examId) {
            this.form.examId = element.examId;
            this.form.examTitle = element.examTitle;
            this.templateSelection = "";
          }
        });
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      if (this.type == "kecheng") {
        this.init();
      } else {
        this.init1();
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      if (this.type == "kecheng") {
        this.init();
      } else {
        this.init1();
      }
    },
    change(val) {
    },
  },
};
</script>

<style lang="scss" scoped>
.containers {
  padding: 20px 20px 0 20px;
  ::v-deep .el-button--default:hover,
  .el-button--default:focus {
    background: #fff;
    border: solid 1px #dcdfe6;
    color: #606266;
  }
  ::v-deep .el-link.el-link--primary {
    color: #409eff !important;
  }
  .btn {
    display: flex;
    justify-content: center;
    ::v-deep .el-button {
      width: 100px;
      height: 40px;
    }
    ::v-deep .el-button {
      width: 100px;
      height: 40px;
    }

    ::v-deep .el-button--primary:hover {
      opacity: 1;
    }
    ::v-deep .el-button + .el-button {
      margin-left: 30px !important;
    }
  }
  ::v-deep .el-form-item__label {
    width: 120px !important;
  }
  ::v-deep .el-form {
    display: flex;
    flex-wrap: wrap;
    .el-form-item {
      width: 50%;
    }
    .el-form-item:last-child {
      width: 100%;
      margin-top: 150px;
    }
  }
}
</style>