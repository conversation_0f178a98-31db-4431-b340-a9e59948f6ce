<template>
  <div class="search">
    <span>
      <div class="title">
        选择分类后，将列表中的数据上下拖动，保存，前台将按照列表中的顺序展示。
      </div>
      <div>
        <span style="margin-right: 20px">选择分类</span>
        <el-select
          v-model="value"
          class="m-2"
          @change="check"
          placeholder="选择分类"
          size="large"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.tagName"
            :value="item.id"
          />
        </el-select>
      </div>
    </span>
    <div class="btn">
      <el-button type="primary" v-if="btnList.course_sort_save" @click="submit">保存</el-button>
    </div>
  </div>
  <div class="draggable" style="padding: 20px 0">
    <el-table row-key="moduleId" :data="tableData" style="width: 100%" border>
      <el-table-column
        v-for="(item, index) in tableConfig.tableItems"
        :key="`col_${index}`"
        :prop="tableConfig.tableItems[index].prop"
        :label="item.label"
        align="center"
      >
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import Sortable from "sortablejs";
import topicModule from '@/mixins/topicModule.js'
import initScroll from '@/mixins/initScroll.js'
import {
  tagList,
  getObjectIdsByTagId,
  batchUpdateSort,
} from "@/request/service.js";
export default {
  mounted() {
    this.columnDrop();
    this.rowDrop();
    this.init();
  },
    mixins: [topicModule, initScroll],
  data() {
    return {
      value: "",
      options: [],
      oldList: [],
      newList: [],
       currentPage: 1,
      tableData: [
        {
          id: 1,
          name: "李一",
          gender: "男",
          age: 30,
          job: "会计",
        },
        {
          id: 2,
          name: "王二",
          gender: "未知",
          age: 18,
          job: "无业游民",
        },
        {
          id: 3,
          name: "张三",
          gender: "男",
          age: 50,
          job: "老板",
        },
      ],
      tableConfig: {
        tableItems: [
          {
            label: "显示顺序",
            prop: "sort",
          },
          {
            label: "课程ID",
            prop: "moduleId",
          },
          {
            label: "课程名称",
            prop: "title",
          },
        ],
      },
    };
  },
  methods: {
    async check(val) {
      let res1 = await getObjectIdsByTagId(val);
      this.tableData = res1.data;
    },
    async init() {
      let params = {
        pageIndex: 0,
        pageSize: 100,
        tagType: "course",
      };
      let res = await tagList(params);
      this.options = res.data;
      this.value = this.options[0].id;

      let res1 = await getObjectIdsByTagId(this.value);
      this.tableData = res1.data;
    },
    async submit() {
      let params = {
        moduleList: this.tableData,
        tagId: this.value,
      };
      await batchUpdateSort(params);
    },
    // 行拖拽
    rowDrop() {
      // 此时找到的元素是要拖拽元素的父容器
      const tbody = document.querySelector(
        ".draggable .el-table__body-wrapper tbody"
      );
      const _this = this;
      Sortable.create(tbody, {
        //  指定父元素下可被拖拽的子元素
        draggable: ".draggable .el-table__row",
        onEnd: ({ newIndex, oldIndex }) => {
          const targetRow = _this.tableData[oldIndex];
          _this.tableData.splice(oldIndex, 1);
          _this.tableData.splice(newIndex, 0, targetRow);
          _this.tableData.forEach((element,index) => {
            element.sort=index+1
          });
        },
      });
    },
    // 列拖拽
    columnDrop() {
      const wrapperTr = document.querySelector(
        ".draggable .el-table__header-wrapper tr"
      );
      this.sortable = Sortable.create(wrapperTr, {
        animation: 180,
        delay: 0,
        onEnd: (evt) => {
          const oldItem = this.newList[evt.oldIndex];
          this.newList.splice(evt.oldIndex, 1);
          this.newList.splice(evt.newIndex, 0, oldItem);
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.container{
  padding-top: 10px;
  padding-left: 0;
}
.search {
  color: #666666;
  margin: 0  0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title {
    margin: 20px 0 30px !important;
  }
}
.btn {
  ::v-deep .el-button {
    width: 120px;
    height: 40px;
  }
}
</style>