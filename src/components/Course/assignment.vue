<template>
  <div class="yxd_course">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">任务名称</span>
            <el-input v-model="title" placeholder="请输入内容"></el-input>
          </div>
          <div class="col">
            <span class="tl">创建时间</span>
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button type="primary" v-if="btnList.course_task_add" @click="add"
          >新建任务</el-button
        >
        <el-button
          v-if="btnList.course_task_export_all"
          @click="handleExport('', 'all')"
          >全部导出</el-button
        >
      </div>
      <div class="table">
        <el-table
          ref="multipleTable"
          border
          :data="tableData"
          :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55" align="center">
          </el-table-column> -->
          <el-table-column prop="id" label="任务ID" width="120" align="center">
          </el-table-column>
          <el-table-column
            prop="title"
            label="任务名称"
            width="260"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.title }}
            </template>
          </el-table-column>
          <el-table-column
            prop="courseTitle"
            label="课程名称"
            width="260"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="examTitle"
            label="考试"
            width="160"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            width="160"
            align="center"
          >
          </el-table-column>

          <el-table-column
            prop="createdTime"
            label="创建时间"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="state"
            label="任务状态"
            width="120"
            align="center"
          >
            <template #default="scope">
              <span
                :class="{ start: scope.row.state == '1' }"
                class="default"
              ></span>
              <span>{{ scope.row.state ? "已启用" : "已禁用" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="onTime"
            label="启用时间"
            width="200"
            align="center"
          >
          </el-table-column>
          <!-- 新增字段 -->
          <!-- <el-table-column prop="userHits" label="点踩次数" width="120" align="center">
          </el-table-column> -->
          <!-- 新增字段 -->
          <el-table-column
            prop="offTime"
            label="禁用时间"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="completeNum"
            label="完成人数"
            width="120"
            align="center"
          >
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            min-width="320"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                size="mini"
                v-if="btnList.course_task_edit"
                @click="edit(scope.row)"
              >
                修改
              </el-button>
              <el-button
                v-if="btnList.course_task_set"
                size="mini"
                @click="handleDown(scope.$index, scope.row)"
                >{{ scope.row.state == 0 ? "启用" : "禁用" }}</el-button
              >

              <el-button
                v-if="btnList.course_task_delete"
                size="mini"
                type="danger"
                plain
                :disabled="scope.row.state == 1&&scope.row.deleteState"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >

              <el-button
                v-if="btnList.course_task_export"
                size="mini"
                @click="handleExport(scope.row)"
              >
                导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 删除单条 -->
    <el-dialog title="删除提示" v-model="deleteCourse" width="70%">
      <span
        >是否删除 <el-tag>{{ currentChose.title }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteCourse = false" size="mini">取 消</el-button>
          <el-button type="primary" @click="deleteSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
      <span
        >是否{{ lineText == "启用提示" ? "启用" : "禁用" }}
        <el-tag>{{ currentChose.title }}</el-tag>
      </span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="lineSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import topicModule from "@/mixins/topicModule.js";
import controlDialog from "../controlDialog.vue";
import classifyDialog from "../classifyDialog.vue";
import {
  tasklist,
  setTask,
  taskdelete,

  // setDelePower
} from "@/request/service.js";

import { taskExport } from "@/request/download.js";
export default {
  name: "course",
  mixins: [topicModule],
  components: {
    controlDialog,
    classifyDialog,
  },
  data() {
    return {
      // powerVisible: false,
      deleteVisible: false,
      editVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      settingVisible: false,
      deleteCourse: false,
      title: "",
      time: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      classGroup: [],
      currentChose: {},
      choseData: [],
      ClassifyData: [],
      lineText: "上线提示",
      levelVisible: false,
      levelOptions: [1, 2, 3],
      levelChose: {},
      RoleList: [],
      radio: 1,
      selectPower: "",
      selectvalue: [],
      checkgroup: {
        checkgroup1: false,
        checkgroup2: false,
        checkgroup3: false,
      },
      checkList: [],
      ClassifyValue: [],
      offLineData: {},
    };
  },
  mounted() {
    this.init();
    // this.getPowerData()
    // this.getClassifyData()
  },
  activated() {
    this.init();
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
    showSendMsg() {
      let projectId = this.$store.state.projectInfo.id;
      let nestleProjectId = [80, 81, 82, 83, 149, 155];

      if (nestleProjectId.includes(projectId)) {
        return true;
      }
      return false;
    },
    showHandleEdit() {
      let projectId = this.$store.state.projectInfo.id;
      let qiluProjectId = [53];

      if (qiluProjectId.includes(projectId)) {
        return true;
      }
      return false;
    },
  },
  methods: {
    add() {
      this.$router.push({
        path: "course",
        query: { type: "add", name: this.$route.query.name },
      });
    },
    edit(row) {
      this.$router.push({
        path: "course",
        query: {
          type: "add",
          name: this.$route.query.name,
          id: row.id,
          state: row.state,
        },
      });
    },
    init() {
      this.tableData = [];
      let startTime = "";
      let endTime = "";
      if (this.time && this.time.length > 0) {
        startTime = this.time[0];
        endTime = this.time[1];
      }
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        endTime: endTime,
        startTime: startTime,
        title: this.title,
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      tasklist(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.total = res.totalSize;
          this.tableData.forEach((val) => {
            let startT = val.startTime;
            let endT = val.endTime;
            let start = new Date(startT).getTime();
            let end = new Date(endT).getTime();
            let news = new Date().getTime();
            console.log(start,news,end)
            if (start < news < end) {
              val.deleteState = true;
            } else {
              val.deleteState = false;
            }
          });
          // this.initScroll()
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds: [],
        tagType: "course",
      };
      listTag(params).then((res) => {
        this.ClassifyData = this.removeClassifyChild(res.data);
      });
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    // 清除空的子类
    removeClassifyChild(data) {
      return data.filter((item) => {
        if (item.childTags.length === 0) {
          delete item.childTags;
          return item;
        } else {
          return this.removeClassifyChild(item.childTags);
        }
      });
    },
    changeCheck() {},

    initTag() {
      let params = {
        tagIds: [],
        tagType: "course",
      };
      tagTypeList(params).then((res) => {
        if (res.status == 200) {
          this.classGroup = res.data;
          this.classGroup.forEach((val) => {
            val.checked = false;
          });
        }
      });
    },
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog("course");
      this.$refs.classifyRef.gitClassList();
      // this.initTag()
      // this.editVisible = true
    },
    setDetClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.initTag();
      this.deleteVisible = true;
    },
    saveEdit() {
      let courseIds = [];
      let tags = [];
      this.tags.forEach((d) => {
        tags.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      this.choseData.forEach((val) => {
        courseIds.push(val.courseId);
      });
      let params = {
        courseIds: courseIds,
        tags: tags,
      };
      updateCourseTag(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.editVisible = false;
        }
      });
    },
    saveDelete() {
      let courseIds = [];
      let tags = [];
      this.tags.forEach((d) => {
        tags.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      this.choseData.forEach((val) => {
        courseIds.push(val.courseId);
      });
      let params = {
        courseIds: courseIds,
        tags: tags,
      };
      deleteCourseTag(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    handleDown(index, row) {
      if (row.state == 0) {
        this.lineText = "启用提示";
      } else {
        this.lineText = "禁用提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleEdit(index, row) {
      this.levelVisible = true;
      this.levelChose = row;
    },
    // 发送通知
    handleSendMsg(row) {
      let params = {
        courseId: row.courseId,
        projectId: this.$store.state.projectInfo.id,
        // debugger
        // projectId: 149
      };
      courseSendNotice(params).then((res) => {
        if (res.status == 200 && res.message == "success") {
          ElMessage.success("发送成功");
        }
      });
    },
    // 数据导出
    handleExport(row, types) {
      if (types == "all") {
        // 全部导出
        taskExport(-1).then((res) => {
          if (res.size == 51) {
            ElMessage.warning("暂无数据导出");
          } else if (res.size == 63) {
            ElMessage.warning("服务错误，请稍后再试......");
          } else {
            let name = `课程任务数据详情.xlsx`;
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(res);
            link.download = name;
            link.click();
          }
        });
      } else {
        taskExport(row.id).then((res) => {
          if (res.size > 57) {
            let name = row.title + `数据详情.xlsx`;
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(res);
            link.download = name;
            link.click();
          } else if (res.size == 63) {
            ElMessage.warning("服务错误，请稍后再试......");
          } else {
            ElMessage.warning("暂无数据导出");
          }
        });
      }
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    reset() {
      this.title = "";
      this.time = "";
      this.selectvalue = [];
      this.ClassifyValue = [];
      this.$refs.multipleTable.clearSelection();
      let obj = {};
      obj.stopPropagation = () => {};
      try {
        this.$refs.cascader.clearValue(obj);
        this.$refs.Classify.clearValue(obj);
      } catch (err) {
        this.$refs.cascader.handleClear(obj);
        this.$refs.Classify.handleClear(obj);
      }
    },
    // 批量上线
    saveOnlineEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType: "course",
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量上线成功");
          this.init();
          this.onlineVisible = false;
        }
      });
    },
    // 批量下线
    saveOffEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType: "course",
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量下线成功");
          this.init();
          this.offLineVisible = false;
        }
      });
    },
    // 批量删除
    saveDeleteEdit() {
      let courseIds = [];
      this.offLineData.forEach((val) => {
        courseIds.push(val.courseId);
      });
      let params = {
        courseIds: courseIds,
        projectId: this.$store.state.projectInfo.id,
      };
      deleteCourse(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
    lineSave() {
      // 上线操作
      let params = {
        id: this.currentChose.id,
      };
      setTask(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("成功");
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData = this.choseData.filter(
        (item) => item.status == "已下线"
      );
      this.settingVisible = true;
    },
    alterPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.$refs.controlRef.openDialog("course");
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    levelEdit() {
      let params = {
        courseId: this.levelChose.courseId,
        level: this.levelChose.levelValue,
        projectId: this.$store.state.projectInfo.id,
        userName: this.$store.state.backInfo.userInfo.userName,
      };
      setCourseLevel(params).then((res) => {
        if (res.status == 200) {
          this.levelVisible = false;
          this.init();
        }
      });
    },
    //  点击权限多选框选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.selectvalue = [];
      nodesObj.forEach((item) => this.selectvalue.push(item.data.id));
    },
    //  点击分类多选框选项
    ClassifyChange() {
      let nodesObj = this.$refs["Classify"].getCheckedNodes();
      this.ClassifyValue = [];
      nodesObj.forEach((item) => this.ClassifyValue.push(item.data.id));
    },
    // 删除单条
    handleDelete(index, row) {
      this.deleteCourse = true;
      this.currentChose = row;
    },
    // 单条删除
    deleteSave() {
      let params = {
        id: this.currentChose.id,
      };
      taskdelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteCourse = false;
          this.init();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_course {
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }

  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }

    .right {
    }
  }
.container{
  padding-top: 10px;
  padding-left: 0;
}
  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      width: 58.333%;
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }

    .right {
    }
  }

  .edit-main {
    display: flex;
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }

  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}

.power-head {
  table {
    .line {
      height: 45px;
    }

    tr {
      padding: 30px 0;

      .title {
        width: 70px;
        // font-size: 20px;
      }
    }
  }
}
</style>

