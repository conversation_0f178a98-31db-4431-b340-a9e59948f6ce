<template>
  <div class="yxd_course">
    <div id="container" class="container" :class="{ isScroll: isScroll }" ref="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入课程标题"></el-input>
          </div>
          <div class="col">
            <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="发布开始时间"
              end-placeholder="发布结束时间" value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <!-- 筛选权限 -->
          <div class="col" v-if="true">
            <el-cascader
            :options="RoleList"
            ref="cascader"
            collapse-tags	
            placeholder="请选择权限"
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles' ,
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
          <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            placeholder="请选择分类"
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
        </div>
        <div class="right">
          <el-button @click="init1" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button v-if="btnList.batchSetRole" @click="alterPower">批量设置权限</el-button>
        <el-button v-if="btnList.batchSetTag" @click="setClass">批量设置分类</el-button>
        <!-- <el-button v-if="btnList.batch_delete_category_btn" @click="setDetClass">批量删除分类</el-button> -->
        <el-button v-if="btnList.batch_start_btn" @click="lineBatch">批量上线</el-button>
        <el-button v-if="btnList.batch_end_btn" @click="offLineBatch">批量下线</el-button>
        <el-button v-if="btnList.batch_remove_btn" @click="deleteClass" icon="el-icon-close">批量删除</el-button>
         <el-button  v-if="btnList.export_course" @click="handleExport('','all')" >全部导出</el-button>
         <el-button
          @click="filterList()"
          :type="btnStatus?'primary':''"
          >未设置分类</el-button
        >
      </div>
      <div class="table">
        <el-table ref="multipleTable" border :data="tableData" :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column sortable prop="id" label="ID" min-width="70" align="center">
          </el-table-column>
          <el-table-column prop="title" label="标题" width="260" align="center">
            <template #default="scope">
              <a :href="'/class/detail/'+scope.row.courseId" target="_blank">{{ scope.row.title }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="pushTime" label="发布时间/创建时间" sortable min-width="250" align="center">
            <template #default="scope">
            {{scope.row.pushTime}}/{{scope.row.createdTime}}
            </template>
          </el-table-column>
         
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="scope">
              <span :class="{ start: scope.row.status == '已上线' }" class="default"></span>
              <span>{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="hits" label="PV/UV" min-width="90" align="center">
             <template #default="scope">
               {{scope.row.hits}}/{{scope.row.userHits}}
             </template>
          </el-table-column>
          <!-- 新增字段 -->
          <!-- <el-table-column prop="userHits" label="点踩次数" width="120" align="center">
          </el-table-column> -->
          <!-- 新增字段 -->
          <el-table-column prop="userCollects" label="收藏/评论/转发" width="100" align="center">
             <template #default="scope">
               {{scope.row.userCollects}}/{{scope.row.userComments}}/{{scope.row.playCount}}
             </template>
          </el-table-column>
          <el-table-column prop="studyCount" label="学习/学完/学完率" width="100" align="center">
             <template #default="scope">
               {{scope.row.studyCount}}/{{scope.row.completeCount}}/{{scope.row.completeRate}}
             </template>
          </el-table-column>
          <el-table-column prop="courseLevel" label="课程等级" width="120" align="center">
            <template #default="scope">
              <span>{{
                  scope.row.courseLevel ? scope.row.courseLevel : '未设置等级'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="220" fixed="right">
            <template #default="scope">
              <el-button v-if="btnList.end_btn" size="mini" @click="handleDown(scope.$index, scope.row)">{{
                  scope.row.status == '已上线' ? '下线' : '上线'
              }}</el-button>
              <el-button size="mini" @click="handleEdit(scope.$index, scope.row)" v-if="showHandleEdit">
                编辑等级
              </el-button>
              <el-button v-if="btnList.remove_btn" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)" :disabled="scope.row.status == '已上线'">删除</el-button>
              <el-button size="mini" @click="handleSendMsg(scope.row)" v-if="showSendMsg">
                发送通知
              </el-button>
              <el-button v-if="btnList.export_course" size="mini" @click="handleExport(scope.row)">
                导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
      <!-- 编辑弹出框 -->
       
      <!-- <el-dialog title="批量设置分类" v-model="editVisible" width="70%">
        <div class="edit-head">
          <div class="left">
            <span>已选分类</span>
            <div class="edit-tag">
              <el-tag v-for="tag in tags" :key="tag.tagName" closable size="mini" :type="tag.type"> 
                {{ tag.tagName }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="edit-main">
          <div class="title">
            <span>可选分类</span>
            <div class="group"></div>
          </div>
          <div class="title" v-for="(item, index) in classGroup" :key="index">
            <div class="group">
              <div class="btn" @click="item.checked = !item.checked" :class="{ checked: item.checked }">
                {{ item.tagName }}
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog> -->

      <!-- 批量删除分类 -->
      <el-dialog title="批量删除分类" v-model="deleteVisible" width="70%">
        <div class="edit-head">
          <div class="left">
            <span>已选分类</span>
            <div class="edit-tag">
              <el-tag v-for="tag in tags" :key="tag.tagName" closable size="mini" :type="tag.type">
                {{ tag.tagName }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="edit-main">
          <div class="title">
            <span>可选分类</span>
            <div class="group"></div>
          </div>
          <div class="title" v-for="(item, index) in classGroup" :key="index">
            <div class="group">
              <div class="btn" @click="item.checked = !item.checked" :class="{ checked: item.checked }">
                {{ item.tagName }}
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDelete" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量上线" v-model="onlineVisible" width="70%">
        <h3>确定上线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量下线" v-model="offLineVisible" width="70%">
        <h3>确定下线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span>是否{{ lineText == '上线提示' ? '上线' : '下线' }}
          <el-tag>{{ currentChose.title }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="lineSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
       <!-- 删除单条 -->
      <el-dialog  title="删除提示" v-model="deleteCourse" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.title }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteCourse = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量设置权限弹框 -->
      <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog> 
      <!-- 批量设置分类弹框 -->
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
      <el-dialog title="编辑等级" v-model="levelVisible" width="70%">
        <el-select v-model="levelChose.levelValue" placeholder="请选择">
          <el-option v-for="(item, index) in levelOptions" :key="index" :label="item" :value="item">
          </el-option>
        </el-select>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="levelVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="levelEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import topicModule from '@/mixins/topicModule.js'
import initScroll from '@/mixins/initScroll.js'
import  controlDialog  from '../controlDialog.vue'
import  classifyDialog  from '../classifyDialog.vue'
import {
  deleteCourse,
  listCourse,
  offlineCourse,
  onlineCourse,
  updateCourseTag,
  deleteCourseTag,
  tagTypeList,
  setCourseLevel,
  courseSendNotice,
  exportViewLog,
  getRoleList,
  query,
  listTag,
  batchLineModule
  // setDelePower
} from '@/request/service.js'
import { exportViewList } from "@/request/download.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import filterList from "@/mixins/filterList.js";
export default {
  name: 'course',
  mixins: [topicModule, initScroll, initAccountTypeList,filterList],
  components:{
    controlDialog,
    classifyDialog
  },
  data() {
    return {
      // powerVisible: false,
      deleteVisible: false,
      editVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      settingVisible: false,
      deleteCourse:false,
      title: '',
      time: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      classGroup: [],
      currentChose: {},
      choseData: [],
      ClassifyData:[],
      lineText: '上线提示',
      levelVisible: false,
      levelOptions: [1, 2, 3],
      levelChose: {},
      RoleList: [],
      radio: 1,
      selectPower: '',
      selectvalue: [],
      checkgroup: {
        checkgroup1: false,
        checkgroup2: false,
        checkgroup3: false,
      },
      checkList: [],
      ClassifyValue: [],
      offLineData:{},
      accountTypeValue: '',
      refesh:false
    }
  },
  mounted() {
    this.init();
    this.getClassifyData()
  },
  activated(){
    this.init();
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked
      })
    },
    showSendMsg() {
      let projectId = this.$store.state.projectInfo.id
      let nestleProjectId = [80, 81, 82, 83, 149, 155]

      if (nestleProjectId.includes(projectId)) {
        return true
      }
      return false
    },
    showHandleEdit() {
      let projectId = this.$store.state.projectInfo.id
      let qiluProjectId = [53]

      if (qiluProjectId.includes(projectId)) {
        return true
      }
      return false
    },
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val){
      if(val===0||val===1){
        this.getPowerData(val)
      }else if(val===""){
        this.RoleList = []
      }
    },
    init() {
      if(this.choseData.length>0){
        this.refesh=true
      }
      this.tableData=[]
      let startTime = ''
      let endTime = ''
      if (this.time && this.time.length > 0) {
        startTime = this.time[0]
        endTime = this.time[1]
      }
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
        pushEndTime: endTime,
        pushStartTime: startTime,
        title: this.title,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue,
      }
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      listCourse(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data
          this.total = res.totalSize
          this.tableData.forEach((val) => {
            if (val.status == 1) {
              val.status = '已上线'
            } else {
              val.status = '已下线'
            }
          })
          this.filterData = this.tableData;
          this.btnStatus=false
          this.initScroll()
        }
      })
    },
    init1() {
      this.currentPage=1
      this.tableData=[]
      let startTime = ''
      let endTime = ''
      if (this.time && this.time.length > 0) {
        startTime = this.time[0]
        endTime = this.time[1]
      }
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
        pushEndTime: endTime,
        pushStartTime: startTime,
        title: this.title,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue,
      }
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      listCourse(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data
          this.total = res.totalSize
          this.tableData.forEach((val) => {
            if (val.status == 1) {
              val.status = '已上线'
            } else {
              val.status = '已下线'
            }
          })
          this.initScroll()
        }
      })
    },
    // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
      // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'course',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
   getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    changeCheck() { },
  
    initTag() {
      let params = {
        tagIds: [],
        tagType: 'course'
      }
      tagTypeList(params).then((res) => {
        if (res.status == 200) {
          this.classGroup = res.data
          this.classGroup.forEach((val) => {
            val.checked = false
          })
        }
      })
    },
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据！')
        return
      }
      this.$refs.classifyRef.openDialog('course')
      this.$refs.classifyRef.gitClassList()
      // this.initTag()
      // this.editVisible = true
    },
    setDetClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据！')
        return
      }
      this.initTag()
      this.deleteVisible = true
    },
    saveEdit() {
      let courseIds = []
      let tags = []
      this.tags.forEach((d) => {
        tags.push({
          tagId: d.tagId,
          tagName: d.tagName
        })
      })
      this.choseData.forEach((val) => {
        courseIds.push(val.courseId)
      })
      let params = {
        courseIds: courseIds,
        tags: tags
      }
      updateCourseTag(params).then((res) => {
        if (res.status == 200) {
          this.init()
          this.editVisible = false
        }
      })
    },
    saveDelete() {
      let courseIds = []
      let tags = []
      this.tags.forEach((d) => {
        tags.push({
          tagId: d.tagId,
          tagName: d.tagName
        })
      })
      this.choseData.forEach((val) => {
        courseIds.push(val.courseId)
      })
      let params = {
        courseIds: courseIds,
        tags: tags
      }
      deleteCourseTag(params).then((res) => {
        if (res.status == 200) {
          this.init()
          this.deleteVisible = false
        }
      })
    },
    handleDown(index, row) {
      if (row.status == '已上线') {
        this.lineText = '下线提示'
      } else {
        this.lineText = '上线提示'
      }
      this.dialogVisible = true
      this.currentChose = row
    },
    handleEdit(index, row) {
      this.levelVisible = true
      this.levelChose = row
    },
    // 发送通知
    handleSendMsg(row) {
      let params = {
        courseId: row.courseId,
        projectId: this.$store.state.projectInfo.id
        // debugger
        // projectId: 149
      }
      courseSendNotice(params).then((res) => {
        if (res.status == 200 && res.message == 'success') {
          ElMessage.success('发送成功')
        }
      })
    },
    // 数据导出
    handleExport(row,types) {
      console.log()
      if(types=="all"){
        // 全部导出
         exportViewList(this.time[0],this.time[1],this.$store.state.projectInfo.id,this.courseIds).then((res) => {
        if (res.size == 51) {
          ElMessage.warning("暂无数据导出");
        }else if(res.size==63){
          ElMessage.warning("上线时间不能为空");
        }
        else if(res.size==78){
          ElMessage.warning('请勾选需要导出记录的课程！');
        }
         else {
          let name = `课程数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        }
        })
      }else{
      exportViewLog(row.courseId).then((res) => {
        if (res.size > 57) {
          let name = row.title+`数据详情.xlsx`
          let link = document.createElement('a')
          link.href = window.URL.createObjectURL(res)
          link.download = name
          link.click()
        } else {
          ElMessage.warning('暂无数据导出')
        }
      })}
    },
    handleSelectionChange(selection) {
        this.choseData = selection
        this.courseIds=this.choseData.map((res)=>{
          return res.id
        })
    },
    reset() {
      this.title = ''
      this.time = ''
      this.selectvalue = []
      this.ClassifyValue = []
      this.$refs.multipleTable.clearSelection()
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    // 批量上线
    saveOnlineEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"course"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量上线成功");
          this.init()
          this.onlineVisible = false
        }
      })
    },
    // 批量下线
    saveOffEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"course"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量下线成功");
          this.init()
          this.offLineVisible = false
        }
      })
    },
    // 批量删除
    saveDeleteEdit() {
      let courseIds = []
      this.offLineData.forEach((val) => {
        courseIds.push(val.courseId)
      })
      let params = {
        courseIds: courseIds,
        projectId: this.$store.state.projectInfo.id
      }
      deleteCourse(params).then((res) => {
        if (res.status == 200) {
          this.init()
          this.settingVisible = false
        }
      })
    },
    lineSave() {
      let ids = []
      ids.push(this.currentChose.id)
      if (this.lineText == '上线提示') {
        // 上线操作
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"course"
      };
        batchLineModule(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("上线成功");
            this.currentChose.status =
            this.currentChose.status == '已下线' ? '已上线' : '已下线'
            this.dialogVisible = false
          }
        })
      } else {
        // 下线操作
        let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"course"
      };
        batchLineModule(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("下线成功");
            this.currentChose.status =
            this.currentChose.status == '已下线' ? '已上线' : '已下线'
            this.dialogVisible = false
          }
        })
      }
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.onlineVisible = true
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.offLineVisible = true
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.offLineData=this.choseData.filter(item=>item.status == "已下线")
      this.settingVisible = true
    },
    alterPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.$refs.controlRef.openDialog('course')
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.init()
    },
    levelEdit() {
      let params = {
        courseId: this.levelChose.courseId,
        level: this.levelChose.levelValue,
        projectId: this.$store.state.projectInfo.id,
        userName: this.$store.state.backInfo.userInfo.userName
      }
      setCourseLevel(params).then((res) => {
        if (res.status == 200) {
          this.levelVisible = false
          this.init()
        }
      })
    },
    //  点击权限多选框选项
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    },
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    // 删除单条
    handleDelete(index, row) {
      this.deleteCourse = true;
      this.currentChose = row;
    },
    // 单条删除
    deleteSave() {
      let courseIds = [];
      courseIds.push(this.currentChose.courseId);
      let params = {
        courseIds: courseIds,
        projectId: this.$store.state.projectInfo.id
      };
      deleteCourse(params).then((res) => {
        if (res.status == 200) {
          this.deleteCourse = false;  
          this.init();
        }
      });
    },
  }
}
</script>

<style scoped lang="scss">
.yxd_course {
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }

  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }

    .right {}
  }

  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      width: 58.333%;
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }

    .right {}
  }
.container{
  padding-top: 10px;
  padding-left: 0;
}
  .edit-main {
    display: flex;
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }

  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}

.power-head {
  table {
    .line {
      height: 45px;
    }

    tr {
      padding: 30px 0;

      .title {
        width: 70px;
        // font-size: 20px;
      }


    }
  }

}
</style>

