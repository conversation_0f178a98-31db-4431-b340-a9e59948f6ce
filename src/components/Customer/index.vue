<template>
  <div class="yxd_users">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            统计周期：
            <el-select
              v-model="type"
              style="margin-right: 10px;"
              clearable
              class="m-2"
              placeholder="请选择统计周期"
              @change="handleChange"
            >
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-date-picker
              v-if="type == 0"
              v-model="lineTime"
              type="date"
              placeholder="选择自然日"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <el-date-picker
              v-if="type == 1"
              v-model="lineTime"
              type="month"
              placeholder="选择自然月"
              format="YYYY-MM"
              value-format="YYYY-MM"
            >
            </el-date-picker>
            <el-date-picker
              v-if="type == 2"
              v-model="lineTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled-date="disabledDate"
            >
            </el-date-picker>
          </div>
          <div class="col">
            <el-input v-model="nameOrmobile" placeholder="请输入姓名或者手机号"></el-input>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button
          @click="handleExportUserInfo"
          >导出用户数据</el-button
        >
      </div>
      <div class="table">
        <ms-table
          :columns="data.columns"
          :data="data.list"
          @sortChange="sortChange"
          ref="tableRef"
        ></ms-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>

import { customersExport, customersAccounts } from "@/request/service.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { onMounted, ref, reactive } from "vue";
import { ElMessage } from "element-plus";

export default {
  name: "users",
  mixins: [initModule, initScroll],
  emits: ["add", "edit"],
  components: {},
  setup() {
    onMounted(() => {
      init();
    });
    const currentPage = ref(1);
    const pageSize = ref(20);
    const total = ref(0);
    const nameOrmobile = ref("");
    const colmuns = [
      {
        label: "客户",
        prop: "customName",
        render: (h, scope) => {
          return h(
            "div",{},
            [
              h(
                'div',{
                  style: {
                    color:'#409eff',
                    textAlign: "left",
                  },
                },
                scope.row.customName
              ),
              h('div',{
                style: {
                  textAlign: "left",
                }
              },scope.row.customMobile),
              h('div',{
                style: {
                  textAlign: "left",
                  color: scope.row.level ? 'rgb(237, 106, 12)' : '#999',
                  border: '1px solid #999',
                  padding: '0px 3px',
                  borderRadius: '3px',
                  borderColor: scope.row.level ? 'rgb(237, 106, 12)' : '#999',
                  width: 'max-content'
                }
              },scope.row.level ? scope.row.level : scope.row.isMobileAuth == '是' ? '' : '未授权')
            ]
          )
        }
      },
      {
        label: "购买次数",
        prop: "tradeCount",
        sortable: 'custom'
      },
      {
        label: "累计消费金额",
        prop: "sales",
        sortable: 'custom'
      },
      {
        label: "上次消费时间",
        prop: "lastPayTime",
        sortable: 'custom'
      },
      {
        label: "分销员",
        prop: "salesmanName",
        render: (h, scope) => {
          return h(
            "div",
            {
              style: {color:'#409eff'}
            },
            scope.row.salesmanName
          )
        }
      }
    ];
    const data = reactive({
      columns: [],
      list: [],
    });
    const currentChose = reactive({});
    const currentFile = reactive({
      file: null,
    });
    const treeRef = ref(null);
    const userImportFailList = ref([]);
    const disabledDate = (time) => {
      let currentTime = new Date();
      let weekTime = 60 * 60 * 1000 * 24 * 7;
      let threeMonth = 60 * 60 * 1000 * 24 * 90
      if(currentTime){
        return time.getTime() > currentTime.getTime() + weekTime ||
						time.getTime() < currentTime.getTime() - threeMonth
      }
    }
    const type = ref(0);
    const typeList = ref([
      {id: 0, name: "自然日"},
      {id: 1, name: "自然月"},
      {id: 2, name: "自定义"}
    ])

    const lineTime = ref(null);
    const sortField = ref('');
    const isAsc = ref(false);
    const init = async () => {
      data.list = [];
      data.columns.length = 0;
      data.columns.push(...colmuns);
      let params = {
        nameOrMobile: nameOrmobile.value,
        startTime: '',
        endTime: '',
        pageSize: pageSize.value,
        pageNo: currentPage.value,
        sortField: sortField.value,
        isAsc: isAsc.value
      };
      if(type.value == 0){
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value).getTime()/1000) : '';
        params.endTime = lineTime.value ? Math.floor(new Date(lineTime.value.slice(0,10) + ' 23:59:59').getTime()/1000) : '';
      }
      if(type.value == 1){
        const date = lineTime.value ? new Date(lineTime.value) : '';
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value + '-01 00:00:00').getTime()/1000) : '';
        if(date){
          date.setMonth(date.getMonth() + 1);
          date.setDate(0);
          const lastDay = date.toLocaleDateString().split('/').join('-');
          params.endTime = lastDay ? Math.floor(new Date(lastDay.slice(0,5) + '0' + lastDay.slice(5) + ' 23:59:59').getTime()/1000) : '';
        }
      }
      if(type.value == 2){
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value[0]).getTime()/1000) : '';
        params.endTime = lineTime.value ? Math.floor(new Date(lineTime.value[1]).getTime()/1000) : '';
      }
      await customersAccounts(params).then((res) => {
        if (res.status == 200) {
          data.list = []
          data.list = res.data;
          total.value = res.totalSize;
        }
      });
    };
    const handleSizeChange = (val) => {
      pageSize.value = val;
      init();
    };
    const handleCurrentChange = (val) => {
      currentPage.value = val;
      init();
    };
    const search = () => {
      init();
    };

    const reset = () => {
      nameOrmobile.value = "";
      lineTime.value = "";
      init();
    };
    const handleExportUserInfo = async () => {
      let params = {
        nameOrMobile: nameOrmobile.value,
        startTime: '',
        endTime: '',
        sortField: sortField.value,
        isAsc: isAsc.value
      };
      if(type.value == 0){
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value).getTime()/1000) : '';
        params.endTime = lineTime.value ? Math.floor(new Date(lineTime.value.slice(0,10) + ' 23:59:59').getTime()/1000) : '';
      }
      if(type.value == 1){
        const date = lineTime.value ? new Date(lineTime.value) : '';
        if(date){
          date.setMonth(date.getMonth() + 1);
          date.setDate(0);
          const lastDay = date.toLocaleDateString().split('/').join('-');
          params.endTime = lastDay ? Math.floor(new Date(lastDay.slice(0,5) + '0' + lastDay.slice(5) + ' 23:59:59').getTime()/1000) : '';
        }
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value + '-01 00:00:00').getTime()/1000) : '';
      }
      if(type.value == 2){
        params.startTime = lineTime.value ? Math.floor(new Date(lineTime.value[0]).getTime()/1000) : '';
        params.endTime = lineTime.value ? Math.floor(new Date(lineTime.value[1]).getTime()/1000) : '';
      }
      customersExport(params).then((res) => {
        if (res.status == 200) {
          let name = `导出数据.csv`;
          let link = document.createElement("a");
          link.href = res.data;
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
      });
    };
    const handleChange = () => {
      lineTime.value = null;
    }
    const sortChange = (item) => {
      sortField.value = item.prop;
      isAsc.value = item.order == "ascending" ? true : false;
      init()
    }
    return {
      currentPage,
      pageSize,
      total,
      nameOrmobile,
      data,
      currentChose,
      currentFile,
      userImportFailList,
      treeRef,
      type,
      typeList,
      lineTime,
      init,
      handleSizeChange,
      handleCurrentChange,
      search,
      reset,
      handleExportUserInfo,
      handleChange,
      disabledDate,
      sortChange
    };
  },
};
</script>

<style scoped lang="scss">
.yxd_users {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .edit-main {
    .col {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30px;
      .el-input {
        width: 30%;
      }
      span {
        margin-top: 10px;
      }
      .upload {
        display: flex;
        flex-direction: column;
        margin-left: 20px;
        span {
          width: 50px;
          white-space: nowrap;
          text-align: right;
          margin-top: 20px;
          cursor: pointer;
          color: #169bd5;
        }
      }
    }
  }
  ::v-deep .el-dialog__footer {
    text-align: center;
  }
}
</style>
