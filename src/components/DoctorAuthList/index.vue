<template>
  <div class="yxd_users">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="realName" placeholder="请输入姓名"></el-input>
          </div>
          <div class="col">
            <el-input v-model="mobile" placeholder="请输入手机号码"></el-input>
          </div>

        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="table">
        <ms-table
          :columns="data.columns"
          :data="data.list"
          ref="tableRef"
        ></ms-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <el-dialog title="审核提示" v-model="approveVisible" width="50%">
      <div style="margin-bottom: 20px">
        <div style="margin-bottom: 10px; font-weight: bold;">{{ currentChose.realName }} 的认证信息：</div>
        <div v-if="currentChose.authenticationFile" class="auth-preview">
          <img
            v-if="isImage(currentChose.authenticationFile)"
            :src="currentChose.authenticationFile"
            class="auth-thumbnail"
            @click="handlePreview(currentChose)"
          />
          <div v-else class="file-preview" @click="openAuthFile">
            <i class="el-icon-document" style="font-size: 48px; color: #409eff;"></i>
            <div style="margin-top: 10px; color: #409eff;">点击查看文件</div>
          </div>
        </div>
        <div v-else class="no-auth-file">
          <i class="el-icon-warning" style="font-size: 48px; color: #e6a23c;"></i>
          <div style="margin-top: 10px; color: #e6a23c;">暂无认证文件</div>
        </div>
      </div>
      <el-radio-group v-model="approveStatus">
        <el-radio :label="2">通过审核</el-radio>
        <el-radio :label="3">去除审核</el-radio>
      </el-radio-group>
      <div v-if="approveStatus === 3" style="margin-top: 15px;">
        <el-input
          v-model="authenticationApprovalReason"
          type="textarea"
          placeholder="请输入审核不通过理由"
          :rows="3"
        ></el-input>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveVisible = false" size="mini">取 消</el-button>
          <el-button type="primary" @click="saveApproveEdit" size="mini">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog title="认证附件预览" v-model="previewVisible" width="60%">
      <div v-if="previewFile">
        <img v-if="isImage(previewFile)" :src="previewFile" style="width: 100%; height: auto;" />
        <div v-else style="text-align: center; padding: 20px;">
          <p>无法预览此文件类型</p>
          <p style="margin-bottom: 10px">{{previewFile}}</p>
          <el-button type="primary" @click="openFile">打开文件</el-button>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getUserList,
  approveUser
} from '@/request/service.js'
import initScroll from '@/mixins/initScroll.js'
import { ElMessage } from 'element-plus'
import { resolveComponent } from 'vue'

export default {
  name: 'doctorAuthList',
  mixins: [initScroll],
  emits: ['add', 'edit'],
  components: {},
  data() {
    return {
      realName: '',
      mobile: '',

      approveVisible: false,
      approveStatus: 2,
      authenticationApprovalReason: '',
      currentChose: {},
      previewVisible: false,
      previewFile: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      data: {
        columns: [
          {
            label: '序号',
            prop: 'accountId',
            width: 80,
          },
          {
            label: '真实姓名',
            prop: 'realName'
          },
          {
            label: '用户名',
            prop: 'userName'
          },
          {
            label: '手机号码',
            prop: 'mobile'
          },
          {
            label: '单位',
            prop: 'companyName',
            width: 220,
          },
          {
            label: '认证状态',
            prop: 'authenticationStatus',
            render: (h, scope) => {
              return h('span', {
                style: {
                  color: scope.row.authenticationStatus == 2 ? '#67C23A' : '#F56C6C'
                }
              }, scope.row.authenticationStatus == 1 ? '待审核' : scope.row.authenticationStatus == 2?'审核通过':scope.row.authenticationStatus == 3?'审核不通过':'未上传')
            }
          },
          {
            label: '认证附件',
            render: (h, scope) => {
              const msHandle = resolveComponent('ms-handle')
              const items = [
                { label: '预览', func: { func: 'preview', uuid: scope }, show: scope.row.authenticationFile?true:false, type:"primary" },
              ]
              return h(msHandle, {
                items,
                onPreview: () => {
                  this.handlePreview(scope.row)
                }
              })
            }
          },
          {
            label: '操作',
            render: (h, scope) => {
              const msHandle = resolveComponent('ms-handle')
              const items = [
                { label: '审核', func: { func: 'approve', uuid: scope }, show: scope.row.authenticationStatus==1||scope.row.authenticationStatus==3?true:false, type:"success" },
                { label: '去审', func: { func: 'approve', uuid: scope }, show: scope.row.authenticationStatus==2?true:false, type:"warning" },
              ]
              return h(msHandle, {
                items,
                onApprove: () => {
                  this.handleApprove(scope.row)
                }
              })
            }
          }
        ],
        list: []
      }
    }
  },
  mounted() {
    this.gitListData()
  },
  methods: {

    gitListData() {
      let params = {
        authenticationStatus: [1,2,3],
        accountType: 0,
        realName: this.realName,
        mobile: this.mobile,
        pageIndex: this.currentPage,
        pageSize: this.pageSize
      }
      getUserList(params).then((res) => {
        if (res.status == 200) {
          this.data.list = res.data || []
          this.total = res.totalSize || 0
        }
      })
    },
    search() {
      this.currentPage = 1
      this.gitListData()
    },
    reset() {
      this.name = ''
      this.mobile = ''
      this.authStatus = ''
      this.currentPage = 1
      this.gitListData()
    },

    handleApprove(row) {
      this.currentChose = row
      this.approveStatus = row.authenticationStatus === 2 ? 3 : 2
      this.authenticationApprovalReason = ''
      this.approveVisible = true
    },
    saveApproveEdit() {
      let data = {
        id: this.currentChose.id,
        authenticationStatus: this.approveStatus
      }
      // 如果是去除审核（审核不通过），需要传递理由
      if (this.approveStatus === 3) {
        if (!this.authenticationApprovalReason.trim()) {
          ElMessage.warning('请输入审核不通过理由')
          return
        }
        data.authenticationApprovalReason = this.authenticationApprovalReason
      }
      approveUser(data).then((res) => {
        if (res.status == 200) {
          ElMessage.success('操作成功')
          this.gitListData()
          this.approveVisible = false
        } else {
          ElMessage.error('审核失败')
          this.approveVisible = false
        }
      })
    },
    handlePreview(row) {
      this.previewFile = row.authenticationFile
      this.previewVisible = true
    },
    isImage(url) {
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url)
    },
    openFile() {
      window.open(this.previewFile, '_blank')
    },
    openAuthFile() {
      window.open(this.currentChose.authenticationFile, '_blank')
    },


    handleSizeChange(val) {
      this.pageSize = val
      this.gitListData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.gitListData()
    },

  }
}
</script>

<style scoped lang="scss">
.yxd_users {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .left {
      display: flex;
      gap: 15px;
      
      .col {
        display: flex;
        align-items: center;
        
        .tl {
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
    
    .right {
      display: flex;
      gap: 10px;
    }
  }
  
  .table {
    margin-bottom: 20px;
  }
  
  .pagination {
    text-align: right;
  }

  .auth-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background-color: #f5f7fa;

    .auth-thumbnail {
      height: 200px;
      width: auto;
      max-width: 100%;
      cursor: pointer;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.02);
      }
    }

    .file-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: #ecf5ff;
        border-radius: 4px;
        padding: 20px;
      }
    }
  }

  .no-auth-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    border: 1px dashed #e6a23c;
    border-radius: 4px;
    background-color: #fdf6ec;
  }
}
</style>
