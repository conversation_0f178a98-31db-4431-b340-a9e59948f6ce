<template>
  <div class="yxd_employee-detail">
    <div class="contain">
      <div class="header">
        <div class="row header_first">
          <div class="col">
            <el-date-picker
              v-model="lineTime"
              type="daterange"
              range-separator="至"
              start-placeholder="统计开始时间"
              end-placeholder="统计结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
            >
            </el-date-picker>
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <!-- 筛选权限 -->
          <div class="col" v-if="true">
            <el-cascader
              :options="roleIds"
              ref="cascader"
              collapse-tags
              placeholder="请选择权限"
              @change="change"
              :props="{
                checkStrictly: true,
                value: 'id',
                label: 'title',
                children: 'childRoles',
                multiple: true,
              }"
              clearable
            >
            </el-cascader>
          </div>
          <div class="col">
            <el-select
              v-model="professionalCatIds"
              clearable
              multiple
              placeholder="请选择身份"
              :disabled="accountTypeValue==1"
            >
              <el-option
                v-for="item in classGroup"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="btn">
            <el-button type="primary" @click="downloads">导出</el-button>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <el-input
              v-model="userName"
              placeholder="请输入姓名"
              clearable
              style="width: 350px"
            ></el-input>
          </div>
          <div class="col">
            <el-input
              v-model="mobile"
              clearable
              placeholder="请输入手机号码"
              style="width: 508px"
            ></el-input>
          </div>
          <div class="col">
            <el-button @click="search" icon="el-icon-search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table :data="tableData">
          <el-table-column width="330" fixed>
            <el-table-column align="center" prop="userName" width="200" label="用户名">
            </el-table-column>
            <el-table-column align="center" prop="realName" width="130" label="真实姓名">
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="用户信息">
            <el-table-column align="center" width="130" label="城市">
              <template #default="scope">
                {{ scope.row.cityName }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="130" label="单位">
              <template #default="scope">
                {{ scope.row.companyName }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="130" label="科室">
              <template #default="scope">
                {{ scope.row.departmentName }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="130" label="职称">
              <template #default="scope">
                {{ scope.row.professionalName }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="130" label="用户组">
              <template #default="scope">
                {{ scope.row.roles }}
              </template>
            </el-table-column>
            <el-table-column align="center" width="130" label="">
              <template #header>
              <span>管理关系</span>
              <el-tooltip class="item" effect="dark" placement="top">
                <i
                  class="el-icon-question"
                  style="font-size: 14px; vertical-align: middle"
                ></i>
                <template #content>
                  <div>
                    <p>若该用户扫描他人二维码注册本项目，则视为两人存在管理关系</p>
                  </div>
                </template>
              </el-tooltip>
            </template>
              <template #default="scope">
                {{ scope.row.inviterName }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="梅花余额">
              <template #default="scope">
                {{ scope.row.integral }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="视频课程">
            <el-table-column align="center" label="学习课程数量">
              <template #default="scope">
                {{ scope.row.courseStatistic.studyCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="学完课程数量">
              <template #default="scope">
                {{ scope.row.courseStatistic.completeCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="累计学习时长（分钟）">
              <template #default="scope">
                {{ scope.row.courseStatistic.cumulativeTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="课均时长（分钟）">
              <template #default="scope">
                {{ scope.row.courseStatistic.averageTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="转发次数">
              <template #default="scope">
                {{ scope.row.courseStatistic.shares }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="收藏数">
              <template #default="scope">
                {{ scope.row.courseStatistic.collections }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="评论数">
              <template #default="scope">
                {{ scope.row.courseStatistic.comments }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="资讯">
            <el-table-column align="center" label="查看数量">
              <template #default="scope">
                {{ scope.row.articleStatistic.viewCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="累计查看时长（分钟）">
              <template #default="scope">
                {{ scope.row.articleStatistic.cumulativeTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="平均停留时长（分钟）">
              <template #default="scope">
                {{ scope.row.articleStatistic.averageTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="读完率">
              <template #default="scope">
                {{ scope.row.articleStatistic.progress }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="点赞量">
              <template #default="scope">
                {{ scope.row.articleStatistic.likes }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="转发次数">
              <template #default="scope">
                {{ scope.row.articleStatistic.shares }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="收藏数量">
              <template #default="scope">
                {{ scope.row.articleStatistic.collections }}
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column align="center" label="评论数量">
            <template #default="scope">
              {{ scope.row.articleStatistic.comments }}
            </template>
          </el-table-column>

          <el-table-column align="center" label="直播">
            <el-table-column align="center" label="观看场次数量">
              <template #default="scope">
                {{ scope.row.liveStatistic.viewCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="观看时长（分钟）">
              <template #default="scope">
                {{ scope.row.liveStatistic.cumulativeTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="平均观看时长（分钟）">
              <template #default="scope">
                {{ scope.row.liveStatistic.averageTime }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="转发次数">
              <template #default="scope">
                {{ scope.row.liveStatistic.shares }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="指南">
            <el-table-column align="center" label="查看数量">
              <template #default="scope">
                {{ scope.row.guiderStatistic.viewCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="下载次数">
              <template #default="scope">
                {{ scope.row.guiderStatistic.downloadCount }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="转发次数">
              <template #default="scope">
                {{ scope.row.guiderStatistic.shares }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import { ElMessage } from "element-plus";
import {
  query,
  professionalCategories,
  analysisDetailList,
  getYxdProjectDetailByOrigin,
  getYxdProjectDetail
} from "@/request/service.js";
import { analysisDetailExport } from "@/request/download.js";
export default {
  name: "employee-detail",
  mixins: [initAccountTypeList],
  data() {
    return {
      lineTime: [],
      department: "",
      professionalCatIds: "",
      userName: "",
      mobile: "",
      roleIds: [],
      classGroup: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      selectvalue: [],
      tableData:[],
      downName:""
    };
  },
  mounted() {
    this.init();
    this.getProfessional();
  },
  methods: {
    // 导出
    downloads() {
      let params = {
        professionalCatIds: this.professionalCatIds,
        roleIds: this.selectvalue,
        startTime: this.lineTime[0],
        endTime: this.lineTime[1],
        userName: this.userName,
        mobile: this.mobile,
      };
      getYxdProjectDetailByOrigin().then((res) => {
        let params = {
          id: res.data.id,
          projectCode: res.data.projectCode,
        };
        getYxdProjectDetail(params).then((item) => {
          if (item.status == 200) {
            this.downName = item.data.name;
          }
        });
      });
      analysisDetailExport(params).then((res) => {
          if (res.size == 51) {
            ElMessage.warning("暂无数据导出");
          } else if (res.size == 63) {
            ElMessage.warning("服务错误，请稍后再试......");
          } else {
            let name = `主要模块的用户明细数据_${this.downName}.xlsx`
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(res);
            link.download = name
            link.click();
          }
      });
    },
    disabledDate(time) {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 确保比较时时间部分为0
      return time.getTime() >= today.getTime();
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    //  点击权限多选框选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.selectvalue = [];
      nodesObj.forEach((item) => this.selectvalue.push(item.data.id));
    },
    handleChange(val) {
      this.professionalCatIds = [];
      if (val === 0 || val === 1) {
        this.getPowerData(val);
      } else if (val === "") {
        this.roleIds = [];
      }
    },
    // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.roleIds = this.getList(res.data);
        }
      });
    },
    // 获取身份下拉
    getProfessional() {
      professionalCategories().then((res) => {
        if (res.status == 200) {
          this.classGroup = res.data;
        }
      });
    },
    init() {
      let params = {
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        professionalCatIds: this.professionalCatIds,
        roleIds: this.selectvalue,
        startTime: this.lineTime[0],
        endTime: this.lineTime[1],
        userName: this.userName,
        mobile: this.mobile,
      };
      analysisDetailList(params).then((res) => {
        this.tableData = res.data;
        this.total = res.totalSize;
      });
    },
    async search() {
      let params = {
        pageIndex: 1,
        pageSize: this.pageSize,
        professionalCatIds: this.professionalCatIds,
        roleIds: this.selectvalue,
        startTime: this.lineTime?this.lineTime[0]:'',
        endTime: this.lineTime?this.lineTime[1]:'',
        userName: this.userName,
        mobile: this.mobile,
      };
     let res = await analysisDetailList(params)
     this.tableData=[]
        this.tableData.push(...res.data); 
        console.log(this.tableData)
        this.total = res.totalSize;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.init();
    },
    reset() {
        this.professionalCatIds= []
        this.lineTime= []
        this.userName= ""
        this.mobile= ""
        this.accountTypeValue = ""
        this.selectvalue=[]
        this.roleIds=[]
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_employee-detail {
  .contain {
    .header {
      padding: 28px 30px 36px 30px;
      .header_first {
        position: relative;
        .btn {
          position: absolute;
          right: 0;
        }
      }
      .row {
        display: flex;
        margin-bottom: 22px;
        .col {
          display: flex;
          align-items: center;
          margin-right: 40px;
          .tl {
            min-width: 70px;
            text-align: right;
            white-space: nowrap;
            margin-right: 12px;
            height: 22px;
            opacity: 1;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 22px;
          }
        }
      }
    }
    .table {
      padding: 28px 30px 36px 30px;
      background: #fff;
      margin-bottom: 24px;
    }
  }
}
</style>
