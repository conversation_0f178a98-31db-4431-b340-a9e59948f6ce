<template>
  <div class="yxd_employee-overview">
    <div class="contain">
      <!-- <div class="header">
        <div class="col">
          <el-date-picker
            v-model="lineTime"
            type="daterange"
            range-separator="至"
            start-placeholder="统计开始时间"
            end-placeholder="统计结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="shortcuts"
          >
          </el-date-picker>
        </div>
        <div class="col">
          <el-select v-model="department" placeholder="请选择科室/部门">
            <el-option
              v-for="item in classGroup"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div>
        <div class="col">
          <span class="tl"></span>
          <el-select v-model="identity" placeholder="请选择身份">
            <el-option
              v-for="item in classGroup"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div>
        <div class="col">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div> -->
      <div class="duration duration_box">
        <div v-if="ischecked" style="width: 100%">
          <div class="duration_top">
            <span class="title">专区用户科室分布</span>
            <el-button type="primary" @click="subPng(1)">保存</el-button>
          </div>
          <div class="main">
            <div class="btn d_item">
              <span id="no">
                <el-button :type="active==1?'primary':'default'" @click="checkEcharts('pie', 1)">饼图</el-button>
                <el-button :type="active==2?'primary':'default'" @click="checkEcharts('pie1', 2)">圆环</el-button>
                <el-button :type="active==3?'primary':'default'" @click="checkEcharts('bar', 3)">柱图</el-button>
                <el-button :type="active==4?'primary':'default'" @click="checkEcharts('shadow', 4)">条形</el-button>
                <el-button :type="active==5?'primary':'default'" @click="checkEcharts('category', 5)">折线</el-button>
              </span>
            </div>
          </div>
          <div style="border: solid 1px #eceef5; width: 100%">
            <div
              id="linesChart1"
              style="width: 100%; height: 400px; margin: 20px auto 0"
              class="d_item"
            ></div>
          </div>
        </div>

        <div
          class="btn1 d_item"
          v-if="
            activeList &&
            activeList.length > 0 &&
            activeList.indexOf(index + 1) != -1 &&
            index < 10
          "
        >
          <el-button id="no" @click="subPng(1)" v-if="index < 10"
            >保存</el-button
          >
        </div>
      </div>
      <!-- <div class="duration">
        <span class="title">学习时长</span>
        <div class="main">
          <div class="item first">
            <span class="t1">总学习时长</span>
            <span class="t2"
              >{{ studyObj.total }} <span class="t3">min</span></span
            >
          </div>
          <div class="item">
            <span class="t1">人均学习时长</span>
            <span class="t2"
              >{{ studyObj.average }} <span class="t3">min</span></span
            >
          </div>
        </div>
      </div> -->

      <!-- <div class="interaction">
        <span class="title">交流互动</span>
        <div class="main">
          <div class="table">
            <el-table :data="courseData">
              <el-table-column
                prop="course"
                align="center"
                label="课程"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="collect"
                align="center"
                label="收藏"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="spot"
                align="center"
                label="点赞"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="comment"
                align="center"
                label="评论"
                width="120"
              >
              </el-table-column>
            </el-table>
          </div>
          <div class="table">
            <el-table :data="articleData">
              <el-table-column
                prop="article"
                align="center"
                label="课程"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="collect"
                align="center"
                label="收藏"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="spot"
                align="center"
                label="点赞"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="comment"
                align="center"
                label="评论"
                width="120"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div> -->
      <div class="statistics">
        <div class="top">
          <h3 >登录统计</h3>
          <div class="right">
            <div class="item">
              <span>统计周期</span>
              <el-date-picker
                v-model="date"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="YYYY-MM-DD"
                @change="change"
              >
              </el-date-picker>
            </div>
            <div class="item">
              <span>单位</span>
              <el-select v-model="unit" @change="select" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="main">
          <div id="linesChart" style="width:100%;height:530px;"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import html2canvas from "html2canvas";
import dayjs from "dayjs";
import { userDepartmentStatistic, perfectInfo, getUserUvByDateType } from "@/request/service.js";
export default {
  name: "employee-overview",
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      lineTime: "",
      department: "",
      identity: "",
      ischecked: false,
      classGroup: [],
      myChart:"",
      date:"",
      shortcuts: [
        {
          text: "最近一周",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          },
        },
        {
          text: "最近一个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          },
        },
        {
          text: "最近三个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
          },
        },
      ],
      unit:1,
      options: [
        {
          value: 1,
          label: "天",
        },
        {
          value: 2,
          label: "周",
        },
        {
          value: 3,
          label: "月",
        },
        {
          value: 4,
          label: "季度",
        },
      ],
      studyObj: {
        total: "153434",
        average: "453",
      },
      courseData: [
        {
          course: "总数",
          collect: 20,
          spot: 454,
          comment: 34,
        },
        {
          course: "人均",
          collect: 20,
          spot: 454,
          comment: 34,
        },
      ],
      articleData: [
        {
          article: "总数",
          collect: 20,
          spot: 454,
          comment: 34,
        },
        {
          article: "人均",
          collect: 20,
          spot: 454,
          comment: 34,
        },
      ],
      activeList: [],
      type: "",
      active: null,
      tableData: [],
      lineOptions1: {},
      lineOptions: {
        xAxis: {
          name: "",
          type: "category",
          data: [],
        },
        yAxis: {
          name: "登录人数",
          type: "value",
          axisLine: { show: true },
          minInterval: 1,
        },
        series: [
          {
            data: [],
            type: "line",
          },
        ],
      },
    };
  },
  watch: {
    lineOptions1: {
      handler(val) {
        this.$nextTick(() => {
          this.destoryEcharts();
          var myChart = echarts.init(
            document.getElementById(`linesChart1`),
            null,
            {
              renderer: "svg",
            }
          );
          myChart.setOption(this.lineOptions1);
          window.onresize = function () {
            myChart.resize();
          };
        });
      },
      deep: true
    },
  },
  async mounted() {
    this.ischecked = false
   await this.init();
    this.initDate1();
    setTimeout(() => {
      this.checkEcharts("pie", 1);
    }, 1000);
    this.initLine1();
  },
  computed: {
    minDate() {
      return this.$store.state.projectInfo.startTime;
    },
  },
  methods: {
    initDate1() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      // 设置默认时间
      this.date = [
        dayjs(start).format("YYYY-MM-DD"),
        dayjs(end).format("YYYY-MM-DD"),
      ];
    },
    change($event) {
      let pre = $event[0];
      const flag = dayjs(pre).isBefore(dayjs(this.minDate));
      // 选择的时间在 最早的日期 项目启动的时间 之前
      if (flag) {
        ElMessage.warning("在项目启动的时间之前的日期无法选择");
        this.initDate1();
      }
      this.date = $event;
      this.initLine1();
    },
    textFn(val) {
      let cur;
      switch (val) {
        case 1:
          cur = "天";
          break;
        case 2:
          cur = "周";
          break;
        case 3:
          cur = "月";
          break;
        case 4:
          cur = "季度";
          break;
      }
      return cur;
    },
    initLine1() {
      this.$nextTick(() => {
        this.destoryEcharts1();
        this.myChart = echarts.init(
          document.getElementById("linesChart"),
          null,
          { renderer: "svg" }
        );
        let params = {
          dateType: this.unit,
          endTime: this.date[1],
          startTime: this.date[0],
        };
        this.lineOptions.xAxis.data = [];
        this.lineOptions.series[0].data = [];
        getUserUvByDateType(params).then((res) => {
          if (res.status == 200) {
            this.lineOptions.xAxis.name = `日期（${this.textFn(
              res.data[0].dateType
            )}）`;
            res.data.forEach((d) => {
              this.lineOptions.xAxis.data.push(d.dateTime);
              this.lineOptions.series[0].data.push(d.count);
            });
            this.myChart.setOption(this.lineOptions);
          }
        });
      });
    },
    select($event) {
      this.unit = $event;
      this.initLine1();
    },
    subPng(index) {
      // 获取要保存为 PNG 的 DOM 元素
      const element = document.getElementById(`linesChart1`);
      // 将该元素转换为 Canvas 对象并导出为 PNG 图片
      html2canvas(element).then((canvas) => {
        const pngUrl = canvas.toDataURL("image/png");
        const downloadLink = document.createElement("a");
        downloadLink.href = pngUrl;
        downloadLink.download = "image.png";
        downloadLink.click();
      });
    },
    // 查询统计详情
    async init() {
      let res =await perfectInfo(0)
      this.ischecked = res.data.checked;
    },
    //   销毁
    destoryEcharts() {
      var myChart = echarts.init(document.getElementById(`linesChart1`), null, {
        renderer: "svg",
      });
      myChart.dispose();
    },
    destoryEcharts1() {
      this.myChart = echarts.init(document.getElementById(`linesChart`), null, {
        renderer: "svg",
      });
      this.myChart.dispose();
    },
    // 显示echarts
    async checkEcharts(val, index) {
      this.echartsList = []
      this.active = index;
      this.activeList.push(index);
      this.type = val;
      let res = await userDepartmentStatistic();
      if (res.status == 200) {
        this.tableData = res.data;
      }
      this.destoryEcharts();
      this.echartsListTitle = this.tableData.map((item) => {
        return item.departmentName ? item.departmentName : "未选择科室";
      });
      this.echartsListValue = this.tableData.map((item) => {
        return item.percentage;
      });
      this.echartsList = this.tableData.map((item) => {
        return {
          name: item.departmentName ? item.departmentName : "未选择科室",
          value: item.percentage,
        };
      });
      switch (val) {
        case "pie":
          this.lineOptions1 = {
            tooltip: {
              trigger: "item",
            },
            legend: {
              left: "center",
              top: "bottom",
            },
            series: [
              {
                name: "",
                type: "pie",
                center: ["50%", "35%"], // 调整饼图位置
                radius: "70%",
                data: this.echartsList,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: (params) => {
                      if (params.data.value) {
                        return `${params.data.value}%`;
                      } else {
                        return "";
                      }
                    }, // 格式化数值百分比输出
                  },
                },
                labelLine: {
                  normal: {
                    position: "inner",
                    show: false,
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                  },
                },
              },
            ],
          };
          break;
        case "pie1":
          this.lineOptions1 = {
            tooltip: {
              trigger: "item",
            },
            legend: {
              top: "bottom",
              left: "center",
            },
            series: [
              {
                name: "",
                type: "pie",
                radius: ["50%", "65%"],
                center: ["50%", "35%"], // 调整饼图位置
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: "#fff",
                  borderWidth: 2,
                },
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: (params) => {
                      if (params.data.value) {
                        return `${params.data.value}%`;
                      } else {
                        return "";
                      }
                    }, // 格式化数值百分比输出
                  },
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: "bold",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: this.echartsList,
              },
            ],
          };
          break;
        case "bar":
          this.lineOptions1 = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            dataZoom: [
              {
                // 设置滚动条的隐藏与显示
                show: true,
                // 设置滚动条类型
                type: "slider",
                // 设置背景颜色
                backgroundColor: "rgb(19, 63, 100)",
                // 设置选中范围的填充颜色
                fillerColor: "rgb(16, 171, 198)",
                // 设置边框颜色
                borderColor: "rgb(19, 63, 100)",
                // 是否显示detail，即拖拽时候显示详细数值信息
                showDetail: false,
                // 数据窗口范围的起始数值
                startValue: 0,
                // 数据窗口范围的结束数值（一页显示多少条数据）
                endValue: 5,
                // empty：当前数据窗口外的数据，被设置为空。
                // 即不会影响其他轴的数据范围
                filterMode: "empty",
                // 设置滚动条宽度，相对于盒子宽度
                width: "80%",
                // 设置滚动条高度
                height: 0,
                // 设置滚动条显示位置
                left: "center",
                // 是否锁定选择区域（或叫做数据窗口）的大小
                zoomLoxk: true,
                // 控制手柄的尺寸
                handleSize: 0,
                // dataZoom-slider组件离容器下侧的距离
                bottom: 3,
              },
              {
                // 没有下面这块的话，只能拖动滚动条，
                // 鼠标滚轮在区域内不能控制外部滚动条
                type: "inside",
                // 滚轮是否触发缩放
                zoomOnMouseWheel: false,
                // 鼠标滚轮触发滚动
                moveOnMouseMove: true,
                moveOnMouseWheel: true,
              },
            ],

            xAxis: {
              type: "category",
              data: this.echartsListTitle,
              axisLabel: {
                interval: 0,
                formatter: (params) => {
                  //调用自定义显示格式
                  return this.getEqualNewlineString(params, 8);
                },
              },
            },
            yAxis: {
              type: "value",
              axisLabel: {
                formatter: "{value}%", // 这里设置x轴的单位
              },
            },
            series: [
              {
                data: this.echartsListValue,
                type: "bar",
                barWidth: 40,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: "{c}%", // 格式化数值百分比输出
                  },
                },
              },
            ],
          };
          break;
        case "shadow":
          this.lineOptions1 = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            legend: {},
            dataZoom: [
              {
                // 设置滚动条的隐藏或显示
                show: true,
                // 设置类型
                type: "slider",
                // 设置背景颜色
                backgroundColor: "rgb(19, 63, 100)",
                // 设置选中范围的填充颜色
                fillerColor: "rgb(16, 171, 198)",
                // 设置边框颜色
                borderColor: "rgb(19, 63, 100)",
                // 是否显示detail，即拖拽时候显示详细数值信息
                showDetail: false,
                // 数据窗口范围的起始数值
                startValue: 0,
                // 数据窗口范围的结束数值（一页显示多少条数据）
                endValue: 5,
                // 控制哪个轴，如果是number表示控制一个轴，
                // 如果是Array表示控制多个轴。此处控制第二根轴
                yAxisIndex: [0, 1],
                // empty：当前数据窗口外的数据，被设置为空。
                // 即不会影响其他轴的数据范围
                filterMode: "empty",
                // 滚动条高度
                width: 0,
                // 滚动条显示位置
                height: "80%",
                // 距离右边
                left: 0,
                // 控制手柄的尺寸
                handleSize: 0,
                // 是否锁定选择区域（或叫做数据窗口）的大小
                zoomLoxk: true,
                // 组件离容器上侧的距离
                // 如果top的值为'top', 'middle', 'bottom'，组件会根据相应的位置自动对齐
                top: "middle",
              },
              {
                // 没有下面这块的话，只能拖动滚动条，
                // 鼠标滚轮在区域内不能控制外部滚动条
                type: "inside",
                // 控制哪个轴，如果是number表示控制一个轴，
                // 如果是Array表示控制多个轴。此处控制第二根轴
                yAxisIndex: [0, 1],
                // 滚轮是否触发缩放
                zoomOnMouseWheel: false,
                // 鼠标移动能否触发平移
                moveOnMouseMove: true,
                // 鼠标滚轮能否触发平移
                moveOnMouseWheel: true,
              },
            ],
            grid: {
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true,
            },

            xAxis: {
              type: "value",
              boundaryGap: [0, 0.01],
              axisLabel: {
                formatter: "{value}%", // 这里设置x轴的单位
              },
            },
            yAxis: {
              type: "category",
              data: this.echartsListTitle,
            },
            series: [
              {
                type: "bar",
                data: this.echartsListValue,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: "{c}%", // 格式化数值百分比输出
                  },
                },
              },
            ],
          };
          break;
        case "category":
          this.lineOptions1 = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            dataZoom: [
              {
                // 设置滚动条的隐藏与显示
                show: true,
                // 设置滚动条类型
                type: "slider",
                // 设置背景颜色
                backgroundColor: "rgb(19, 63, 100)",
                // 设置选中范围的填充颜色
                fillerColor: "rgb(16, 171, 198)",
                // 设置边框颜色
                borderColor: "rgb(19, 63, 100)",
                // 是否显示detail，即拖拽时候显示详细数值信息
                showDetail: false,
                // 数据窗口范围的起始数值
                startValue: 0,
                // 数据窗口范围的结束数值（一页显示多少条数据）
                endValue: 5,
                // empty：当前数据窗口外的数据，被设置为空。
                // 即不会影响其他轴的数据范围
                filterMode: "empty",
                // 设置滚动条宽度，相对于盒子宽度
                width: "80%",
                // 设置滚动条高度
                height: 0,
                // 设置滚动条显示位置
                left: "center",
                // 是否锁定选择区域（或叫做数据窗口）的大小
                zoomLoxk: true,
                // 控制手柄的尺寸
                handleSize: 0,
                // dataZoom-slider组件离容器下侧的距离
                bottom: 3,
              },
              {
                // 没有下面这块的话，只能拖动滚动条，
                // 鼠标滚轮在区域内不能控制外部滚动条
                type: "inside",
                // 滚轮是否触发缩放
                zoomOnMouseWheel: false,
                // 鼠标滚轮触发滚动
                moveOnMouseMove: true,
                moveOnMouseWheel: true,
              },
            ],
            xAxis: {
              type: "category",
              data: this.echartsListTitle,
              axisLabel: {
                interval: 0,
                formatter: (params) => {
                  //调用自定义显示格式
                  return this.getEqualNewlineString(params, 8);
                },
              },
            },
            yAxis: {
              type: "value",
              axisLabel: {
                formatter: "{value}%", // 这里设置x轴的单位
              },
            },
            series: [
              {
                data: this.echartsListValue,
                type: "line",
              },
            ],
          };
          break;
      }
      setTimeout(() => {
        // 获取要保存为 PNG 的 DOM 元素
        const element = document.getElementById(`linesChart${index}`);
        // 将该元素转换为 Canvas 对象并导出为 PNG 图片
        html2canvas(element).then((canvas) => {
          const pngUrl = canvas.toDataURL("image/png");
          this.tableData[index - 1].img = pngUrl;
          this.tableData[index - 1].isImg = true;
        });
      }, 1000);
    },
    getEqualNewlineString(params, length) {
      let text = "";
      let count = Math.ceil(params.length / length); // 向上取整数
      // 一行展示length个
      if (count > 1) {
        for (let z = 1; z <= count; z++) {
          text += params.substr((z - 1) * length, length);
          if (z < count) {
            text += "\n";
          }
        }
      } else {
        text += params.substr(0, length);
      }
      return text;
    },
    reset() {
      this.lineTime = "";
      this.department = "";
      this.identity = "";
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_employee-overview {
  .contain {
    .header {
      padding: 20px;
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 40px;
        .tl {
          white-space: nowrap;
          margin-right: 12px;
          height: 22px;
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
        }
      }
    }
    .duration_box {
      display: flex;
      background: #fff;
      margin-top: 12px;
      justify-content: space-between;
    }
    .duration {
      padding: 20px;
      background: #fff;
      margin-bottom: 12px;
      .duration_top {
        display: flex;
        justify-content: space-between;
      }
      .main {
        .btn {
          margin: 20px 0;
        }
      }
      .title {
        height: 24px;
        font-size: 18px;
        font-family: PingFangSC, PingFangSC-Medium;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
      }
      .main {
        display: flex;
        .item {
          display: flex;
          flex-direction: column;
          margin-top: 20px;
          width: 320px;
          .t1 {
            height: 28px;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 28px;
          }
          .t2 {
            height: 42px;
            opacity: 1;
            font-size: 30px;
            font-family: HelveticaNeue, HelveticaNeue-Medium;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            line-height: 42px;
          }
          .t3 {
            height: 22px;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 22px;
          }
        }
        .first {
          position: relative;
        }
        .first::after {
          position: absolute;
          content: "";
          width: 1px;
          height: 46px;
          top: 18px;
          right: 80px;
          background: #e9e9e9;
        }
      }
    }
    .interaction {
      padding: 20px;
      background: #fff;
      margin-top: 12px;
      margin-bottom: 12px;
      .title {
        height: 24px;
        font-size: 18px;
        font-family: PingFangSC, PingFangSC-Medium;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
      }
      .main {
        display: flex;
        justify-content: space-between;
        .table {
          display: flex;
          justify-content: center;
          margin-left: 2%;
          margin-right: 2%;
        }
      }
    }
    .statistics {
    padding: 20px;
    background: #fff;
    margin-top: 12px;
    margin-bottom: 12px;
    .title {
      height: 24px;
      font-size: 18px;
      font-family: PingFangSC, PingFangSC-Medium;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
    }
    .top {
      display: flex;
      justify-content: space-between;
      h3{
        font-size: 18px;
        font-weight: 400;
      }
    }
    .right {
      display: flex;
      justify-content: space-between;
      .item {
        margin-left: 20px;
        font-size: 14px;
        span {
          margin-right: 20px;
        }
      }
    }
  }
  }
}
</style>
