<template>
  <div class="containers">
    <el-dialog v-model="resultDialog" title="新建结果" width="30%">
      <el-form
        ref="form"
        :rules="formRules"
        :model="formData"
        label-width="100px"
      >
        <el-form-item label="结果名称">
          <el-input v-model="formData.name" prop="name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resultDialog = false">取消</el-button>
          <el-button type="primary" @click="resultSubmit('form')">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-form
      :model="form"
      label-width="140px"
      :rules="rules"
      ref="forms"
      v-if="form"
    >
      <!-- 考试信息 -->
      <div>
        <div class="title">考试信息</div>
        <el-form-item
          v-if="!search"
          :label="form.paperTitle ? '考试内容' : '选择试卷'"
          prop="paperId"
          style="margin-top: 30px"
        >
          <el-button
            type="primary"
            @click="choiceShiJuan"
            style="margin-right: 20px"
            >选择试卷</el-button
          ><span style="color: #66b1ff">{{ form.paperTitle }}</span>
        </el-form-item>
        <el-form-item
          v-else
          :label="form.paperTitle ? '考试内容' : '选择试卷'"
          prop="paperId"
          style="margin-top: 30px"
        >
          <div v-if="form.paperTitle" style="color: #66b1ff">
            {{ form.paperTitle }}
          </div>
        </el-form-item>

        <el-form-item label="考试名称" prop="title">
          <el-input
            v-model="form.title"
            :disabled="search"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="考试说明">
          <ms-editor
            class="editor"
            :class="[search ? 'disabledClass' : '']"
            v-model="form.summary"
          ></ms-editor>
          <div></div>
        </el-form-item>
      </div>

      <!-- 考试设置 -->
      <div>
        <div class="title">考试设置</div>

        <el-form-item label="考试时间" style="margin-top: 30px" prop="lineTime">
          <el-date-picker
            :disabled="search"
            v-model="form.lineTime"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="small"
            @blur="blurTime"
            @focus="focusTime"
          />
        </el-form-item>

        <el-form-item label="考试次数" prop="times">
          <el-input
            :disabled="search"
            v-model="form.times"
            placeholder="请输入大于0的整数"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="考试限时" prop="timeLimit" v-if="mode != 1">
          <el-input-number
            style="width: 300px"
            precision="0"
            placeholder="请输入<=1024的正整数"
            :disabled="search"
            v-model="form.timeLimit"
            @change="handleChange"
          />
          &nbsp;&nbsp; 分钟
        </el-form-item>
        <el-form-item label="通过分数" prop="passScore" v-if="mode != 1&&mode != 2">
          <el-input-number
            style="width: 300px"
            precision="0"
            placeholder="请输入＞0的整数"
            :disabled="search"
            v-model="form.passScore"
            @change="handleChange"
          />&nbsp;&nbsp; 分
        </el-form-item>
        <el-form-item
          label="考试范围"
          style="margin-bottom: 5px"
          prop="scope"
          v-if="mode != 1&&mode != 2"
        >
          <el-radio-group v-model="form.scope" class="ml-4" :disabled="search">
            <el-radio label="0" size="large">全部人员</el-radio>
            <el-radio label="1" size="large">部分人员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.scope == '1'">
          <el-button
            :disabled="search"
            v-if="
              initActive &&
              initActive.length == 0 &&
              form.userIds &&
              form.userIds.length == 0
            "
            type="primary"
            size="small"
            style="margin-left: 130px"
            @click="choice"
            >选择用户组</el-button
          >
          <div class="alreadyChoice" v-else>
            <span @click="choice" v-if="!search"> 已选择</span>
            <span v-else :disabled="true">已选{{ form.userCount }}人</span>
          </div>
        </el-form-item>
      </div>
      <!-- 显示设置 -->
      <div>
        <div class="title">显示设置</div>

        <el-form-item label="PC答题" style="margin-top: 30px" prop="pcView" v-if="mode != 2">
          <el-radio-group
            :disabled="search"
            v-model="form.pcView"
            style="vertical-align: text-top"
          >
            <div>
              <el-radio :label="1" style="margin-bottom: 20px"
                >一页一题<span class="gradio">点击按钮进入下一题</span>
              </el-radio>
            </div>
            <div>
              <el-radio :label="0"
                >整页模式<span class="gradio">一页显示所有题目</span></el-radio
              >
            </div>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="交卷后" v-if="mode != 1&&mode != 2" style="margin-top: 30px" >
          <el-checkbox-group
            v-model="form.checkList1"
            @change="changeCheckbox"
            :disabled="search"
          >
            <el-checkbox label="1">分数可见</el-checkbox>
            <el-checkbox label="2">对错可见</el-checkbox>
            <el-checkbox label="3">正确答案可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="考试结束后"
          v-if="mode != 1&&mode != 2"
          style="margin-top: 30px"
        >
          <el-checkbox-group
            v-model="form.checkList2"
            @change="changeCheckbox1"
            :disabled="search"
          >
            <el-checkbox label="1">分数可见</el-checkbox>
            <el-checkbox label="2">对错可见</el-checkbox>
            <el-checkbox label="3">正确答案可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="以题目为单位提交"
          style="margin-top: 30px"
          prop="analysisMode"
          v-if="mode==2"
        >
          <el-switch
            v-model="form.nextStatus"
            active-value="1"
            inactive-value="0"
            :disabled="search"
          />
          <span class="gradio"
            >每答一个题就提交，答错展示正确答案和解析</span
          >
        </el-form-item>
        <div :style="{marginBottom: mode == 2&&focusTimes?'250px':''}"></div>
      </div>
      <!-- 结果分析 -->
      <div class="fenxi" v-if="mode == 0 && form.paperTitle">
        <div class="title">结果分析</div>
        <el-form-item
          label="答题结果分析"
          style="margin-top: 30px"
          prop="analysisMode"
        >
          <el-switch
            v-model="form.analysisStatus"
            active-value="1"
            inactive-value="0"
            :disabled="search"
          />
          <span class="gradio"
            >打开时，用户提交考卷后，考试结果中会展示答题情况分析结果</span
          >
        </el-form-item>
        <el-form-item
          v-if="
            (form.analysisStatus == 1 && form.times != 1 && form.times !== 0) ||
            (form.analysisStatus == 1 && form.times == '')
          "
          label="多次答题分析"
          style="margin-top: 30px"
          prop="analysisMode"
        >
          <el-switch
            v-model="form.multipleAnalysisStatus"
            active-value="1"
            inactive-value="0"
          /><span class="gradio">当考试次数不限时，展示作答变化情况</span>
        </el-form-item>
        <el-form-item
          v-if="form.analysisStatus == 1"
          label="分析方式"
          style="margin-top: 30px"
          prop="analysisMode"
        >
          <el-radio-group
            :disabled="search"
            v-model="form.analysisMode"
            style="vertical-align: text-top"
            @change="changeRadio"
          >
            <div>
              <el-radio :label="0" style="margin-bottom: 20px"
                >根据答题得分<span class="gradio">用户本次考卷总分</span>
              </el-radio>
            </div>
            <div v-if="modeType != 2">
              <el-radio :label="1"
                >根据标签得分<span class="gradio"
                  >试卷内容结构得分</span
                ></el-radio
              >
            </div>
          </el-radio-group>
        </el-form-item>
        <!-- 得分 -->

        <div
          class="defen_table"
          v-if="form.analysisMode == 0 && form.analysisStatus == 1"
        >
          <div class="table_item">
            <div>答卷得分范围</div>
            <div>范围说明</div>
            <div>操作</div>
          </div>
          <div
            class="table_itema"
            v-for="(items, index) in form.weightSettings"
            :key="index"
          >
            <div class="jiexi_box">
              <span
                class="input_inner"
                style="display: flex; flex-direction: row"
              >
                <el-input
                  v-model="items.weightStart"
                  :disabled="search"
                  style="display: inline-block"
                  placeholder="输入分值权重起始值"
                ></el-input
                >~
                <el-input
                  v-model="items.weightEnd"
                  :disabled="search"
                  :key="index"
                  style="display: inline-block"
                  placeholder="输入分值权重最大值"
                ></el-input>
              </span>
            </div>
            <div>
              <el-input
                v-model="items.quota"
                :disabled="search"
                style="display: inline-block"
              ></el-input>
            </div>
            <span class="jiexi_Op">
              <div>
                <div>
                  <span
                    style="width: 20px !important"
                    :class="search ? 'noclick' : ''"
                    @click="addOp()"
                    >+</span
                  >
                  <span style="width: 20px !important">
                    <span
                      :class="search ? 'noclick' : ''"
                      v-if="index1 != 0"
                      @click="delOp(index)"
                      >-</span
                    >
                  </span>
                </div>
              </div>
            </span>
          </div>
        </div>
        <!-- 标签 -->
        <div v-if="form.analysisMode == 1 && form.analysisStatus == 1">
          <el-form-item style="margin-top: 30px">
            <div class="table">
              <div class="table_item">
                <div>标签</div>
                <div>关联的题目</div>
                <div>答卷得分范围</div>
                <div>解释说明</div>
                <div>操作</div>
              </div>
              <template style="display: block">
                <div
                  class="table_itema"
                  v-for="(items, index) in form.resultSettings"
                  :key="index"
                >
                  <div>{{ items.labelName }}</div>
                  <div style="word-break: break-all">
                    {{
                      items.relateQuestions && items.relateQuestions.length > 0
                        ? items.relateQuestions.join(",")
                        : []
                    }}
                  </div>
                  <div class="box">
                    <div
                      v-for="(item, index1) in items.weightSettings"
                      :key="index1"
                    >
                      <span
                        class="input_inner"
                        style="display: flex; flex-direction: row"
                      >
                        <el-input
                          v-model="item.weightStart"
                          :disabled="search"
                          style="display: inline-block"
                          placeholder="输入分值权重起始值"
                        ></el-input
                        >~
                        <el-input
                          v-model="item.weightEnd"
                          :disabled="search"
                          :key="index"
                          style="display: inline-block"
                          placeholder="输入分值权重最大值"
                        ></el-input>
                      </span>
                      <span>
                        <el-input
                          v-model="item.quota"
                          :disabled="search"
                          :key="index"
                          @input="inputchange"
                        ></el-input>
                      </span>
                      <span class="op">
                        <div>
                          <div>
                            <span
                              style="width: 20px !important"
                              :class="search ? 'noclick' : ''"
                              @click="addOps(index)"
                              >+</span
                            >
                            <span style="width: 20px !important">
                              <span
                                :class="search ? 'noclick' : ''"
                                v-if="index1 != 0"
                                @click="delOps(index, index1)"
                                >-</span
                              >
                            </span>
                          </div>
                        </div>
                      </span>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </el-form-item>
        </div>
      </div>
      <!-- 结果分析 -->
      <div v-if="mode == 1 && form.paperTitle" class="jieguo">
        <div class="title">结果分析</div>
        <el-form-item
          label="分析方式"
          style="margin-top: 30px"
          prop="analysisMode"
        >
          <el-radio-group
            :disabled="search"
            v-model="form.analysisMode"
            style="vertical-align: text-top"
            @change="changeRadio"
          >
            <div>
              <el-radio :label="0" style="margin-bottom: 20px"
                >根据测评得分<span class="gradio">用户做题结果总分</span>
              </el-radio>
            </div>
            <div>
              <el-radio :label="1"
                >根据标签得分<span class="gradio"
                  >试卷内容结构得分</span
                ></el-radio
              >
            </div>
          </el-radio-group>
          <!-- 列表 -->
          <!-- <el-table :data="tagsList" style="width: 100%">
            <el-table-column prop="date" label="标签" width="180" />
            <el-table-column prop="name" label="关联的题目" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />

          </el-table> -->
        </el-form-item>
        <div>
          <el-form-item style="margin-top: 30px">
            <div class="table">
              <div class="table_item">
                <div v-if="form.analysisMode">标签</div>
                <div v-if="form.analysisMode">关联的题目</div>
                <div>权重分层</div>
                <div>分层对应的指标</div>
                <div>操作</div>
              </div>
              <template v-if="form.analysisMode && form.resultSettings">
                <div
                  class="table_itema"
                  v-for="(items, index) in form.resultSettings"
                  :key="index"
                >
                  <div>{{ items.labelName }}</div>
                  <div style="word-break: break-all">
                    {{
                      items.relateQuestions && items.relateQuestions.length > 0
                        ? items.relateQuestions.join(",")
                        : []
                    }}
                  </div>
                  <div class="box">
                    <div
                      v-for="(item, index1) in items.weightSettings"
                      :key="index1"
                    >
                      <span
                        class="input_inner"
                        style="display: flex; flex-direction: row"
                      >
                        <el-input
                          v-model="item.weightStart"
                          :disabled="search"
                          style="display: inline-block"
                          placeholder="输入分值权重起始值"
                        ></el-input
                        >~
                        <el-input
                          v-model="item.weightEnd"
                          :disabled="search"
                          :key="index"
                          style="display: inline-block"
                          placeholder="输入分值权重最大值"
                        ></el-input>
                      </span>
                      <span>
                        <el-input
                          v-model="item.quota"
                          :disabled="search"
                          :key="index"
                          @input="inputchange"
                        ></el-input>
                      </span>
                      <span class="op">
                        <div>
                          <div>
                            <span
                              style="width: 20px !important"
                              :class="search ? 'noclick' : ''"
                              @click="addOps(index)"
                              >+</span
                            >
                            <span style="width: 20px !important">
                              <span
                                :class="search ? 'noclick' : ''"
                                v-if="index1 != 0"
                                @click="delOps(index, index1)"
                                >-</span
                              >
                            </span>
                          </div>
                        </div>
                      </span>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div
                  class="table_itema"
                  v-for="(items, index) in form.weightSettings"
                  :key="index"
                >
                  <div style="display: flex; flex-direction: row">
                    <el-input
                      v-model="items.weightStart"
                      :disabled="search"
                      style="display: inline-block"
                    ></el-input
                    >~
                    <el-input
                      v-model="items.weightEnd"
                      :disabled="search"
                      :key="index"
                      style="display: inline-block"
                    ></el-input>
                  </div>
                  <div>
                    <el-input
                      v-model="items.quota"
                      :disabled="search"
                      :key="index"
                    ></el-input>
                  </div>
                  <div class="op">
                    <div>
                      <div>
                        <span :class="search ? 'noclick' : ''" @click="addOp()"
                          >+</span
                        >
                        <span>
                          <span
                            :class="search ? 'noclick' : ''"
                            style=""
                            v-if="index != 0"
                            @click="delOp(index)"
                            >-</span
                          >
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <div class="btns" v-if="!$route.query.id && form.analysisMode">
                <el-button @click="addFenxi">保存</el-button>
              </div>
              <div
                class="btns"
                v-if="
                  $route.query.id &&
                  form.analysisMode &&
                  setList &&
                  setList.length == 0
                "
              >
                <el-button @click="addFenxi">保存</el-button>
              </div>
              <div style="color: #999999">
                说明:
                若需预览题号对应的题目，可先保存考试草稿，再到考试列表页中点击当前考试后查看
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <!-- 结果设置 -->
      <div
        v-if="
          form.analysisMode == 1 && mode == 1 && setList && setList.length > 0
        "
      >
        <div class="title">
          结果设置<span @click="addResult" class="addResult">添加结果</span>
        </div>
        <el-table :data="setList" style="margin-top: 20px" class="result_Table">
          <el-table-column prop="id" label="结果ID" align="center">
          </el-table-column>
          <el-table-column prop="result" label="结论" align="center" />
          <!-- <template #default="scope">
              {{scope.row.result.length>0?scope.row.result.join("|"):''}}
            </template> -->
          <el-table-column prop="analysis" label="结论说明" align="center">
            <template #default="scope">
              <el-input
                v-model="scope.row.analysis"
                placeholder="输入结论"
                :disabled="search"
                type="textarea"
                cols="5"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-for="column in dynamicColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            align="center"
          >
            <template #default="scope">
              <el-input
                type="textarea"
                v-model="scope.row[column.prop]"
                :disabled="search"
                cols="5"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column></el-table-column>
          <el-table-column align="right">
            <template #header>
              <el-icon class="del" @click="delResult"><Delete /></el-icon
            ></template>
          </el-table-column>
        </el-table>
      </div>
      <!-- footer button -->
      <div class="footerBtn">
        <el-button :disabled="search" @click="cancel">取消</el-button>
        <el-button @click="submitSome" :disabled="search">保存草稿</el-button>
        <el-button type="primary" @click="subALL('forms')" :disabled="search"
          >提交</el-button
        >
      </div>
    </el-form>
  </div>
  <!-- 对话框 -->
  <div>
    <!-- 选择试卷 -->
    <el-dialog v-model="dialogTablesVisible" title="选择试卷">
      <el-table
        :data="tableData"
        style="width: 100%"
        @row-click="singleElection"
        class="tables_deep"
        highlight-current-row
      >
        <el-table-column align="center" width="55" label="">
          <template #default="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio
              class="radio"
              v-model="templateSelection"
              :label="scope.row"
              >&nbsp;</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column align="center" prop="id" label="试卷ID">
        </el-table-column>
        <el-table-column align="center" prop="title" label="试卷名称">
        </el-table-column>
        <el-table-column align="center" prop="title" label="试卷名称">
           <template #default="scope">
             {{ scope.row.property }}试卷
           </template>
        </el-table-column>
        <el-table-column align="center" prop="createdTime" label="创建时间">
        </el-table-column>
        <el-table-column align="center" prop="address" label="操作">
          <template #default="scope">
            <span
              class="hand"
              style="color: #66b1ff"
              @click="toPreview(scope.row)"
              >预览</span
            >
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogTablesVisible = false">取消</el-button>
          <el-button type="primary" @click="check()"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 用户 -->
    <el-dialog v-model="dialogTableVisible" title="选择范围">
      <div style="display: flex">
        <el-tabs v-model="activeName" class="demo-tabs" style="width: 70%">
          <el-tab-pane label="用户组" name="first">
            <el-tree
              ref="treeRef"
              :data="data"
              show-checkbox
              default-expand-all
              node-key="id"
              :default-checked-keys="initActive"
              check-strictly="true"
              highlight-current
              :props="defaultProps"
              @check="checkTree"
              @check-change="handleNodeChange"
            />
          </el-tab-pane>
          <el-tab-pane label="用户" name="second">
            <el-input
              v-model="userName"
              class="w-50 m-2"
              placeholder="请输入用户名搜索，两个字及以上"
              :suffix-icon="Search"
            />
            <el-checkbox-group v-model="form.userIds" @change="changeChoice">
              <el-checkbox
                style="display: block; margin-top: 20px"
                :label="item.userId"
                v-for="item in searchList"
                :key="item.userId"
                >{{ item.realName }}</el-checkbox
              >
            </el-checkbox-group>
          </el-tab-pane>
        </el-tabs>
        <div class="checkActive">
          <div class="checkActive_title">
            <div>已选择：</div>
            <div @click="deletes('all', form.roleIds, form.userIds)">清空</div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in form.roleIds"
            :key="index"
          >
            <div>{{ item.title }}</div>
            <div @click="deletes('one', item.id)">x</div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in checkActive1"
            :key="index"
          >
            <div>{{ item.realName }}</div>
            <div @click="deletes('two', item.userId)">x</div>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button
          size="small"
          @click="
            () => {
              dialogTableVisible = false;
            }
          "
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="addsub">确认</el-button>
      </div>
    </el-dialog>
    <!-- 取消 -->
    <el-dialog
      v-model="dialogVisible"
      title="确认操作"
      width="30%"
      :before-close="handleClose"
    >
      <span>取消不会保存您编辑的数据，确认取消么？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="
              dialogVisible = false;
              $router.push('/exam');
            "
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script >
import {
  paperExamList,
  draft,
  roleList,
  userList,
  addexam,
  examDetail,
  examEdit,
  settingInitList,
  initListByData,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import msEditor from "@/components/ms-editor/index";
import { reactive } from "@vue/reactivity";
import { Delete } from "@element-plus/icons";
import htmlMethods from "@/utils/htmlFunction.js";
export default {
  name: "addExam",
  components: { msEditor, Delete },
  data() {
    return {
      dynamicColumns: [],
      formData: {},
      resultDialog: false,
      setList: [],
      yijingList: [],
      search: false,
      reg: "",
      mode: "",
      modeType: "",
      tableInfo: {
        encodeId: "4c2353e1e",
        userId: "a5de6809512",
        userName: "ms6000000014001862",
        account: "***********",
        projectId: 1,
        projectName: "梅斯医学",
        moduleName: "医调研",
      },
      rules: {
        paperId: [{ required: true, message: "请选择", trigger: "blur" }],
        title: [{ required: true, message: "请输入考试名称", trigger: "blur" }],
        lineTime: [
          { required: true, message: "请选择考试时间", trigger: "blur" },
        ],
        scope: [{ required: true, message: "请选择考试范围", trigger: "blur" }],
        pcView: [
          { required: true, message: "请选择Pc答题方式", trigger: "blur" },
        ],
        times: [
          {
            message: "请输入大于0的整数",
            pattern: /^\+?[1-9]\d*$/,
            trigger: "blur",
          },
        ],
        timeLimit: [
          {
            pattern: /^([1-9][0-9]{0,2}|1024)$/,
            message: "请输入大于0小于1024的整数",
            trigger: "blur",
          },
        ],
      },

      templateSelection: "",
      checkList: [],
      searchList: [],
      tableData: [],
      filterText: "",
      member: "",
      initActive: [],
      userIds: [],
      checkActive: [],
      checkActive1: [],
      dialogTablesVisible: false,
      dialogVisible: false,
      dialogTableVisible: false,

      activeName: "first",
      form: {
        title: "",
        summary: "",
        startTime: "",
        endTime: "",
        times: "",
        timeLimit: "",
        passScore: "",
        analysisMode: 0,
        scope: "0",
        pcView: "",
        mode: "",
        resultSettings: [],
        multipleAnalysisStatus: "0",
        analysisStatus: "0",
        weightSettings: [
          {
            weightEnd: "",
            weightStart: "",
            quota: "",
          },
        ],
        resultVisible: "1",
        answerVisible: "1",
        scoreVisible: "1",
        overScoreVisible: "1",
        overResultVisible: "1",
        overAnswerVisible: "1",
        lineTime: "",
        paperId: "",
        roleIds: [],
        userIds: [],
        radio: 3,
        checkList1: ["1", "2", "3"],
        checkList2: ["1", "2", "3"],
      },
      defaultProps: {
        children: "childRoles",
        label: "title",
      },
      totalScore: "",
      data: [],
      userName: "",
      focusTimes:false,
      data1: [
        {
          id: 20,
          label: "Level one 1",
        },
        {
          id: 21,
          label: "Level one 2",
        },
        {
          id: 22,
          label: "Level one 3",
        },
      ],
      mode: "", //量表
      tagList: [], //标签列表
      showBackBtn: true,
    };
  },
  watch: {
    userName(val) {
      this.searchLiist();
    },
  },
  async mounted() {
    let publishedStartTime;
    let publishedEndTime;
    if (this.lineTime && this.lineTime.length > 0) {
      publishedStartTime = this.lineTime[0];
      publishedEndTime = this.lineTime[1];
    }
    let params = {
      startTime: publishedStartTime,
      endTime: publishedEndTime,
      pageIndex: this.currentPage,
      pageSize: this.pageSize,
      title: this.title,
    };
    paperExamList(params).then((res) => {
      this.total = res.totalSize;
      this.tableData = res.data;
    });
    roleList().then((res) => {
      this.data = res.data.filter((res) => {
        return res.userNum > 0;
      });
    });
    if (this.$route.query.searchSet == "searchSet") {
      this.$nextTick(() => {
        this.search = true;
      });
    }
    if (this.$route.query && this.$route.query.id) {
      let res = await examDetail(this.$route.query.id);
      res.data.scope = String(res.data.scope);
      res.data.passScore =
        res.data.passScore == null ? undefined : res.data.passScore;
      res.data.timeLimit =
        res.data.timeLimit == null ? undefined : res.data.timeLimit;
      this.totalScore = res.data.totalScore;
      this.mode = res.data.mode;
      this.form.mode = res.data.mode;
      this.form.modeType = res.data.type;
      this.form = JSON.parse(JSON.stringify(res.data));
      this.form.analysisStatus = res.data.analysisStatus.toString();
      this.form.multipleAnalysisStatus =
        res.data.multipleAnalysisStatus.toString();
      this.initActive = this.form.roleIds || [];
      this.checkActive1 = this.form.userList || [];
      if(this.form.summary){
        this.form.summary = htmlMethods.unexcapeHtml(this.form.summary)
      }
      if (this.mode) {
        res.data.analysisSettings&&res.data.analysisSettings.forEach((element, index) => {
          element.id = index + 1;
          element.result = JSON.parse(element.result).join(" ");
        });
        let customAnalysisList = [];
        res.data.analysisSettings&&res.data.analysisSettings.forEach((element) => {
          customAnalysisList.push(JSON.parse(element.customAnalysis));
          let arr = JSON.parse(element.customAnalysis);
          let keys_all = Object.keys(arr);
          customAnalysisList.forEach((elements) => {
            this.dynamicColumns = [];
            keys_all.forEach((row) => {
              element[row] = elements[row];
              this.dynamicColumns.push({
                prop: row,
                label: row,
              });
            });
          });
        });

        this.setList = res.data.analysisSettings;
      }

      if (this.form.userList && this.form.userList.length > 0) {
        this.form.userIds = this.form.userList.map((item) => {
          return item.userId;
        });
      } else {
        this.form.userIds = [];
      }
      if (
        !this.form.overScoreVisible &&
        !this.form.overResultVisible &&
        !this.form.overAnswerVisible
      ) {
        this.form.checkList2 = [];
      }
      if (this.form.overAnswerVisible == 1) {
        this.form.checkList2 = ["1", "2", "3"];
      }
      if (
        this.form.overScoreVisible == 1 &&
        this.form.overResultVisible == 0 &&
        this.form.overAnswerVisible == 0
      ) {
        this.form.checkList2 = ["1"];
      }
      if (
        this.form.overResultVisible == 1 &&
        this.form.overAnswerVisible == 0
      ) {
        this.form.checkList2 = ["1", "2"];
      }

      if (this.form.answerVisible == 1) {
        this.form.checkList1 = ["1", "2", "3"];
      }
      if (
        this.form.overScoreVisible == 1 &&
        this.form.resultVisible == 0 &&
        this.form.answerVisible == 0
      ) {
        this.form.checkList1 = ["1"];
      }
      if (this.form.resultVisible == 1 && this.form.answerVisible == 0) {
        this.form.checkList1 = ["1", "2"];
      }
      if (
        !this.form.overScoreVisible &&
        !this.form.resultVisible &&
        !this.form.answerVisible
      ) {
        this.form.checkList1 = [];
      }

      this.form.lineTime = [this.form.startTime, this.form.endTime];
      this.form.nextStatus =String( this.form.nextStatus)

    }
  },
  methods: {
    blurTime(){
      this.focusTimes=false
    },
     focusTime(){
this.focusTimes=true
     },
    backHome() {
      let baseUrl = "https://www.medsci.cn/form";
      window.location.href = baseUrl;
    },
    submitSuccess() {
    },
    // 编辑  输入结果分析  重新查询结果设置
    inputchange() {
      if (this.$route.query.id && this.setList && this.setList.length > 0) {
        this.addFenxi();
      }
    },
    // 添加结果
    addResult() {
      this.resultDialog = true;
      this.formData.name = "";
    },
    // 删除结果配置
    delResult() {
      if (this.search) {
      } else {
        this.setList.forEach((item) => {
          const lastProp =
            this.dynamicColumns[this.dynamicColumns.length - 1].prop;
          delete item[lastProp];
          if (item.customAnalysis) {
            let obj = item.customAnalysis
              ? JSON.parse(item.customAnalysis)
              : "";
            delete obj[lastProp];
            item.customAnalysis = JSON.stringify(obj);
          }
        });

        this.dynamicColumns.pop();
      }
    },
    // 确认添加结果
    resultSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.resultDialog = false;
          this.$nextTick(() => {
            this.dynamicColumns.push({
              prop: this.formData.name,
              label: this.formData.name,
            });
            this.setList.forEach((item) => {
              item[this.formData.name] = "";
            });
          });

          // 表单校验通过，执行提交操作
        } else {
          // 表单校验失败，不执行提交操作
        }
      });
    },
    tip() {},
    async addFenxi() {
      if (this.mode == 1) {
        if (this.form.analysisMode == 1) {
          this.form.weightSettings = [];
          for (let i = 0; i < this.form.resultSettings.length; i++) {
            for (
              let j = 0;
              j < this.form.resultSettings[i].weightSettings.length;
              j++
            ) {
              if (
                (this.form.resultSettings[i].weightSettings[j].weightStart !==
                  0 &&
                  this.form.resultSettings[i].weightSettings[j].weightStart ==
                    null) ||
                (this.form.resultSettings[i].weightSettings[j].weightStart !==
                  0 &&
                  this.form.resultSettings[i].weightSettings[j].weightStart ==
                    "")
              ) {
                ElMessage.error(
                  `标签为${this.form.resultSettings[i].labelName}的权重起始值不能为空`
                );
                return;
              }
              if (
                (this.form.resultSettings[i].weightSettings[j].weightEnd !==
                  0 &&
                  this.form.resultSettings[i].weightSettings[j].weightEnd ==
                    null) ||
                (this.form.resultSettings[i].weightSettings[j].weightEnd !==
                  0 &&
                  this.form.resultSettings[i].weightSettings[j].weightEnd == "")
              ) {
                ElMessage.error(
                  `标签为${this.form.resultSettings[i].labelName}的权重最大值不能为空`
                );
                return;
              }
              if (
                (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                  this.form.resultSettings[i].weightSettings[j].quota ==
                    null) ||
                (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                  this.form.resultSettings[i].weightSettings[j].quota == "")
              ) {
                ElMessage.error(
                  `标签为${this.form.resultSettings[i].labelName}的权重对应的${
                    this.mode == 1 ? "指标描述" : "说明"
                  }不能为空`
                );
                return;
              }
            }
          }
        } else {
          for (let i = 0; i < this.form.weightSettings.length; i++) {
            if (
              (this.form.weightSettings[i].weightStart !== 0 &&
                this.form.weightSettings[i].weightStart == null) ||
              (this.form.weightSettings[i].weightStart !== 0 &&
                this.form.weightSettings[i].weightStart == "")
            ) {
              ElMessage.error(`权重起始值不能为空`);
              return;
            }
            if (
              (this.form.weightSettings[i].weightEnd !== 0 &&
                this.form.weightSettings[i].weightEnd == null) ||
              (this.form.weightSettings[i].weightEnd !== 0 &&
                this.form.weightSettings[i].weightEnd == "")
            ) {
              ElMessage.error(`权重最大值不能为空`);
              return;
            }
            if (
              (this.form.weightSettings[i].quota !== 0 &&
                this.form.weightSettings[i].quota == null) ||
              (this.form.weightSettings[i].quota !== 0 &&
                this.form.weightSettings[i].quota == "")
            ) {
              ElMessage.error(
                `权重对应的${this.mode == 1 ? "指标描述" : "说明"}不能为空`
              );
              return;
            }
          }
        }
      }
      let res = await initListByData({
        resultSettings: this.form.resultSettings,
      });
      let data = res.data.map((element, index) => {
        return {
          result: element.join(" "),
          customAnalysis: "",
          analysis: "",
          id: index + 1,
        };
      });
      this.setList = data;
    },
    // 添加分层
    addOp() {
      this.form.weightSettings.push({
        weightEnd: "",
        weightStart: "",
        quota: "",
      });
    },
    // 带标签的添加分层
    addOps(index) {
      this.form.resultSettings[index].weightSettings.push({
        weightEnd: "",
        weightStart: "",
        quota: "",
      });
    },
    // 删除分层
    delOp(index) {
      this.form.weightSettings.splice(index, 1);
    },
    delOps(index, index1) {
      this.form.resultSettings[index].weightSettings.splice(index1, 1);
    },
    handleNodeChange(data, checked, deep) {
      if (this.initActive.includes(data.id)) {
        if (!checked) {
          this.initActive = this.initActive.filter((id) => {
            return id != data.id;
          });
        }
      } else {
        if (checked) {
          this.initActive.push(data.id);
        }
      }
      if (data.childRoles.length > 0) {
        return false;
      }
    },
    // 取消
    cancel() {
      this.dialogVisible = true;
    },
    // 保存草稿
    async submitSome() {
      let arr = [];
      this.setList.forEach((element) => {
        let keys = Object.keys(element);
        let keyarr = keys.filter(
          (key) =>
            key !== "analysis" &&
            key !== "customAnalysis" &&
            key !== "id" &&
            key !== "result"
        );
        let obj = {};

        keyarr.forEach((rows) => {
          obj[rows] = element[rows];
        });
        element.customAnalysis = JSON.stringify(obj);
      });
      this.setList.forEach((element) => {
        element.result = element.result
          ? JSON.stringify(element.result.split(" "))
          : "";
      });
      this.form.analysisSettings = this.form.analysisMode ? this.setList : [];
      this.form.mode = this.mode;
      if (this.analysisSettings) {
        await this.addFenxi();
      }

      if (this.$route.query.id) {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.form.timeLimit =
          this.form.timeLimit == undefined ? null : this.form.timeLimit;
        this.form.passScore =
          this.form.passScore == undefined ? null : this.form.passScore;
           this.form.summary = htmlMethods.excapeHtml(this.form.summary)
        draft(this.form).then((res) => {
          if (res.status == "200") {
            this.$router.push("/exam");
          }
        });
      } else {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        draft(this.form).then((res) => {
          if (res.status == "200") {
            this.$router.push("/exam");
          }
        });
      }
    },
    // 保存全部
    async subALL(formName) {
      if(this.mode==0&&this.form.analysisStatus==1){
        if (this.form.analysisMode == 1) {
        this.form.weightSettings = [];
        for (let i = 0; i < this.form.resultSettings.length; i++) {
          for (
            let j = 0;
            j < this.form.resultSettings[i].weightSettings.length;
            j++
          ) {
            if (
              (this.form.resultSettings[i].weightSettings[j].weightStart !==
                0 &&
                this.form.resultSettings[i].weightSettings[j].weightStart ==
                  null) ||
              (this.form.resultSettings[i].weightSettings[j].weightStart !==
                0 &&
                this.form.resultSettings[i].weightSettings[j].weightStart == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重起始值不能为空`
              );
              return;
            }
            if (
              (this.form.resultSettings[i].weightSettings[j].weightEnd !== 0 &&
                this.form.resultSettings[i].weightSettings[j].weightEnd ==
                  null) ||
              (this.form.resultSettings[i].weightSettings[j].weightEnd !== 0 &&
                this.form.resultSettings[i].weightSettings[j].weightEnd == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重最大值不能为空`
              );
              return;
            }
            if (
              (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                this.form.resultSettings[i].weightSettings[j].quota == null) ||
              (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                this.form.resultSettings[i].weightSettings[j].quota == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重对应的${
                  this.mode == 1 ? "指标描述" : "说明"
                }不能为空`
              );
              return;
            }
          }
        }
      } else {
        for (let i = 0; i < this.form.weightSettings.length; i++) {
          if (
            (this.form.weightSettings[i].weightStart !== 0 &&
              this.form.weightSettings[i].weightStart == null) ||
            (this.form.weightSettings[i].weightStart !== 0 &&
              this.form.weightSettings[i].weightStart == "")
          ) {
            ElMessage.error(`权重起始值不能为空`);
            return;
          }
          if (
            (this.form.weightSettings[i].weightEnd !== 0 &&
              this.form.weightSettings[i].weightEnd == null) ||
            (this.form.weightSettings[i].weightEnd !== 0 &&
              this.form.weightSettings[i].weightEnd == "")
          ) {
            ElMessage.error(`权重最大值不能为空`);
            return;
          }
          if (
            (this.form.weightSettings[i].quota !== 0 &&
              this.form.weightSettings[i].quota == null) ||
            (this.form.weightSettings[i].quota !== 0 &&
              this.form.weightSettings[i].quota == "")
          ) {
            ElMessage.error(
              `权重对应的${this.mode == 1 ? "指标描述" : "说明"}不能为空`
            );
            return;
          }
        }
      }
      }
      if(this.mode==1){
        if (this.form.analysisMode == 1) {
        this.form.weightSettings = [];
        for (let i = 0; i < this.form.resultSettings.length; i++) {
          for (
            let j = 0;
            j < this.form.resultSettings[i].weightSettings.length;
            j++
          ) {
            if (
              (this.form.resultSettings[i].weightSettings[j].weightStart !==
                0 &&
                this.form.resultSettings[i].weightSettings[j].weightStart ==
                  null) ||
              (this.form.resultSettings[i].weightSettings[j].weightStart !==
                0 &&
                this.form.resultSettings[i].weightSettings[j].weightStart == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重起始值不能为空`
              );
              return;
            }
            if (
              (this.form.resultSettings[i].weightSettings[j].weightEnd !== 0 &&
                this.form.resultSettings[i].weightSettings[j].weightEnd ==
                  null) ||
              (this.form.resultSettings[i].weightSettings[j].weightEnd !== 0 &&
                this.form.resultSettings[i].weightSettings[j].weightEnd == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重最大值不能为空`
              );
              return;
            }
            if (
              (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                this.form.resultSettings[i].weightSettings[j].quota == null) ||
              (this.form.resultSettings[i].weightSettings[j].quota !== 0 &&
                this.form.resultSettings[i].weightSettings[j].quota == "")
            ) {
              ElMessage.error(
                `标签为${this.form.resultSettings[i].labelName}的权重对应的${
                  this.mode == 1 ? "指标描述" : "说明"
                }不能为空`
              );
              return;
            }
          }
        }
          if (!this.setList.length) {
            ElMessage.error(`请先保存结果分析`);
            return;
          } else {
            for (let i = 0; i < this.setList.length; i++) {
              if (!this.setList[i].analysis) {
                ElMessage.error(`结论说明不能为空`);
                return;
              }
            }
          }
      } else {
        for (let i = 0; i < this.form.weightSettings.length; i++) {
          if (
            (this.form.weightSettings[i].weightStart !== 0 &&
              this.form.weightSettings[i].weightStart == null) ||
            (this.form.weightSettings[i].weightStart !== 0 &&
              this.form.weightSettings[i].weightStart == "")
          ) {
            ElMessage.error(`权重起始值不能为空`);
            return;
          }
          if (
            (this.form.weightSettings[i].weightEnd !== 0 &&
              this.form.weightSettings[i].weightEnd == null) ||
            (this.form.weightSettings[i].weightEnd !== 0 &&
              this.form.weightSettings[i].weightEnd == "")
          ) {
            ElMessage.error(`权重最大值不能为空`);
            return;
          }
          if (
            (this.form.weightSettings[i].quota !== 0 &&
              this.form.weightSettings[i].quota == null) ||
            (this.form.weightSettings[i].quota !== 0 &&
              this.form.weightSettings[i].quota == "")
          ) {
            ElMessage.error(
              `权重对应的${this.mode == 1 ? "指标描述" : "说明"}不能为空`
            );
            return;
          }
        }
      }
      }
      if(this.mode == 2){
        this.form.pcView = 1
      }
      let arr = [];
      this.setList.forEach((element) => {
        let keys = Object.keys(element);
        let keyarr = keys.filter(
          (key) =>
            key !== "analysis" &&
            key !== "customAnalysis" &&
            key !== "id" &&
            key !== "result"
        );
        let obj = {};

        keyarr.forEach((rows) => {
          obj[rows] = element[rows];
        });
        element.customAnalysis = JSON.stringify(obj);
      });

      this.form.analysisSettings = this.form.analysisMode ? this.setList : [];
      this.form.mode = this.mode;
      if (this.analysisSettings) {
        await this.addFenxi();
      }

      if (this.$route.query.id) {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];

        //    if (this.form.roleIds && this.form.roleIds.length > 0) {
        //   this.form.roleIds = this.form.roleIds.map((element) => {
        //     return element.id;
        //   });
        // }
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.form.summary = htmlMethods.excapeHtml(this.form.summary)
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.setList.forEach((element) => {
              element.result = element.result
                ? JSON.stringify(element.result.split(" "))
                : "";
            });
            this.form.timeLimit =
              this.form.timeLimit == undefined ? null : this.form.timeLimit;
            this.form.passScore =
              this.form.passScore == undefined ? null : this.form.passScore;

            examEdit(this.form).then((res) => {
              if (res.status == "200") {
                this.$router.push("/exam");
              }
            });
          }
        });
      } else {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.form.summary = htmlMethods.excapeHtml(this.form.summary)
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.setList.forEach((element) => {
              element.result = element.result
                ? JSON.stringify(element.result.split(" "))
                : "";
            });
            this.form.timeLimit =
              this.form.timeLimit == undefined ? null : this.form.timeLimit;
            this.form.passScore =
              this.form.passScore == undefined ? null : this.form.passScore;

            addexam(this.form).then((res) => {
              if (res.status == "200") {
                this.$router.push("/exam");
              }
            });
          }
        });
      }
    },
    // 选择试卷
    choiceShiJuan() {
      this.dialogTablesVisible = true;
    },
    // 跳转到预览页
    toPreview(row) {
      window.open(window.location.origin + `/paperPreview?id=${row.encodeId}`);
    },
    changeChoice(e) {
      this.searchList.forEach((element) => {
        e.forEach((rows) => {
          if (element.userId == rows) {
            this.checkActive1.push(element);
          }
        });
      });
      this.checkActive1 = this.fn2(this.checkActive1);
      let a = this.checkActive1.map((element) => {
        return element.userId;
      });
      if (a.length != e.length) {
        a.forEach((element, index) => {
          if (e.indexOf(element) == -1) {
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    fn2(arr) {
      const res = new Map();
      return arr.filter(
        (arr) => !res.has(arr.userId) && res.set(arr.userId, arr.userId)
      );
    },
    //   查询用户列表
    async searchLiist() {
      if (this.userName.trim() && this.userName.length >= 2) {
        userList(this.userName).then((res) => {
          this.searchList = res.data;
        });
      }
    },
    //   过滤node
    filterNode(value, data) {
      if (!value) return true;
      return data.label.includes(value);
    },
    //   添加用户
    addsub() {
      this.dialogTableVisible = false;
    },
    //   设置右边显示的数据
    setActive() {
      this.initActive = this.$refs.treeRef.getCheckedKeys(false);
      this.form.roleIds = this.$refs.treeRef.getCheckedNodes(false, true);
    },
    //   删除
    deletes(type, item, item1) {
      if (type == "all") {
        item.forEach((element) => {
          this.$refs.treeRef.setChecked(element.id, false, true);
        });
        this.form.userIds = [];
        this.checkActive1 = [];
        this.setActive();
      } else if (type == "one") {
        this.$refs.treeRef.setChecked(item, false, true);
        this.setActive();
      } else {
        this.form.userIds.forEach((element, index) => {
          if (item == element) {
            this.form.userIds.splice(index, 1);
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    async changeRadio() {
      if (this.form.analysisMode == 1) {
        this.form.weightSettings = [];
        let res = await settingInitList(this.form.paperId);
        res.data.forEach((item) => {
          item.weightSettings = [
            {
              quota: "",
              weightEnd: "",
              weightStart: "",
            },
          ];
        });
        this.form.resultSettings = res.data;
      } else {
        this.form.resultSettings = [];
        this.form.analysisSettings = [];
        this.form.weightSettings = [
          {
            weightEnd: "",
            weightStart: "",
            quota: "",
          },
        ];
      }
    },
    async check() {
      if (this.templateSelection) {
        this.form.title = this.templateSelection.title;
        this.form.paperId = this.templateSelection.id;
        this.form.paperTitle = this.templateSelection.title;
        this.totalScore = this.templateSelection.totalScore;
        this.mode = this.templateSelection.mode;
        this.modeType = this.templateSelection.type;
        if (this.mode == 1 && this.form.analysisMode == 1) {
          let res = await settingInitList(this.form.paperId);
          res.data.forEach((item) => {
            item.weightSettings = [
              {
                quota: "",
                weightEnd: "",
                weightStart: "",
              },
            ];
          });
          this.form.resultSettings = res.data;
        }

        this.dialogTablesVisible = false;
      } else {
        ElMessage("请选择一项");
      }
    },
    //   复选框选择
    checkTree(e, nodes) {
      const node = this.$refs.treeRef.getNode(e.id);
      this.setNode(node);
      this.setActive();
    },
    setChildrenNode(node, state) {
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i], state);
      }
    },
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    },
    setNode(node) {
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node, node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node, node.checked);
      }
    },
    //选择用户组
    choice() {
      this.dialogTableVisible = true;

      this.$nextTick((vm) => {
        this.setActive();
      });
    },
    //   交卷后
    changeCheckbox(e) {
      if (e.length > 0) {
        e.forEach((element) => {
          if (element == "3") {
            this.form.checkList1 = ["1", "2", "3"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 1;
            this.form.answerVisible = 1;
          } else if (element == "2") {
            this.form.checkList1 = ["1", "2"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 1;
            this.form.answerVisible = 0;
          } else if (element == "1") {
            this.form.checkList1 = ["1"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 0;
            this.form.answerVisible = 0;
          }
        });
      } else {
        this.form.scoreVisible = 0;
        this.form.resultVisible = 0;
        this.form.answerVisible = 0;
      }
    },
    // 考试结束后
    changeCheckbox1(e) {
      if (e.length > 0) {
        e.forEach((element) => {
          if (element == "3") {
            this.form.checkList2 = ["1", "2", "3"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 1;
            this.form.overAnswerVisible = 1;
          } else if (element == "2") {
            this.form.checkList2 = ["1", "2"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 1;
            this.form.overAnswerVisible = 0;
          } else if (element == "1") {
            this.form.checkList2 = ["1"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 0;
            this.form.overAnswerVisible = 0;
          }
        });
      } else {
        this.form.overScoreVisible = 0;
        this.form.overResultVisible = 0;
        this.form.overAnswerVisible = 0;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.containers {
  .title {
    line-height: 40px;
    background: #f2f2f2;
    padding-left: 10px;
    box-sizing: border-box;
    font-size: 14px;
  }
  .addResult:hover {
    cursor: pointer;
  }
  .addResult {
    color: rgb(2, 167, 241);
    margin-left: 15px;
  }

  .gradio {
    color: #7f7f7f;
    margin-left: 20px;
  }
}
.checkActive {
  width: 30%;
  height: 250px;
  overflow: auto;
  margin-top: 40px;
  margin-left: 10px;
  background: #f2f2f2;
  padding: 20px 20px;
  &_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    > div:last-child:hover {
      cursor: pointer;
    }
    > div:last-child {
      width: 40px;
      text-align: center;
    }
  }
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.alreadyChoice {
  margin-left: 130px;
  color: rgb(2, 167, 241);
}
.alreadyChoice:hover {
  cursor: pointer;
}
.line {
  width: 100%;
  height: 1px;
  background: #7f7f7f;
}
.footerBtn {
  display: flex;
  justify-content: flex-end;
  margin: 20px 30px;
}
.tables_deep {
  ::v-deep .el-link.el-link--primary {
    color: #66b1ff;
  }
}
.hand:hover {
  cursor: pointer;
}
.disabledClass {
  position: relative;
  left: 0;
  top: 0;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 998;
  pointer-events: none;
  box-sizing: border-box;
}
.table {
  .table_item {
    display: flex;
    font-size: 14px;
    ::v-deep .el-input__inner {
      width: 30%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      background: #f2f2f2;
      width: 20%;
      text-align: center;
      line-height: 2;
    }
  }
  .table_itema {
    display: flex;
    ::v-deep .el-input__inner {
      width: 60%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      width: 20%;
      text-align: center;
      margin-top: 10px;
    }
    .jiexi_box {
      width: 20%;
      display: block;
      .input_inner {
        ::v-deep .el-input__inner {
          width: 95%;
          line-height: 1.5;
          text-align: center;
          margin-bottom: 10px;
        }
      }
      > div {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }
      span {
        display: inline-block;
        width: 100% !important;
        text-align: center;
      }
    }
    .box {
      width: 60%;
      display: block;
      .input_inner {
        ::v-deep .el-input__inner {
          width: 95%;
          line-height: 1.5;
          text-align: center;
          margin-bottom: 10px;
        }
      }
      > div {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }
      span {
        display: inline-block;
        width: 33.3% !important;
        text-align: center;
      }
    }
  }
  .jiexi_Op {
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      margin-bottom: 10px;
      height: 28px;
      span:hover {
        cursor: pointer;
      }
    }
    > div {
      span:first-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        margin-right: 10px;
        font-weight: 600;
      }
      span:last-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        font-weight: 600;
      }
    }
  }
  .op {
    > div {
      margin-bottom: 10px;
      height: 28px;
      span:hover {
        cursor: pointer;
      }
    }
    > div {
      span:first-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        margin-right: 10px;
        font-weight: 600;
      }
      span:last-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        font-weight: 600;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: flex-end;
  }
}
.biaoqian_table {
  margin-left: 120px;
  .table_item {
    font-size: 14px;
    display: flex;
    ::v-deep .el-input__inner {
      width: 30%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      background: #f2f2f2;
      width: 20%;
      text-align: center;
      line-height: 2;
    }
  }
  .table_itema {
    display: flex;
    ::v-deep .el-input__inner {
      width: 60%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      width: 20%;
      text-align: center;
      margin-top: 10px;
    }
    .jiexi_box {
      width: 20%;
      display: block;
      .input_inner {
        ::v-deep .el-input__inner {
          width: 95%;
          line-height: 1.5;
          text-align: center;
          margin-bottom: 10px;
        }
      }
      > div {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }
      span {
        display: inline-block;
        width: 100% !important;
        text-align: center;
      }
    }
  }
  .jiexi_Op {
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      margin-bottom: 10px;
      height: 28px;
      span:hover {
        cursor: pointer;
      }
    }
    > div {
      span:first-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        margin-right: 10px;
        font-weight: 600;
      }
      span:last-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        font-weight: 600;
      }
    }
  }

  .btns {
    display: flex;
    justify-content: flex-end;
  }
}
.defen_table {
  margin-left: 120px;
  .table_item {
    font-size: 14px;
    display: flex;
    ::v-deep .el-input__inner {
      width: 30%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      background: #f2f2f2;
      width: 20%;
      text-align: center;
      line-height: 2;
    }
  }
  .table_itema {
    display: flex;
    ::v-deep .el-input__inner {
      width: 60%;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 10px;
    }
    > div {
      width: 20%;
      text-align: center;
      margin-top: 10px;
    }
    .jiexi_box {
      width: 20%;
      display: block;
      .input_inner {
        ::v-deep .el-input__inner {
          width: 95%;
          line-height: 1.5;
          text-align: center;
          margin-bottom: 10px;
        }
      }
      > div {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }
      span {
        display: inline-block;
        width: 100% !important;
        text-align: center;
      }
    }
  }
  .jiexi_Op {
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      margin-bottom: 10px;
      height: 28px;
      span:hover {
        cursor: pointer;
      }
    }
    > div {
      span:first-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        margin-right: 10px;
        font-weight: 600;
      }
      span:last-child {
        display: inline-block;
        width: 20px;
        text-align: left;
        font-size: 24px;
        color: rgb(2, 167, 241);
        font-weight: 600;
      }
    }
  }

  .btns {
    display: flex;
    justify-content: flex-end;
  }
}
.jieguo {
  ::v-deep .el-form-item {
    padding-right: 0;
    .el-form-item__content {
      margin-left: 50px !important;
    }
  }
}
.fenxi {
  ::v-deep .el-form-item {
    padding-right: 0;
  }
}
.noclick {
  pointer-events: none;
}
.containers .editor {
  ::v-deep .mce-panel {
    box-sizing: border-box;
  }
}
</style>