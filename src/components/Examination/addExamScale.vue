<template>
  <div class="containers">
    <el-form
      :model="form"
      label-width="120px"
      :rules="rules"
      ref="forms"
      v-if="form"
    >
      <!-- 考试信息 -->
      <div>
        <div class="title">考试信息</div>
        <el-form-item
          v-if="!search"
          :label="form.paperTitle ? '考试内容' : '选择试卷'"
          prop="paperId"
          style="margin-top: 30px"
        >
          <el-button
            type="primary"
            @click="choiceShiJuan"
            style="margin-right: 20px"
            >选择试卷</el-button
          ><span style="color: #66b1ff">{{ form.paperTitle }}</span>
        </el-form-item>
        <el-form-item
          v-else
          :label="form.paperTitle ? '考试内容' : '选择试卷'"
          prop="paperId"
          style="margin-top: 30px"
        >
          <div v-if="form.paperTitle" style="color: #66b1ff">
            {{ form.paperTitle }}
          </div>
        </el-form-item>

        <el-form-item label="考试名称" prop="title">
          <el-input
            v-model="form.title"
            :disabled="search"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="考试说明">
          <ms-editor
            class="editor"
            :class="[search ? 'disabledClass' : '']"
            v-model="form.summary"
          ></ms-editor>
          <div></div>
        </el-form-item>
      </div>

      <!-- 考试设置 -->
      <div>
        <div class="title">考试设置</div>

        <el-form-item label="考试时间" style="margin-top: 30px" prop="lineTime">
          <el-date-picker
            :disabled="search"
            v-model="form.lineTime"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>

        <el-form-item label="考试次数" prop="times">
          <el-input
            :disabled="search"
            v-model="form.times"
            placeholder="请输入大于0的整数"
            style="width: 300px"
          />
        </el-form-item>
      </div>
      <!-- 显示设置 -->
      <div>
        <div class="title">显示设置</div>

        <el-form-item label="PC答题" style="margin-top: 30px" prop="pcView">
          <el-radio-group
            :disabled="search"
            v-model="form.pcView"
            style="vertical-align: text-top"
          >
            <div>
              <el-radio :label="1" style="margin-bottom: 20px"
                >一页一题<span class="gradio">点击按钮进入下一题</span>
              </el-radio>
            </div>
            <div>
              <el-radio :label="0"
                >整页模式<span class="gradio">一页显示所有题目</span></el-radio
              >
            </div>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="交卷后" style="margin-top: 30px">
          <el-checkbox-group
            v-model="form.checkList1"
            @change="changeCheckbox"
            :disabled="search"
          >
            <el-checkbox label="1">分数可见</el-checkbox>
            <el-checkbox label="2">对错可见</el-checkbox>
            <el-checkbox label="3">正确答案可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="考试结束后" style="margin-top: 30px">
          <el-checkbox-group
            v-model="form.checkList2"
            @change="changeCheckbox1"
            :disabled="search"
          >
            <el-checkbox label="1">分数可见</el-checkbox>
            <el-checkbox label="2">对错可见</el-checkbox>
            <el-checkbox label="3">正确答案可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
      <!-- 结果分析 -->
      <div>
        <div class="title">结果分析</div>

        <el-form-item label="分析方式" style="margin-top: 30px" prop="fenxi">
          <el-radio-group
            :disabled="search"
            v-model="form.fenxi"
            style="vertical-align: text-top"
          >
            <div>
              <el-radio :label="1" style="margin-bottom: 20px"
                >根据测评得分<span class="gradio">用户做题结果总分</span>
              </el-radio>
            </div>
            <div>
              <el-radio :label="0"
                >根据标签得分<span class="gradio"
                  >试卷内容结构得分</span
                ></el-radio
              >
            </div>
          </el-radio-group>
          <!-- 列表 -->
          <!-- <el-table :data="tagsList" style="width: 100%">
            <el-table-column prop="date" label="标签" width="180" />
            <el-table-column prop="name" label="关联的题目" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />
            <el-table-column prop="fenceng" label="权重分层" width="180" />

          </el-table> -->
        </el-form-item>
        <div>
          <el-form-item style="margin-top: 30px" prop="pcView">
            <div class="table">
              <div class="table_item">
                <div>标签</div>
                <div>关联的题目</div>
                <div>权重分层</div>
                <div>分层对应的指标</div>
                <div>操作</div>
              </div>
              <div
                class="table_item"
                v-for="(item, index) in tagsList"
                :key="index"
              >
                <div>{{ item.data }}</div>
                <div>{{ item.name }}</div>
                <div>
                  <el-input
                    v-model="items.fenceng"
                    v-for="(items, index) in item.arr"
                    :key="index"
                  ></el-input>
                </div>
                <div>
                  <el-input
                    v-model="items.zhibiao"
                    v-for="(items, index) in item.arr"
                    :key="index"
                  ></el-input>
                </div>
                <div class="op">
                  <div v-for="(items, index) in item.arr" :key="index">
                    <div>
                      <span @click="addOp(item.id)">+</span>
                      <span>
                        <span v-if="index != 0" @click="delOp(item.id)">-</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="btns">
                <el-button>保存</el-button>
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <!-- 结果设置 -->
      <div v-if="form.fenxi == '0'">
        <div class="title">
          结果分析<span @click="addResult" class="addResult">添加结果</span>
        </div>
        <el-table
          :data="setList"
          height="250"
          style="margin-top: 20px"
          class="result_Table"
        >
          <el-table-column prop="id" label="结果ID" align="center" />
          <el-table-column prop="jielun" label="结论" align="center" />
          <el-table-column prop="shuoming" label="结论说明" align="center">
            <template #default="scope">
              <el-input
                v-model="scope.row.shuoming"
                placeholder="输入结论"
                type="textarea"
                cols="5"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-for="column in dynamicColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            align="center"
          >
            <template #default="scope">
              <el-input
                type="textarea"
                v-model="scope.row[column.prop]"
                cols="5"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column></el-table-column>
          <el-table-column align="right">
            <template #header>
              <el-icon class="del" @click="delResult"><Delete /></el-icon
            ></template>
          </el-table-column>
        </el-table>
      </div>
      <!-- footer button -->
      <div class="line"></div>
      <div class="footerBtn">
        <el-button :disabled="search" @click="cancel">取消</el-button>
        <el-button @click="submitSome" :disabled="search">保存草稿</el-button>
        <el-button type="primary" @click="subALL('forms')" :disabled="search"
          >提交</el-button
        >
      </div>
    </el-form>
  </div>
  <!-- 对话框 -->
  <div>
    <!-- 选择试卷 -->
    <el-dialog v-model="dialogTablesVisible" title="选择试卷">
      <el-table
        :data="tableData"
        style="width: 100%"
        @row-click="singleElection"
        class="tables_deep"
        highlight-current-row
      >
        <el-table-column align="center" width="55" label="">
          <template #default="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio
              class="radio"
              v-model="templateSelection"
              :label="scope.row"
              >&nbsp;</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column align="center" prop="id" label="试卷ID">
        </el-table-column>
        <el-table-column align="center" prop="title" label="试卷名称">
        </el-table-column>
        <el-table-column align="center" prop="createdTime" label="创建时间">
        </el-table-column>
        <el-table-column align="center" prop="address" label="操作">
          <template #default="scope">
            <span
              class="hand"
              style="color: #66b1ff"
              @click="toPreview(scope.row)"
              >预览</span
            >
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogTablesVisible = false">取消</el-button>
          <el-button type="primary" @click="check()"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 用户 -->
    <el-dialog v-model="dialogTableVisible" title="选择范围">
      <div style="display: flex">
        <el-tabs v-model="activeName" class="demo-tabs" style="width: 70%">
          <el-tab-pane label="用户组" name="first">
            <el-tree
              ref="treeRef"
              :data="data"
              show-checkbox
              default-expand-all
              node-key="id"
              :default-checked-keys="initActive"
              check-strictly="true"
              highlight-current
              :props="defaultProps"
              @check="checkTree"
              @check-change="handleNodeChange"
            />
          </el-tab-pane>
          <el-tab-pane label="用户" name="second">
            <el-input
              v-model="userName"
              class="w-50 m-2"
              placeholder="请输入用户名搜索，两个字及以上"
              :suffix-icon="Search"
            />
            <el-checkbox-group v-model="form.userIds" @change="changeChoice">
              <el-checkbox
                style="display: block; margin-top: 20px"
                :label="item.userId"
                v-for="item in searchList"
                :key="item.userId"
                >{{ item.realName }}</el-checkbox
              >
            </el-checkbox-group>
          </el-tab-pane>
        </el-tabs>
        <div class="checkActive">
          <div class="checkActive_title">
            <div>已选择：</div>
            <div @click="deletes('all', form.roleIds, form.userIds)">清空</div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in form.roleIds"
            :key="index"
          >
            <div>{{ item.title }}</div>
            <div @click="deletes('one', item.id)">x</div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in checkActive1"
            :key="index"
          >
            <div>{{ item.realName }}</div>
            <div @click="deletes('two', item.userId)">x</div>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button
          size="small"
          @click="
            () => {
              dialogTableVisible = false;
            }
          "
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="addsub">确认</el-button>
      </div>
    </el-dialog>
    <!-- 取消 -->
    <el-dialog
      v-model="dialogVisible"
      title="确认操作"
      width="30%"
      :before-close="handleClose"
    >
      <span>取消不会保存您编辑的数据，确认取消么？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="
              dialogVisible = false;
              $router.push('/exam');
            "
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 添加结果 -->

    <span
      ><el-dialog v-model="resultDialog" title="新建结果" width="30%">
        <el-form
          ref="form"
          :rules="formRules"
          :model="formData"
          label-width="100px"
        >
          <el-form-item label="姓名">
            <el-input v-model="formData.name" prop="name" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="resultDialog = false">取消</el-button>
            <el-button type="primary" @click="resultSubmit('form')">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog></span
    >
  </div>
</template>

<script >
import {
  paperExamList,
  draft,
  roleList,
  userList,
  addexam,
  examDetail,
  examEdit,
} from "@/request/service.js";
import htmlMethods from "@/utils/htmlFunction.js";
import { ElMessage } from "element-plus";
import msEditor from "@/components/ms-editor/index";
import { reactive } from "@vue/reactivity";
import { Delete } from "@element-plus/icons";
export default {
  name: "addExam",
  components: { msEditor, Delete },
  data() {
    return {
      yijingList: [],
      search: false,
      reg: "",
      resultDialog: false,
      rules: {
        paperId: [{ required: true, message: "请选择", trigger: "blur" }],
        title: [{ required: true, message: "请输入考试名称", trigger: "blur" }],
        lineTime: [
          { required: true, message: "请选择考试时间", trigger: "blur" },
        ],
        scope: [{ required: true, message: "请选择考试范围", trigger: "blur" }],
        pcView: [
          { required: true, message: "请选择Pc答题方式", trigger: "blur" },
        ],
        times: [
          {
            message: "请输入大于0的整数",
            pattern: /^\+?[1-9]\d*$/,
            trigger: "blur",
          },
        ],
        timeLimit: [
          {
            pattern: /^([1-9][0-9]{0,2}|1024)$/,
            message: "请输入大于0小于1024的整数",
            trigger: "blur",
          },
        ],
      },
      formRules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          // { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符之间', trigger: 'blur' }
        ],
      },
      formData: {
        name: "",
      },
      dynamicColumns: [],

      templateSelection: "",
      checkList: [],
      searchList: [],
      tableData: [],
      filterText: "",
      member: "",
      initActive: [],
      userIds: [],
      checkActive: [],
      checkActive1: [],
      dialogTablesVisible: false,
      dialogVisible: false,
      dialogTableVisible: false,

      activeName: "first",
      form: {
        title: "",
        summary: "",
        startTime: "",
        endTime: "",
        times: "",
        timeLimit: "",
        passScore: "",
        scope: "0",
        pcView: "",
        fenxi: 1,
        resultVisible: "1",
        answerVisible: "1",
        scoreVisible: "1",
        overScoreVisible: "1",
        overResultVisible: "1",
        overAnswerVisible: "1",
        lineTime: "",
        paperId: "",
        roleIds: [],
        userIds: [],
        radio: 3,
        checkList1: ["1", "2", "3"],
        checkList2: ["1", "2", "3"],
      },
      defaultProps: {
        children: "childRoles",
        label: "title",
      },
      totalScore: "",
      data: [],
      userName: "",
      data1: [
        {
          id: 20,
          label: "Level one 1",
        },
        {
          id: 21,
          label: "Level one 2",
        },
        {
          id: 22,
          label: "Level one 3",
        },
      ],
      // 标签列表
      tagsList: [
        {
          name: "1，2，3，4，5",
          data: "水合程度",
          id: 1,
          arr: [
            { fenceng: "29－52", zhibiao: "干燥" },
            { fenceng: "29－52", zhibiao: "干燥" },
          ],
        },
        {
          name: "1，2，3，4，5",
          data: "水合程度",
          id: 2,
          arr: [
            { fenceng: "29－52", zhibiao: "干燥" },
            { fenceng: "29－52", zhibiao: "干燥" },
          ],
        },
        {
          name: "1，2，3，4，5",
          data: "水合程度",
          id: 3,
          arr: [
            { fenceng: "29－52", zhibiao: "干燥" },
            { fenceng: "29－52", zhibiao: "干燥" },
          ],
        },
        {
          name: "1，2，3，4，5",
          data: "水合程度",
          id: 4,
          arr: [
            { fenceng: "29－52", zhibiao: "干燥" },
            { fenceng: "29－52", zhibiao: "干燥" },
          ],
        },
      ],

      // 结果设置列表
      setList: [
        {
          id: 1,
          jielun: "结论",
          shuoming: "结论说明",
        },
        {
          id: 2,
          jielun: "结论",
          shuoming: "结论说明",
        },
        {
          id: 3,
          jielun: "结论",
          shuoming: "结论说明",
        },
      ],
    };
  },
  watch: {
    userName(val) {
      this.searchLiist();
    },
  },
  async mounted() {
    let publishedStartTime;
    let publishedEndTime;
    if (this.lineTime && this.lineTime.length > 0) {
      publishedStartTime = this.lineTime[0];
      publishedEndTime = this.lineTime[1];
    }
    let params = {
      startTime: publishedStartTime,
      endTime: publishedEndTime,
      pageIndex: this.currentPage,
      pageSize: this.pageSize,
      title: this.title,
    };
    paperExamList(params).then((res) => {
      this.total = res.totalSize;
      this.tableData = res.data;
    });
    roleList().then((res) => {
      this.data = res.data.filter((res) => {
        return res.userNum > 0;
      });
    });
    if (this.$route.query.searchSet == "searchSet") {
      this.$nextTick(() => {
        this.search = true;
      });
    }
    if (this.$route.query && this.$route.query.id) {
      examDetail(this.$route.query.id).then((res) => {
        res.data.scope = String(res.data.scope);
        res.data.passScore =
          res.data.passScore == null ? undefined : res.data.passScore;
        res.data.timeLimit =
          res.data.timeLimit == null ? undefined : res.data.timeLimit;
        this.totalScore = res.data.totalScore;
        this.form = JSON.parse(JSON.stringify(res.data));
        this.initActive = this.form.roleIds || [];
        this.checkActive1 = this.form.userList || [];
        if(this.form.summary){
            this.form.summary = htmlMethods.unexcapeHtml(this.form.summary)
          }
        if (this.form.userList && this.form.userList.length > 0) {
          this.form.userIds = this.form.userList.map((item) => {
            return item.userId;
          });
        } else {
          this.form.userIds = [];
        }
        if (
          !this.form.overScoreVisible &&
          !this.form.overResultVisible &&
          !this.form.overAnswerVisible
        ) {
          this.form.checkList2 = [];
        }
        if (this.form.overAnswerVisible == 1) {
          this.form.checkList2 = ["1", "2", "3"];
        }
        if (
          this.form.overScoreVisible == 1 &&
          this.form.overResultVisible == 0 &&
          this.form.overAnswerVisible == 0
        ) {
          this.form.checkList2 = ["1"];
        }
        if (
          this.form.overResultVisible == 1 &&
          this.form.overAnswerVisible == 0
        ) {
          this.form.checkList2 = ["1", "2"];
        }

        if (this.form.answerVisible == 1) {
          this.form.checkList1 = ["1", "2", "3"];
        }
        if (
          this.form.overScoreVisible == 1 &&
          this.form.resultVisible == 0 &&
          this.form.answerVisible == 0
        ) {
          this.form.checkList1 = ["1"];
        }
        if (this.form.resultVisible == 1 && this.form.answerVisible == 0) {
          this.form.checkList1 = ["1", "2"];
        }
        if (
          !this.form.overScoreVisible &&
          !this.form.resultVisible &&
          !this.form.answerVisible
        ) {
          this.form.checkList1 = [];
        }

        this.form.lineTime = [this.form.startTime, this.form.endTime];
      });
    }
  },
  methods: {
    // 删除结果配置
    delResult() {

      this.setList.forEach((item) => {
        const lastProp =
          this.dynamicColumns[this.dynamicColumns.length - 1].prop;
        delete item[lastProp];
      });

      this.dynamicColumns.pop();

    },
    // 添加结果
    addResult() {
      this.resultDialog = true;
    },
    // 确认添加结果
    resultSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.resultDialog = false;
          this.$nextTick(() => {
            this.dynamicColumns.push({
              prop: this.formData.name,
              label: this.formData.name,
            });
            this.setList.forEach((item) => {
              item[this.formData.name] = "";
            });
          });
          // 表单校验通过，执行提交操作
        } else {
          // 表单校验失败，不执行提交操作
        }
      });
    },
    // 添加分层
    addOp(id) {
      this.tagsList.forEach((item) => {
        if (item.id == id) {
          item.arr.push({ fenceng: "", zhibiao: "" });
        }
      });
    },
    // 删除分层
    delOp(id) {
      this.tagsList.forEach((item, index) => {
        if (item.id == id) {
          item.arr.splice(index, 1);
        }
      });
    },
    handleNodeChange(data, checked, deep) {
      if (this.initActive.includes(data.id)) {
        if (!checked) {
          this.initActive = this.initActive.filter((id) => {
            return id != data.id;
          });
        }
      } else {
        if (checked) {
          this.initActive.push(data.id);
        }
      }
      if (data.childRoles.length > 0) {
        return false;
      }
    },
    // 取消
    cancel() {
      this.dialogVisible = true;
    },
    // 保存草稿
    submitSome() {
      if (this.$route.query.id) {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.form.timeLimit =
          this.form.timeLimit == undefined ? null : this.form.timeLimit;
        this.form.passScore =
          this.form.passScore == undefined ? null : this.form.passScore;
         this.form.summary = htmlMethods.excapeHtml(this.form.summary)
        draft(this.form).then((res) => {
          if (res.status == "200") {
            this.$router.push("/exam");
          }
        });
      } else {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        draft(this.form).then((res) => {
          if (res.status == "200") {
            this.$router.push("/exam");
          }
        });
      }
    },
    // 保存全部
    subALL(formName) {
      if (this.$route.query.id) {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];

        //    if (this.form.roleIds && this.form.roleIds.length > 0) {
        //   this.form.roleIds = this.form.roleIds.map((element) => {
        //     return element.id;
        //   });
        // }
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.form.summary = htmlMethods.excapeHtml(this.form.summary)
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.form.timeLimit =
              this.form.timeLimit == undefined ? null : this.form.timeLimit;
            this.form.passScore =
              this.form.passScore == undefined ? null : this.form.passScore;
            examEdit(this.form).then((res) => {
              if (res.status == "200") {
                this.$router.push("/exam");
              }
            });
          }
        });
      } else {
        if (this.form.timeLimit > 1024) {
          ElMessage.error("考试时间不能大于1024分");
          return;
        }
        if (this.form.passScore > this.totalScore) {
          ElMessage.error("通过分数不能大于试卷总分" + this.totalScore);
          return;
        }
        this.form.startTime = this.form.lineTime[0];
        this.form.endTime = this.form.lineTime[1];
        if (this.form.roleIds && this.form.roleIds.length > 0) {
          this.form.roleIds = this.form.roleIds.map((element) => {
            if (element.id) {
              return element.id;
            } else {
              return element;
            }
          });
        }
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.form.timeLimit =
              this.form.timeLimit == undefined ? null : this.form.timeLimit;
            this.form.passScore =
              this.form.passScore == undefined ? null : this.form.passScore;
            addexam(this.form).then((res) => {
              if (res.status == "200") {
                this.$router.push("/exam");
              }
            });
          }
        });
      }
    },
    // 选择试卷
    choiceShiJuan() {
      this.dialogTablesVisible = true;
    },
    // 跳转到预览页
    toPreview(row) {
      window.open(window.location.origin + `/paperPreview?id=${row.encodeId}`);
    },
    changeChoice(e) {
      this.searchList.forEach((element) => {
        e.forEach((rows) => {
          if (element.userId == rows) {
            this.checkActive1.push(element);
          }
        });
      });
      this.checkActive1 = this.fn2(this.checkActive1);
      let a = this.checkActive1.map((element) => {
        return element.userId;
      });
      if (a.length != e.length) {
        a.forEach((element, index) => {
          if (e.indexOf(element) == -1) {
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    fn2(arr) {
      const res = new Map();
      return arr.filter(
        (arr) => !res.has(arr.userId) && res.set(arr.userId, arr.userId)
      );
    },
    //   查询用户列表
    async searchLiist() {
      if (this.userName.trim() && this.userName.length >= 2) {
        userList(this.userName).then((res) => {
          this.searchList = res.data;
        });
      }
    },
    //   过滤node
    filterNode(value, data) {
      if (!value) return true;
      return data.label.includes(value);
    },
    //   添加用户
    addsub() {
      this.dialogTableVisible = false;
    },
    //   设置右边显示的数据
    setActive() {
      this.initActive = this.$refs.treeRef.getCheckedKeys(false);
      this.form.roleIds = this.$refs.treeRef.getCheckedNodes(false, true);
    },
    //   删除
    deletes(type, item, item1) {
      if (type == "all") {
        item.forEach((element) => {
          this.$refs.treeRef.setChecked(element.id, false, true);
        });
        this.form.userIds = [];
        this.checkActive1 = [];
        this.setActive();
      } else if (type == "one") {
        this.$refs.treeRef.setChecked(item, false, true);
        this.setActive();
      } else {
        this.form.userIds.forEach((element, index) => {
          if (item == element) {
            this.form.userIds.splice(index, 1);
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    check() {
      if (this.templateSelection) {
        this.form.title = this.templateSelection.title;
        this.form.paperId = this.templateSelection.id;
        this.form.paperTitle = this.templateSelection.title;
        this.totalScore = this.templateSelection.totalScore;
        this.dialogTablesVisible = false;
      } else {
        ElMessage("请选择一项");
      }
    },
    //   复选框选择
    checkTree(e, nodes) {
      const node = this.$refs.treeRef.getNode(e.id);
      this.setNode(node);
      this.setActive();
    },
    setChildrenNode(node, state) {
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i], state);
      }
    },
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    },
    setNode(node) {
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node, node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node, node.checked);
      }
    },
    //选择用户组
    choice() {
      this.dialogTableVisible = true;

      this.$nextTick((vm) => {
        this.setActive();
      });
    },
    //   交卷后
    changeCheckbox(e) {
      if (e.length > 0) {
        e.forEach((element) => {
          if (element == "3") {
            this.form.checkList1 = ["1", "2", "3"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 1;
            this.form.answerVisible = 1;
          } else if (element == "2") {
            this.form.checkList1 = ["1", "2"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 1;
            this.form.answerVisible = 0;
          } else if (element == "1") {
            this.form.checkList1 = ["1"];
            this.form.scoreVisible = 1;
            this.form.resultVisible = 0;
            this.form.answerVisible = 0;
          }
        });
      } else {
        this.form.scoreVisible = 0;
        this.form.resultVisible = 0;
        this.form.answerVisible = 0;
      }
    },
    // 考试结束后
    changeCheckbox1(e) {
      if (e.length > 0) {
        e.forEach((element) => {
          if (element == "3") {
            this.form.checkList2 = ["1", "2", "3"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 1;
            this.form.overAnswerVisible = 1;
          } else if (element == "2") {
            this.form.checkList2 = ["1", "2"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 1;
            this.form.overAnswerVisible = 0;
          } else if (element == "1") {
            this.form.checkList2 = ["1"];
            this.form.overScoreVisible = 1;
            this.form.overResultVisible = 0;
            this.form.overAnswerVisible = 0;
          }
        });
      } else {
        this.form.overScoreVisible = 0;
        this.form.overResultVisible = 0;
        this.form.overAnswerVisible = 0;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.containers {
  .result_Table {
    ::v-deep .el-icon {
      cursor: pointer;
    }
  }
  .addResult:hover {
    cursor: pointer;
  }
  .addResult {
    color: rgb(2, 167, 241);
    margin-left: 15px;
  }
  .table {
    .table_item {
      display: flex;
      ::v-deep .el-input__inner {
        width: 150px;
        line-height: 1.5;
        text-align: center;
        margin-bottom: 10px;
      }
      > div {
        width: 300px;
        text-align: center;
      }
    }
    .op {
      > div {
        margin-bottom: 10px;
        height: 28px;
        span:hover {
          cursor: pointer;
        }
      }
      > div {
        span:first-child {
          display: inline-block;
          width: 20px;
          text-align: left;
          font-size: 24px;
          color: rgb(2, 167, 241);
          margin-right: 10px;
          font-weight: 600;
        }
        span:last-child {
          display: inline-block;
          width: 20px;
          text-align: left;
          font-size: 24px;
          color: rgb(2, 167, 241);
          font-weight: 600;
        }
      }
    }
    .btns {
      display: flex;
      justify-content: flex-end;
    }
  }
  .title {
    line-height: 40px;
    background: #f2f2f2;
    padding-left: 10px;
    box-sizing: border-box;
    font-size: 14px;
  }
  ::v-deep .el-form-item {
    padding-right: 40%;
  }
  .gradio {
    color: #7f7f7f;
    margin-left: 20px;
  }
}
.checkActive {
  width: 30%;
  height: 250px;
  overflow: auto;
  margin-top: 40px;
  margin-left: 10px;
  background: #f2f2f2;
  padding: 20px 20px;
  &_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    > div:last-child:hover {
      cursor: pointer;
    }
    > div:last-child {
      width: 40px;
      text-align: center;
    }
  }
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.alreadyChoice {
  margin-left: 130px;
  color: rgb(2, 167, 241);
}
.alreadyChoice:hover {
  cursor: pointer;
}
.line {
  width: 100%;
  height: 1px;
  background: #7f7f7f;
}
.footerBtn {
  display: flex;
  justify-content: flex-end;
  margin: 20px 30px;
}
.tables_deep {
  ::v-deep .el-link.el-link--primary {
    color: #66b1ff;
  }
}
.hand:hover {
  cursor: pointer;
}
.disabledClass {
  position: relative;
  left: 0;
  top: 0;
  opacity: 0.5;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 998;
  pointer-events: none;
}
.containers .editor{
 ::v-deep .mce-panel{
    box-sizing: border-box;
  }
}
</style>