<template>
  <!-- 试卷单选设置分数dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="shezhiFormVisible"
      title="批量设置分数"
    >
      <el-form :model="form" ref="ruleForm">
        <el-form-item
          label="单选题分值"
          prop="count"
          :rules="[
            { required: true, message: '单选题分值不能为空', trigger: 'blur' },
            {
              pattern: /^\+?[1-9][0-9]*$/,
              message: '请输入＞0的整数',
              trigger: 'blur',
            },
          ]"
          :label-width="formLabelWidth"
        >
          <el-input v-model="form.count" placeholder="请输入＞0的整数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shezhiFormVisible = false">取消</el-button>
          <el-button type="primary" @click="setFenshu('danxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 试卷多选设置分数dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="duoShezhiFormVisible"
      title="批量设置分数"
    >
      <el-form :model="form" ref="ruleForm">
        <el-form-item
          label="多选题分值"
          :rules="[
            { required: true, message: '单选题分值不能为空', trigger: 'blur' },
            {
              pattern: /^\+?[1-9][0-9]*$/,
              message: '请输入＞0的整数',
              trigger: 'blur',
            },
          ]"
          :label-width="formLabelWidth"
          prop="duoCount"
        >
          <el-input v-model="form.duoCount" placeholder="请输入＞0的整数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="duoShezhiFormVisible = false">取消</el-button>
          <el-button type="primary" @click="setFenshu('duoxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 点击确认dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="submitFormVisible"
      title="确认操作"
    >
      试卷提交成功，是否立即发起考试？
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              () => {
                submitFormVisible = false;
                submitnoFormVisible = true;
              }
            "
            >取消</el-button
          >
          <el-button type="primary" @click="a('duoxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 点击取消dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="submitnoFormVisible"
      title="确认操作"
    >
      取消不会保存您编辑的数据，确认取消么？
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submitnoFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitnoFormVisible = false">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <div class="container1">
    <el-form :model="content_List" label-width="120px">
      <el-form-item
        label="试卷名称"
        prop="title"
        :rules="[
          { required: true, message: '试卷名称不能为空', trigger: 'blur' },
        ]"
      >
        <el-input
          v-model="content_List.title"
          placeholder="请输入≤64字的试卷名称"
        />
      </el-form-item>
      <el-button type="primary" @click="addSubject">选择题库</el-button>
      <div class="content1">
        <div class="left">
          <div>
            <div>
              <span>单选题</span>
              <span>{{ count ? (count||0)+'题':'不限'}}</span>
            </div>
            <div>
              <span>多选题</span>
              <span>{{ duoCount ? (duoCount||0)+'题':'不限'}}</span>
            </div>
          </div>
          <div>
            <div>
              <span>总分</span>
              <span>{{ num ? num+'分':'不限' }}</span>
            </div>
          </div>
        </div>
        <div class="right">
          <template v-if="JSON.stringify(content_List.randomRule[0]) != '{}'&&selectAll.length>0">
            <div>已选择以下题库，将从这些题库中随机抽题：</div>
            <el-tag
              v-for="(tag, index) in selectAll"
              :key="index"
              class="mx-1"
              closable
              type="info"
              size="large"
              style="margin-right: 10px;margin-bottom:10px"
              @close="closeTag(index)"
            >
              {{tag.title.length>20? tag.title.substr(0,20)+'…':tag.title }}
            </el-tag>

            <div class="radios">
              <el-radio-group
                @change="changeRadio"
                v-model="radio"
                class="ml-4"
              >
                <el-radio label="0" size="large">从所有题库中随机抽题</el-radio>
                <el-radio label="1" size="large">分题库随机抽题</el-radio>
              </el-radio-group>
            </div>

            <div class="roles">抽题规则：</div>
            <div v-if="radio == 0" class="roles_two">
              <!-- <el-table
                :data="content_List[0].data"
                border
                style="width: 100%; height: auto"
              >
                <el-table-column
                  prop="name"
                  align="center"
                  label="题型及数量"
                />
                <el-table-column prop="num1" align="center" label="抽题数量">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.num1" :step="1" />
                  </template>
                </el-table-column>
                <el-table-column prop="num2" align="center" label="每题分值">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.num2" :step="1" />
                  </template>
                </el-table-column>
              </el-table> -->
              <table>
                <tr>
                  <td>题型及数量</td>
                  <td>抽题数量</td>
                  <td>每题分值</td>
                </tr>
                <tr v-if="content_List.randomRule[0].singleChoiceQuestionCount">
                  <td>
                    单选题
                    {{ content_List.randomRule[0].singleChoiceQuestionCount }}题
                  </td>
                  <td>
                    <el-input-number
                      v-model="content_List.randomRule[0].singleChoice"
                      :step="1"
                      :min="null"
                      :precision="0"
                      @change="setFenshu"
                    />
                  </td>
                  <td>
                    <el-input-number
                      v-model="content_List.randomRule[0].singleChoiceScore"
                      :step="1"
                      :min="null"
                      :precision="0"
                      @change="setFenshu"
                    />
                  </td>
                </tr>
                <tr v-if="content_List.randomRule[0].multipleChoiceQuestionCount">
                  <td>
                    多选题
                    {{
                      content_List.randomRule[0].multipleChoiceQuestionCount
                    }}题
                  </td>
                  <td>
                    <el-input-number
                      v-model="content_List.randomRule[0].multipleChoice"
                      :step="1"
                      :min="null"
                      :precision="0"
                      @change="setFenshu"
                    />
                  </td>
                  <td>
                    <el-input-number
                      v-model="content_List.randomRule[0].multipleChoiceScore"
                      :step="1"
                      :min="null"
                      :precision="0"
                      @change="setFenshu"
                    />
                  </td>
                </tr>
              </table>
            </div>
            <div v-if="radio == '1'">
              <div
                class="roles_two"
                v-for="(item1, index) in content_List.randomRule"
                :key="item1.bankId"
              >
                <div>{{ index + 1 }}. {{ item1.title }}</div>
                <span>
                  <table border="1">
                    <tr>
                      <td>题型及数量</td>
                      <td>抽题数量</td>
                      <td>每题分值</td>
                    </tr>
                    <tr v-if="item1.singleChoiceQuestionCount">
                      <td>单选题 {{ item1.singleChoiceQuestionCount }}题</td>
                      <td>
                        <el-input-number
                          v-model="item1.singleChoice"
                          :step="1"
                          :min="null"
                          :precision="0"
                          @change="setFenshu"
                        />
                      </td>
                      <td>
                        <el-input-number
                          v-model="item1.singleChoiceScore"
                          :step="1"
                          :min="null"
                          :precision="0"
                          @change="setFenshu"
                        />
                      </td>
                    </tr>
                    <tr v-if="item1.multipleChoiceQuestionCount">
                      <td>多选题 {{ item1.multipleChoiceQuestionCount }}题</td>
                      <td>
                        <el-input-number
                          v-model="item1.multipleChoice"
                          :step="1"
                          :min="null"
                          :precision="0"
                          @change="setFenshu"
                        />
                      </td>
                      <td>
                        <el-input-number
                          v-model="item1.multipleChoiceScore"
                          :step="1"
                          :min="null"
                          :precision="0"
                          @change="setFenshu"
                        />
                      </td>
                    </tr>
                  </table>
                </span>
              </div>
            </div>
          </template>
          <!-- <div
            class="content_list"
          >


          </div> -->
          <div class="zhanwei" v-else>
            <span @click="addSubject">+ 选择题库</span>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button @click="() => $router.push('/exam')">取消</el-button>
        <el-button type="primary" @click="sub">保存</el-button>
      </div>
    </el-form>
    <!-- 弹窗 -->
    <el-dialog
      class="tiku"
      v-model="dialogTableVisible"
      title="请选择题库"
    >
      <el-table
        :data="questionBankList"
        :row-key="
          (row) => {
            return row.id;
          }
        "
        ref="multipleTable"
        @selection-change="handleSelectionChange"
      >
        <el-table-column

          type="selection"
          width="55"
          :selectable="selectable"
          :reserve-selection="true"
        />
        <el-table-column property="title" label="题库名称" align="center" />
        <el-table-column
          property="questionCount"
          label="题目数量"
          align="center"
        />
      </el-table>
      <div
        class="dia_btn"
      >
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script >
import {
  questionBankList,
  questionList,
  addPaper,
  paperDetail,
  editPaper,
} from "@/request/service.js";
import { ElMessage } from "element-plus";

export default {
  name: "Topic",
  components: {},
  data() {
    return {
      content_List: [],
      radio: "0",
      tags: [
        { name: "我是已经保存的题库名称", type: "info" },
        { name: "我是已经保存的题库名称", type: "info" },
        { name: "我是已经保存的题库名称", type: "info" },
      ],
      active: 0,
      count: 0,
      duoCount: 0,
      questionBankList: [],
      dialogTableVisible: false,
      cancelSelectAll: [],
      showConetnt: true,
      shezhiFormVisible: false,
      duoShezhiFormVisible: false,
      submitFormVisible: false,
      submitnoFormVisible: false,
      selectAll: [],
      gridData: [],
      checkbox: [],
      form: {
        count: "",
        duoCount: "",
      },
      content_List: {
        title: "",
        multipleQuestions: [],
        singleQuestions: [],
        randomRule: [{}],
        type: 2,
      },
      num: 0,
      rules: {
        name: [
          { required: true, message: "请输入试卷名称", trigger: "change" },
        ],
      },
    };
  },
  async mounted() {
    if (this.$route.query.id) {
    await this.editInit()
    this.getNum()
    }
  },
  methods: {
    selectable(row, index) {
    // 获取当前选中的行
    let selectedRows =[];
    if(this.selectAll){
      selectedRows=[...this.selectAll]
    }
    // // 判断选中的行数是否超过 15
    // if (selectedRows.length >= 15) {
    //   // 超过 15 行，禁止继续选中
    //   return false;
    // } else {
    //   // 未超过 15 行，允许选中
    //   return true;
    // }
    return true
  },
    changeRadio() {
      let singleNum = 0;
      let multipleNum = 0;
      let bankIds = [];
      if (this.radio == 0) {
        this.content_List.randomRule = [{}];
        this.selectAll.forEach((item) => {
          singleNum += item.singleChoiceQuestionCount;
          multipleNum += item.multipleChoiceQuestionCount;
          bankIds.push(item.id);
        });
        this.content_List.randomRule[0].singleChoiceQuestionCount = singleNum;
        this.content_List.randomRule[0].multipleChoiceQuestionCount =
          multipleNum;
        if(singleNum){
          
          this.content_List.randomRule[0].singleChoice = null;
        this.content_List.randomRule[0].singleChoiceScore = null;
        }else{
           this.content_List.randomRule[0].singleChoice = null;
           this.content_List.randomRule[0].singleChoiceScore = null;
        }
        if(multipleNum){
          
          this.content_List.randomRule[0].multipleChoice = null;
        this.content_List.randomRule[0].multipleChoiceScore = null;
        }else{
           this.content_List.randomRule[0].multipleChoice = null;
        this.content_List.randomRule[0].multipleChoiceScore = null;
        }
        this.content_List.randomRule[0].bankIds = bankIds;
      }
      if (this.radio == 1) {
        this.content_List.randomRule = this.selectAll.map((ele, index) => {
          return {
            bankId: ele.id,
            title: ele.title,
            questionCount: ele.questionCount,
            singleChoiceQuestionCount: ele.singleChoiceQuestionCount,
            multipleChoiceQuestionCount: ele.multipleChoiceQuestionCount,
            multipleChoice:null,
            multipleChoiceScore:null,
            singleChoice:  null,
            singleChoiceScore: null,
          };
        });
      }
      this.getNum();
    },
    // 编辑初始详情
    async editInit() {
        let { data } = await paperDetail(this.$route.query.id);
        this.content_List = data;
        this.radio = JSON.stringify(data.scope);
        this.selectAll = data.bankDetailList;
        let singleNum = 0;
        let multipleNum = 0;
        let bankIds = [];
        if (this.radio == 0) {
          this.selectAll.forEach((item) => {
            singleNum += item.singleChoiceQuestionCount;
            multipleNum += item.multipleChoiceQuestionCount;
            bankIds.push(item.id);
          });
          this.content_List.randomRule[0].singleChoiceQuestionCount = singleNum;
          this.content_List.randomRule[0].multipleChoiceQuestionCount =
            multipleNum;
          this.content_List.randomRule[0].bankIds = bankIds;
        }
        if (this.radio == "1") {
          this.content_List.randomRule.forEach((res) => {
            this.selectAll.map((ele, index) => {
              if (res.bankId == ele.id) {
                res.bankId = ele.id;
                res.title = ele.title;
                res.questionCount = ele.title;
                res.singleChoiceQuestionCount = ele.singleChoiceQuestionCount;
                res.multipleChoiceQuestionCount =
                  ele.multipleChoiceQuestionCount;
              }
            });
          });
        }
        this.getNum();
    },

    closeTag(i) {
      if (this.radio == 1) {
        this.selectAll.splice(i, 1);
        this.content_List.randomRule.splice(i, 1);
      }
      if (this.radio == 0) {
        this.selectAll.splice(i, 1);
        let singleNum = 0;
        let multipleNum = 0;
        let bankIds = [];
        this.selectAll.forEach((item) => {
          singleNum += item.singleChoiceQuestionCount;
          multipleNum += item.multipleChoiceQuestionCount;
          bankIds.push(item.id);
        });
        this.content_List.randomRule[0].singleChoiceQuestionCount = singleNum;
        this.content_List.randomRule[0].multipleChoiceQuestionCount = multipleNum;
        this.content_List.randomRule[0].bankIds = bankIds;
        // this.content_List.randomRule.splice(i, 1);
      }
      this.getNum();
    },
    //   设置分数
    setFenshu() {
      this.getNum();
    },
    // 算多少个题和多少分
    getNum() {
      let allNum = 0
      let singleChoice = 0
      let multipleChoice = 0
      console.log(this.content_List.randomRule)
      if(this.content_List.randomRule.length>0){
        this.content_List.randomRule.forEach((e) => {
          allNum += (Number(e.multipleChoice)||0) * (Number(e.multipleChoiceScore)||0) +(Number(e.singleChoice)||0) * (Number(e.singleChoiceScore)||0);
          singleChoice += Number(e.singleChoice)||0;
          multipleChoice += Number(e.multipleChoice)||0;
      });
      this.num=allNum
      this.count=singleChoice
      this.duoCount=multipleChoice
      }
    },
    //   添加题库
    async addSubject() {
      this.dialogTableVisible = true;
      this.$nextTick(async () => {
        var newObj = JSON.parse(JSON.stringify(this.selectAll));
        this.$refs.multipleTable.clearSelection();
        let { data } = await questionBankList();
        this.questionBankList = data;
        newObj.forEach((element) => {
          this.$refs.multipleTable.toggleRowSelection(element, true);
        });
        // this.getNum();
      });
    },
    async sub() {
      if (!this.content_List.title) {
        ElMessage.error("试卷名称不能为空");
        return false;
      }
      if (this.content_List.title.length > 64) {
        ElMessage.error("请输入≤64字的试卷名称");
        return false;
      }
      this.content_List.scope = this.radio;
      this.content_List.mode = 2
      try {
        if (!this.$route.query.id) {
          let res = await addPaper(this.content_List);
          if(res.status==200){
            this.$router.push("/exam");
            this.$emit("sub");
          }
        } else {
          let res = await editPaper(this.content_List);
          if(res.status==200){
            this.$router.push("/exam");
            this.$emit("sub");
          }
        }
      } catch (error) {}
    },
    async checkSub(e, id) {
      this.active = e;
      let res = await questionList(id,0);
      this.gridData = res.data;
    },
    // 取消
    async cancel() {
      this.dialogTableVisible = false;
    },
    // 确定
    submit() {
      let singleNum = 0;
      let multipleNum = 0;
      let bankIds = [];
      if (this.radio == 0) {
        this.selectAll.forEach((item) => {
          singleNum += item.singleChoiceQuestionCount;
          multipleNum += item.multipleChoiceQuestionCount;
          bankIds.push(item.id);
        });
        this.content_List.randomRule[0].singleChoiceQuestionCount = singleNum;
        this.content_List.randomRule[0].multipleChoiceQuestionCount = multipleNum;
        if(singleNum){
          this.content_List.randomRule[0].singleChoice = null
          this.content_List.randomRule[0].singleChoiceScore = null
        }else{
          this.content_List.randomRule[0].singleChoice = null
          this.content_List.randomRule[0].singleChoiceScore = null
        }
        if(multipleNum){
        this.content_List.randomRule[0].multipleChoice = null
        this.content_List.randomRule[0].multipleChoiceScore = null
        }else{
          this.content_List.randomRule[0].multipleChoice = null
          this.content_List.randomRule[0].multipleChoiceScore = null
        }
        this.content_List.randomRule[0].bankIds = bankIds;
      }
      if (this.radio == 1) {
        this.content_List.randomRule = this.selectAll.map((ele, index) => {
          return {
            bankId: ele.id,
            title: ele.title,
            questionCount: ele.questionCount,
            singleChoiceQuestionCount: ele.singleChoiceQuestionCount,
            multipleChoiceQuestionCount: ele.multipleChoiceQuestionCount,
            multipleChoice:null,
            multipleChoiceScore:null,
            singleChoice: null,
            singleChoiceScore: null
          };
        });
      }
      this.dialogTableVisible = false;
      this.showConetnt = false;
      this.getNum();
    },
    // index变化
    changeIndex() {
      this.content_List.singleQuestions.forEach((element, index) => {
        element.questionSort = index + 1;
      });
      this.content_List.multipleQuestions.forEach((element, index) => {
        element.questionSort = index + 1;
      });
    },
    // 多选
    handleSelectionChange(val) {
      this.selectAll = val
      // this.getNum();
    },
    // 设置分数
    shezhi(type) {
      if (type == "dan") {
        this.shezhiFormVisible = true;
      } else {
        this.duoShezhiFormVisible = true;
      }
    },
    filterTag(value, row) {
      return row.type == value;
    },
  },
};
</script>

<style lang="scss" scoped>
.container1 {
   ::v-deep th:first-child .el-checkbox__input:first-child {
    display: none;
  }
  .dia_btn{
     width: 100%;
          display: flex;
          justify-content: center;
          height: 30px;
          position: absolute;
          bottom: 20px;
          left: 0;
  }
  ::v-deep .el-dialog {
    padding: 10px 20px;
    .el-dialog__body {
      .el-table td,
      .el-table th.is-leaf {
        border: none;
      }
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      .table_left {
        width: 20%;
        margin-right: 5px;
        height: 500px;
        .l_header {
          background: #f5f7fa !important;
          padding: 12px 0;
          line-height: 23px;
          text-align: center;
        }
        .l_content {
          padding: 10px 20px;
          text-align: center;
          overflow: auto;
          height: 80%;
          div {
            margin-bottom: 20px;
          }
          div:hover {
            cursor: pointer;
          }
        }
        .active {
          color: #4c90ff;
        }
      }
    }
  }
  ::v-deep .el-table::before {
    height: 0;
  }
  ::v-deep .el-table__body-wrapper {
    overflow-y: auto;
    height: 85%;
  }
  ::v-deep .el-table {
    height: 500px;
  }
  ::v-deep .el-form {
    .el-form-item {
      justify-content: center;
      background: #f2f2f2;
    }
    .el-form-item__content {
      width: 70%;
      flex: none;
    }
    .el-input__inner {
      border: none;
      background: #f2f2f2;
      height: 50px;
      line-height: 50px;
    }
    .el-form-item__label {
      border-right: solid 1px #fff;
      line-height: 50px;
    }
  }

  .content1 {
    display: flex;
    margin-top: 10px;

    .left {
      font-size: 14px;
      width: 15%;
      padding: 20px;
      height: 140px;
      background: #f2f2f2;
      > div {
        background: #fff;
        padding: 10px;
        div {
          display: flex;
          justify-content: space-between;
          line-height: 30px;
        }
      }
      > div:last-child {
        margin-top: 10px;
      }
    }
    .right {
      width: calc(85% - 20px);
      height: 55vh;
      margin-left: 20px;
      background: #f2f2f2;
      padding: 30px;
      overflow: auto;
      > div:first-child {
        margin-bottom: 20px;
      }
      ::v-deep .el-input__inner {
        height: auto !important;
        line-height: unset;
      }
      .radios {
        margin: 10px 0 20px;
      }
      .roles {
        margin: 20px 0;
      }
      .roles_two {
        margin-bottom: 20px;
        > div:first-child {
          margin-bottom: 5px;
        }
        table {
          border-collapse: collapse;
          border: 1px solid #e0e0e0;
          width: 90%;
          margin-bottom: 20px;
          background: #fff;
        }
        tr,
        td {
          border: 1px solid #e0e0e0;
        }
        td {
          width: 33.3%;
          text-align: center;
          padding: 10px 0;
          font-size: 14px;
        }
      }
      .zhanwei {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #169bd5;
      }
      .zhanwei span:hover {
        cursor: pointer;
      }
      .cus:hover {
        cursor: pointer;
      }
      .content_list {
        width: 100%;
        background: #fff;
        padding: 20px;
        box-sizing: border-box;
        padding-bottom: 20px;
        .line {
          width: 100%;
          height: 1px;
          background: #e0e0e0;
          margin: 20px 0;
        }
        .xuanze {
          .shezhi:hover {
            cursor: pointer;
          }
          .title {
            font-size: 16px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            > span:last-child {
              font-size: 12px;
              color: #169bd5;
              margin-left: 20px;
            }
          }
          ::v-deep .el-radio {
            display: block;
            margin-top: 20px;
            .el-radio__label {
              color: black;
            }
          }
          ::v-deep .el-checkbox {
            display: block;
            margin-top: 20px;
            .el-checkbox__label {
              color: black;
            }
          }
        }
        .xuanze_content {
          margin-bottom: 20px;
          font-size: 14px;
          ::v-deep .el-radio-group {
            margin-left: 20px;
          }
          ::v-deep .el-checkbox-group {
            margin-left: 20px;
          }
          .fenzhi {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            div:first-child {
              white-space: nowrap;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
            div:last-child {
              font-size: 14px;
            }
          }
          ::v-deep .el-form-item__content {
            flex: none;
          }
          ::v-deep .el-input-number {
            margin-left: 10px;
          }
        }

        ::v-deep .el-input__inner {
          border: solid 1px #e0e0e0;
          background: none;
          line-height: 32px;
          height: 32px;
        }
      }
    }
  }
  .btn {
    margin-left: calc(15% + 20px + (85% - 20px) / 2 - 110px);
    margin-top: 20px;
    margin-bottom: 20px;
    ::v-deep .el-button {
      padding-left: 40px;
      padding-right: 40px;
    }
  }
}
</style>