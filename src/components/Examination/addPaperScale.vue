<template>
  <!-- 试卷单选设置分数dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="shezhiFormVisible"
      title="批量设置权重"
    >
      <el-form :model="form" ref="ruleForm">
        <el-form-item
          v-for="item in maxOptions"
          :key="item.option"
          :label="item.option"
          :rules="[
            { required: true, message: '单选题权重不能为空', trigger: 'blur' },
            {
              pattern: /^\+?[1-9][0-9]*$/,
              message: '请输入＞0的整数',
              trigger: 'blur',
            },
          ]"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="item.weight"
            placeholder="请输入＞0的整数"
            style="width: 80%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shezhiFormVisible = false">取消</el-button>
          <el-button type="primary" @click="setFenshu('danxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 试卷多选设置分数dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="duoShezhiFormVisible"
      title="批量设置权重"
    >
      <el-form :model="form" ref="ruleForm">
        <el-form-item
          :label="item.option"
          v-for="item in maxMultipleOptions"
          :key="item.option"
          :rules="[
            { required: true, message: '多选题权重不能为空', trigger: 'blur' },
            {
              pattern: /^\+?[1-9][0-9]*$/,
              message: '请输入＞0的整数',
              trigger: 'blur',
            },
          ]"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="item.weight"
            placeholder="请输入＞0的整数"
            style="width: 80%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="duoShezhiFormVisible = false">取消</el-button>
          <el-button type="primary" @click="setFenshu('duoxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 点击确认dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="submitFormVisible"
      title="确认操作"
    >
      试卷提交成功，是否立即发起考试？
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              () => {
                submitFormVisible = false;
                submitnoFormVisible = true;
              }
            "
            >取消</el-button
          >
          <el-button type="primary" @click="a('duoxuan', 'ruleForm')">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <!-- 点击取消dialog -->
  <div>
    <el-dialog
      style="width: 100px"
      v-model="submitnoFormVisible"
      title="确认操作"
    >
      取消不会保存您编辑的数据，确认取消么？
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submitnoFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitnoFormVisible = false">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <div class="container1">
    <el-form :model="content_List" label-width="120px">
      <el-form-item
        label="试卷名称"
        prop="title"
        :rules="[
          { required: true, message: '试卷名称不能为空', trigger: 'blur' },
        ]"
      >
        <el-input
          v-model="content_List.title"
          placeholder="请输入≤64字的试卷名称"
        />
      </el-form-item>
      <el-button type="primary" @click="addSubject">从题库中选择题目</el-button>
      <div class="content1">
        <div class="left">
          <div>
            <div>
              <span>单选题</span>
              <span>{{ count }}题</span>
            </div>
            <div>
              <span>多选题</span>
              <span>{{ duoCount }}题</span>
            </div>
          </div>
        </div>
        <div class="right">
          <div
            class="content_list"
            v-if="
              content_List.singleQuestions.length > 0 ||
              content_List.multipleQuestions.length > 0
            "
          >
            <div class="xuanze" v-if="content_List.singleQuestions.length > 0">
              <div class="title">
                <span>一、单选题</span>
                <span class="shezhi" @click="shezhi('dan')">批量设置权重</span>
              </div>
              <div
                class="xuanze_content"
                v-for="(item, index) in content_List.singleQuestions"
                :key="index"
              >
                <div class="xuanze_title">
                  {{ item.questionSort }}.{{ item.title }}
                </div>
                <el-radio-group v-model="radio">
                  <el-radio
                    :label="item1.option"
                    v-for="(item1, index1) in item.scaleOptions"
                    :key="index1"
                    disabled
                  >
                    <span class="xuanze_check"
                      ><span>{{ item1.option }}.{{ item1.content }}</span>
                      <el-input-number
                        size="small"
                        v-model="item1.weight"
                        :min="0"
                        :precision="0"
                        @change="changeStore()"
                    /></span>
                  </el-radio>
                </el-radio-group>
                <div class="fenzhi">
                  <div>
                    <!-- <span style="color: red; margin-right: 5px">*</span> 分值
                    <el-input-number
                      size="small"
                      v-model="item.questionScore"
                      :min="0"
                      :precision="0"
                      @change="changeStore()"
                    /> -->
                  </div>
                  <div>
                    <span
                      :class="[index != 0 ? 'cus' : '']"
                      :style="{ color: index == 0 ? '#f0f0f0' : '#169bd5' }"
                      @click="index != 0 && moveUpObj(index)"
                      >上移</span
                    ><span
                      style="margin: 0 20px"
                      :class="[
                        index != content_List.singleQuestions.length - 1
                          ? 'cus'
                          : '',
                      ]"
                      :style="{
                        color:
                          index == content_List.singleQuestions.length - 1
                            ? '#f0f0f0'
                            : '#169bd5',
                      }"
                      @click="
                        index != content_List.singleQuestions.length - 1 &&
                          moveDown(index)
                      "
                      >下移</span
                    ><span
                      class="cus"
                      style="color: #169bd5"
                      @click="deletes(item.id, 'single')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
            <div
              class="line"
              v-if="
                content_List.multipleQuestions.length > 0 &&
                content_List.singleQuestions.length > 0
              "
            ></div>
            <div
              class="xuanze"
              v-if="content_List.multipleQuestions.length > 0"
            >
              <div class="title">
                <span>二、多选题</span>
                <span class="shezhi" @click="shezhi('duo')">批量设置权重</span>
              </div>
              <div
                class="xuanze_content"
                v-for="(item, index) in content_List.multipleQuestions"
                :key="index"
              >
                <div class="xuanze_title">
                  {{ item.questionSort }}.{{ item.title }}
                </div>
                <el-checkbox-group v-model="checkbox">
                  <el-checkbox
                    disabled
                    :label="item1.option"
                    v-for="(item1, index1) in item.scaleOptions"
                    :key="index1"
                    ><span class="xuanze_check"
                      ><span>{{ item1.option }}.{{ item1.content }}</span>
                      <el-input-number
                        size="small"
                        v-model="item1.weight"
                        :min="0"
                        :precision="0"
                        @change="changeStore()" /></span
                  ></el-checkbox>
                </el-checkbox-group>
                <div class="fenzhi">
                  <div>
                    <!-- <span style="color: red; margin-right: 5px">*</span> 分值
                    <el-input-number
                      size="small"
                      v-model="item.questionScore"
                      :min="0"
                      :precision="0"
                      @change="changeStore()"
                    />-->
                  </div>
                  <div>
                    <span
                      :class="[index != 0 ? 'cus' : '']"
                      :style="{ color: index == 0 ? '#f0f0f0' : '#169bd5' }"
                      @click="index != 0 && duomoveUpObj(index)"
                      >上移</span
                    ><span
                      style="margin: 0 20px"
                      :class="[
                        index != content_List.multipleQuestions.length - 1
                          ? 'cus'
                          : '',
                      ]"
                      :style="{
                        color:
                          index == content_List.multipleQuestions.length - 1
                            ? '#f0f0f0'
                            : '#169bd5',
                      }"
                      @click="
                        index != content_List.multipleQuestions.length - 1 &&
                          duomoveDown(index)
                      "
                      >下移</span
                    ><span
                      class="cus"
                      style="color: #169bd5"
                      @click="deletes(item.id, 'multiple')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="zhanwei" v-else>
            <span @click="addSubject">+ 从题库中选择题目</span>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button @click="() => $router.push('/exam')">取消</el-button>
        <el-button type="primary" @click="sub">保存</el-button>
      </div>
    </el-form>
    <!-- 弹窗 -->
    <el-dialog
      class="tiku"
      v-model="dialogTableVisible"
      title="从题库中选择题目"
    >
      <div class="table_left">
        <div class="l_header">题库名称</div>
        <div class="l_content">
          <div
            v-for="(item, index) in questionBankList"
            :class="[active == index ? 'active' : '']"
            @click="checkSub(index, item.id)"
            :key="index"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <el-table
        :data="gridData"
        :row-key="
          (row) => {
            return row.id;
          }
        "
        ref="multipleTable"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :reserve-selection="true"
        />
        <el-table-column property="title" label="题目" align="center" />
        <el-table-column
          property="type"
          label="题型"
          align="center"
          :filters="[
            { text: '单选题', value: '1' },
            { text: '多选题', value: '2' },
          ]"
          :filter-method="filterTag"
          filter-placement="bottom-end"
        >
          <template #default="scope">
            <div
              :type="scope.row.type == '1' ? '单选题' : '多选题'"
              disable-transitions
            >
              {{ scope.row.type == "1" ? "单选题" : "多选题" }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div
        style="
          width: 100%;
          display: flex;
          justify-content: center;
          height: 30px;
        "
      >
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script >
import {
  questionBankList,
  questionList,
  addPaper,
  paperDetail,
  editPaper,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
export default {
  name: "Topic",
  data() {
    return {
      active: 0,
      count: 0,
      duoCount: 0,
      questionBankList: [],
      dialogTableVisible: false,
      cancelSelectAll: [],
      showConetnt: true,
      shezhiFormVisible: false,
      duoShezhiFormVisible: false,
      submitFormVisible: false,
      submitnoFormVisible: false,
      selectAll: [],
      gridData: [],
      checkbox: [],
      form: {
        count: "",
        duoCount: "",
      },
      content_List: {
        title: "",
        multipleQuestions: [],
        singleQuestions: [],
        type: 1,
        randomRule: [],
      },
      num: 0,
      rules: {
        name: [
          { required: true, message: "请输入试卷名称", trigger: "change" },
        ],
      },
      maxOptions: [],
      maxMultipleOptions: [],
    };
  },
  async mounted() {
    if (this.$route.query.id) {
      let detail = await paperDetail(this.$route.query.id);
      this.content_List = detail.data;
      this.getNum();
    }
    this.findMaxOptionsCount(this.content_List.singleQuestions)
    this.findMaxOptionsMultiple(this.content_List.multipleQuestions)
  },
  methods: {
    // 修改分数
    changeStore() {
      this.getNum();
    },
    // 查询更多的选项
    findMaxOptionsCount(questions) {
      let maxOptionsCount = 0;
      let maxOptions;

      questions.forEach((question) => {
        const optionsCount = question.scaleOptions.length;
        if (optionsCount > maxOptionsCount) {
          maxOptionsCount = optionsCount;
          maxOptions = question.scaleOptions.map((item) => {
            return { option: item.option, questionScore: item.questionScore };
          });
        }
      });

      if (this.maxOptions&&this.maxOptions.length > 0) {
        maxOptions.forEach((option) => {
          const existingOption = this.maxOptions.find(
            (item) => item.option === option.option
          );
          if (!existingOption) {
            this.maxOptions.push(option);
          }
        });

        this.maxOptions = this.maxOptions.filter((option) => {
          return maxOptions.some((item) => item.option === option.option);
        });
      } else {
        this.maxOptions = maxOptions;
      }
    },
    // 查询多选题更多的选项
    findMaxOptionsMultiple(questions) {
      let maxOptionsCount = 0;
      let maxOptions;

      questions.forEach((question) => {
        const optionsCount = question.scaleOptions.length;
        if (optionsCount > maxOptionsCount) {
          maxOptionsCount = optionsCount;
          maxOptions = question.scaleOptions.map((item) => {
            return { option: item.option, questionScore: item.questionScore };
          });
        }
      });

      if (this.maxMultipleOptions&&this.maxMultipleOptions.length > 0) {
        maxOptions.forEach((option) => {
          const existingOption = this.maxMultipleOptions.find(
            (item) => item.option === option.option
          );
          if (!existingOption) {
            this.maxMultipleOptions.push(option);
          }
        });

        this.maxMultipleOptions = this.maxMultipleOptions.filter((option) => {
          return maxOptions.some((item) => item.option === option.option);
        });
      } else {
        this.maxMultipleOptions = maxOptions;
      }
    },
    //  批量添加
    assignCountToOptions(arr, questions) {
      questions.forEach((question) => {
        question.scaleOptions.forEach((option) => {
          const matchingOption = arr.find(
            (item) => item.option === option.option
          );
          if (matchingOption) {
            option.questionScore = matchingOption.questionScore;
            option.weight = Number(matchingOption.weight);
          }
        });
      });

      return questions;
    },

    //   设置分数
    setFenshu(type, formName) {
      if (type == "danxuan") {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            console.log(this.maxOptions)
           for(let i = 0;i<this.maxOptions.length;i++){
              if(!this.maxOptions[i].weight){
                ElMessage.error("权重不能为空");
               return false
             }
            }
            this.content_List.singleQuestions = this.assignCountToOptions(
              this.maxOptions,
              this.content_List.singleQuestions
            );
            this.shezhiFormVisible = false;
          } else {
            return false;
          }
        });
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
             console.log(this.maxMultipleOptions)
            for(let i = 0;i<this.maxMultipleOptions.length;i++){
              if(!this.maxMultipleOptions[i].weight){
                ElMessage.error("权重不能为空");
               return false
             }
            }
            this.content_List.multipleQuestions = this.assignCountToOptions(
              this.maxMultipleOptions,
              this.content_List.multipleQuestions
            );
            this.duoShezhiFormVisible = false;
          } else {
            return false;
          }
        });
      }
      this.getNum();
    },
    //上移
    moveUpObj(index) {
      let arr = this.content_List.singleQuestions;
      arr.splice(index - 1, 1, ...arr.splice(index, 1, arr[index - 1]));
      this.changeIndex();
    },
    //上移
    duomoveUpObj(index) {
      let arr = this.content_List.multipleQuestions;
      arr.splice(index - 1, 1, ...arr.splice(index, 1, arr[index - 1]));
      this.changeIndex();
    },
    // 下移
    moveDown(index) {
      let arr = this.content_List.singleQuestions;
      arr.splice(index, 1, ...arr.splice(index + 1, 1, arr[index]));
      this.changeIndex();
    },
    //上移
    duomoveDown(index) {
      let arr = this.content_List.multipleQuestions;
      arr.splice(index, 1, ...arr.splice(index + 1, 1, arr[index]));
      this.changeIndex();
    },
    //  删除
    deletes(id, type) {
      this.selectAll.forEach((element, index) => {
        if (element.id == id) {
          this.$refs.multipleTable.toggleRowSelection(element, undefined);
        }
      });

      if (type == "single") {
        this.content_List.singleQuestions.forEach((element, index) => {
          if (element.id == id) {
            this.content_List.singleQuestions.splice(index, 1);
          }
        });
        this.findMaxOptionsCount(this.content_List.singleQuestions);
        this.changeIndex();
      } else {
        this.content_List.multipleQuestions.forEach((element, index) => {
          if (element.id == id) {
            this.content_List.multipleQuestions.splice(index, 1);
          }
        });
        this.findMaxOptionsMultiple(this.content_List.multipleQuestions);

        this.changeIndex();
      }
      this.getNum();
    },
    // 算多少个题和多少分
    getNum() {
      let danxuanNum = 0;
      let duoxuanNum = 0;
      this.duoCount = this.content_List.multipleQuestions.length;
      this.count = this.content_List.singleQuestions.length;
      this.content_List.multipleQuestions.forEach((element) => {
        duoxuanNum += parseInt(element.questionScore);
      });
      this.content_List.singleQuestions.forEach((element) => {
        danxuanNum += parseInt(element.questionScore);
      });
      this.num = parseInt(danxuanNum) + parseInt(duoxuanNum);
    },
    //   添加题库
    async addSubject() {
      this.dialogTableVisible = true;

      this.$nextTick(async () => {
        this.$refs.multipleTable.clearSelection();
        this.content_List.multipleQuestions.forEach((element) => {
          this.selectAll.push(element);
        });
        this.content_List.singleQuestions.forEach((element) => {
          this.selectAll.push(element);
        });

        let { data } = await questionBankList(1);
        this.questionBankList = data;
        let res = await questionList(data[this.active].id,1);
        this.gridData = res.data;
        this.getNum();
        this.selectAll.forEach((element) => {
          this.$refs.multipleTable.toggleRowSelection(element, true);
        });
      });
    },
    async sub() {
     console.log(this.content_List)

      if (!this.content_List.title) {
        ElMessage.error("试卷名称不能为空");
        return false;
      }
      if (this.content_List.title.length > 64) {
        ElMessage.error("请输入≤64字的试卷名称");
        return false;
      }
      if(this.content_List.singleQuestions.length){
          for (let i = 0; i < this.content_List.singleQuestions.length; i++) {
        for(let j = 0; j < this.content_List.singleQuestions[i].scaleOptions.length; j++){
          if (!this.content_List.singleQuestions[i].scaleOptions[j].weight) {
              ElMessage.error("单选选项权重不能为空");
              return false;
            }
        }
      }
      }
     
      for (let i = 0; i < this.content_List.multipleQuestions.length; i++) {
        for(let j = 0; j < this.content_List.multipleQuestions[i].scaleOptions.length; j++){
           if (!this.content_List.multipleQuestions[i].scaleOptions[j].weight) {
          ElMessage.error("多选选项权重不能为空");
          return false;
        }
      }
      }
      try {
        if (!this.$route.query.id) {
          this.content_List.mode=1
          let res = await addPaper(this.content_List);
          this.$router.push("/exam");
          this.$emit("sub");
        } else {
           this.content_List.mode=1
          let res = await editPaper(this.content_List);
          this.$router.push("/exam");
          this.$emit("sub");
        }
      } catch (error) {}
    },
    async checkSub(e, id) {
      this.active = e;
      let res = await questionList(id,1);
      this.gridData = res.data;
    },
    // 取消
    async cancel() {
      this.dialogTableVisible = false;
    },
    // 确定
    submit() {
      // 单选集合
      this.content_List.singleQuestions = [];
      // 多选集合
      this.content_List.multipleQuestions = [];
      this.selectAll.forEach((element, index) => {
        if (element.type == 1) {
          if (!element.questionScore) {
            element.questionScore = 0;
          }
          element.scaleOptions=element.scaleOptions||element.options
          element.questionSort = index + 1;
          this.content_List.singleQuestions.push(element);
          this.findMaxOptionsCount(this.content_List.singleQuestions);
        } else {
          if (!element.questionScore) {
            element.questionScore = 0;
          }
          element.questionSort = index + 1;
          element.scaleOptions=element.scaleOptions||element.options
          this.content_List.multipleQuestions.push(element);
          console.log(this.content_List.multipleQuestions)

          this.findMaxOptionsMultiple(this.content_List.multipleQuestions);
        }
        this.changeIndex();
      });
      this.getNum();
      this.dialogTableVisible = false;
      this.showConetnt = false;
    },
    // index变化
    changeIndex() {
      this.content_List.singleQuestions.forEach((element, index) => {
        element.questionSort = index + 1;
      });
      this.content_List.multipleQuestions.forEach((element, index) => {
        element.questionSort = index + 1;
      });
    },
    // 多选
    handleSelectionChange(val) {
      let selectInitAll = JSON.parse(JSON.stringify(this.selectAll));//老的
      this.selectAll = val;//新的
      this.selectAll.forEach(element => {
          selectInitAll.forEach(row => {
              if(element.id==row.id){
                element.scaleOptions = row.scaleOptions
              }
          });
      });
      console.log(this.selectAll)
      this.getNum();
    },
    // 设置分数
    shezhi(type) {
      if (type == "dan") {
        this.shezhiFormVisible = true;
        this.maxOptions=[]
        this.findMaxOptionsCount(this.content_List.singleQuestions)
      } else {
        this.duoShezhiFormVisible = true;
        this.maxMultipleOptions=[]
    this.findMaxOptionsMultiple(this.content_List.multipleQuestions)
      }
    },
    filterTag(value, row) {
      return row.type == value;
    },
  },
};
</script>

<style lang="scss" scoped>
.container1 {
  ::v-deep .el-dialog {
    padding: 10px 20px;
    .el-dialog__body {
      .el-table td,
      .el-table th.is-leaf {
        border: none;
      }
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      .table_left {
        width: 20%;
        margin-right: 5px;
        height: 500px;
        .l_header {
          background: #f5f7fa !important;
          padding: 12px 0;
          line-height: 23px;
          text-align: center;
        }
        .l_content {
          padding: 10px 20px;
          text-align: center;
          overflow: auto;
          height: 80%;
          div {
            margin-bottom: 20px;
          }
          div:hover {
            cursor: pointer;
          }
        }
        .active {
          color: #4c90ff;
        }
      }
    }
  }
  ::v-deep .el-table::before {
    height: 0;
  }
  ::v-deep .el-table__body-wrapper {
    overflow-y: auto;
    height: 85%;
  }
  ::v-deep .el-table {
    height: 500px;
    width: 78%;
  }
  ::v-deep .el-form {
    .el-form-item {
      justify-content: center;
      background: #f2f2f2;
    }
    .el-form-item__content {
      width: 70%;
      flex: none;
    }
    .el-input__inner {
      border: none;
      background: #f2f2f2;
      line-height: 50px;
      height: 50px;
    }
    .el-form-item__label {
      border-right: solid 1px #fff;
      line-height: 50px;
    }
  }

  .content1 {
    display: flex;
    margin-top: 10px;

    .left {
      font-size: 14px;
      width: 15%;
      padding: 20px;
      height: 100px;
      background: #f2f2f2;
      > div {
        background: #fff;
        padding: 10px;
        div {
          display: flex;
          justify-content: space-between;
          line-height: 30px;
        }
      }
      > div:last-child {
        margin-top: 10px;
      }
    }
    .right {
      width: calc(85% - 20px);
      height: 55vh;
      margin-left: 20px;
      background: #f2f2f2;
      padding: 30px;
      overflow: auto;
      .zhanwei {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #169bd5;
      }
      .zhanwei span:hover {
        cursor: pointer;
      }
      .cus:hover {
        cursor: pointer;
      }
      .content_list {
        width: 100%;
        background: #fff;
        padding: 20px;
        box-sizing: border-box;
        padding-bottom: 20px;
        .line {
          width: 100%;
          height: 1px;
          background: #e0e0e0;
          margin: 20px 0;
        }
        .xuanze {
          .shezhi:hover {
            cursor: pointer;
          }
          .title {
            font-size: 16px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            > span:last-child {
              font-size: 12px;
              color: #169bd5;
              margin-left: 20px;
            }
          }
          ::v-deep .el-radio {
            display: flex;
            margin-top: 20px;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            .el-radio__label {
              color: black;
              width: 100%;
            }
          }
          ::v-deep .el-checkbox {
            display: flex;
            margin-top: 20px;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            .el-checkbox__label {
              color: black;
              width: 100%;
            }
          }
        }
        .xuanze_content {
          margin-bottom: 20px;
          font-size: 14px;
          ::v-deep .el-radio-group {
            margin-left: 20px;
            width: 100%;
            padding-right: 20px;
            box-sizing: border-box;
          }
          ::v-deep .el-checkbox-group {
            margin-left: 20px;
          }
          .xuanze_check {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .fenzhi {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            div:first-child {
              white-space: nowrap;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
            div:last-child {
              font-size: 14px;
            }
          }
          ::v-deep .el-form-item__content {
            flex: none;
          }
          ::v-deep .el-input-number {
            margin-left: 10px;
          }
        }

        ::v-deep .el-input__inner {
          border: solid 1px #e0e0e0;
          background: none;
          line-height: 32px;
          height: 32px;
        }
      }
    }
  }
  
  .btn {
    margin-left: calc(15% + 20px + (85% - 20px) / 2 - 110px);
    margin-top: 20px;
    ::v-deep .el-button {
      padding-left: 40px;
      padding-right: 40px;
    }
  }
}

</style>