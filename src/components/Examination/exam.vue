<template>
  <div class="header">
    <div class="left">
      <div class="col">
        <el-input
          v-model="title"
          clearable
          placeholder="请输入考试名称"
          @keydown.enter="init"
        ></el-input>
      </div>
      <div class="col">
        <el-date-picker
          v-model="lineTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="创建开始时间"
          end-placeholder="创建结束时间"
          :default-time="defaultTime"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </div>

    </div>
    <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
  </div>
   <!-- 批量设置分类弹框 -->
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
  <div class="operate">
    <el-button type="primary" @click="add" v-if="btnList.add_examination_btn"
      >新建考试</el-button
    >
    <el-button v-if="btnList.batchSetTag" @click="setClass">批量设置分类</el-button>
    <el-button @click="filterList()" :type="btnStatus ? 'primary' : ''"
          >未设置分类</el-button
        >
  </div>

  <div class="table">
    <el-table
      ref="multipleTable"
      border
      :data="tableData"
      tooltip-effect="dark"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="考试ID" width="60" align="center">
      </el-table-column>
      <el-table-column
        prop="title"
        show-overflow-tooltip="true"
        label="考试名称"
        min-width="150"
      >
      <template #default="scope">
        <span class="cur" v-if="scope.row.type==1" @click="toPreview(scope.row)">{{scope.row.title}}</span>
         <span  v-if="scope.row.type==2" >{{scope.row.title}}</span>
      </template>

      </el-table-column>
      <el-table-column
        prop="property"
        label="试卷类型"
        align="center"
        :filters="[
          { text: '固定练习', value: '固定练习' },
          { text: '随机练习', value: '随机练习' },
          { text: '随机考试', value: '随机考试' },
          { text: '固定考试', value: '固定考试' },
          { text: '固定量表', value: '固定量表' },
        ]"
        :filter-method="filterHandler"
      >
        <template #default="scope">
          {{ scope.row.property }}试卷
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" align="center" min-width="90">
      </el-table-column>
      <el-table-column prop="createdName" label="创建人" align="center" min-width="70">
      </el-table-column>

      <el-table-column prop="startTime" label="开始时间" align="center"  min-width="90">
      </el-table-column>
      <el-table-column prop="endTime" label="结束时间" align="center" min-width="90">
      </el-table-column>
      <el-table-column
        prop="state"
        label="考试状态"
        align="center"
        min-width="90"
        :filters="[
          { text: '草稿', value: '0' },
          { text: '待开始', value: '1' },
          { text: '进行中', value: '2' },
          { text: '已结束', value: '3' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <div disable-transitions>
            {{
              scope.row.state == "0"
                ? "草稿"
                : scope.row.state == "1"
                ? "待开始"
                : scope.row.state == "2"
                ? "进行中"
                : "已结束"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="examineesCount" label="已考人数" align="center" min-width="70">
      </el-table-column>
      <el-table-column label="操作" min-width="180" width="250" align="center">
        <template #default="scope">
          <el-button
            type="default"
            id="copy"
            @click="copy(scope.row)"
            ><el-icon class="el-icon--center" ><CopyDocument /></el-icon></el-button
          >
          <el-button
            @click="editPaper(scope.row)"
            type="default"
            :disabled="scope.row.state == '2' || scope.row.state == '3'"
            v-if="btnList.edit_examination_btn"
          >
          <el-icon class="el-icon--center" ><Edit /></el-icon>
          </el-button>
          <el-button
            @click="copyPaper(scope.row)"
            type="default"
            v-if="btnList.edit_examination_btn"
          >
          <el-icon><DocumentCopy /></el-icon>
          </el-button>

         
          <el-button   style="margin-left:10px;"  type="default"  v-if="btnList.delete_examination_btn" :disabled="scope.row.state == '2' || scope.row.state == '3'"
            @click="del(scope.row.id)"><el-icon class="el-icon--center"  ><Delete /></el-icon></el-button>

        </template>
      </el-table-column>
      <el-table-column label="高级操作" width="200px" align="center">
        <template #default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="searchExamPeizhi(scope.row)"
            v-if="btnList.view_examination_btn"
            >查看配置</el-button
          >
          <el-button size="mini" type="primary" :disabled="scope.row.state == 0||scope.row.state == 1" v-if="btnList.view_record_list_btn" @click="searchExam(scope.row)"
            >查看考卷
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="确认操作"
    width="30%"
    :before-close="handleClose"
  >
    <span>确定删除该考试么?</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="delQuestionBank"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
    <h3>确定删除这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveDelete" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog> -->
</template>

<script>
import { addQuestionBank, examList, deleteExam ,copy} from "@/request/service.js";
import filterList from "@/mixins/filterList.js";
import topicModule from "@/mixins/topicModule.js";
import { Edit, Delete ,CopyDocument ,DocumentCopy} from "@element-plus/icons";
import clipboard from 'clipboard'
import classifyDialog from "../classifyDialog.vue";
import { ElMessage } from "element-plus";
// import dayjs from "dayjs";
export default {
  name: "Topic",
  emits: ["goDetail"],
  mixins: [topicModule,filterList],
  components: {
    Edit,
    Delete,
    CopyDocument,
    classifyDialog,
    DocumentCopy
  },
  data() {
    return {
      delId: "",
      title: "",
      lineTime: "",
      bankName: "",
      currentPage: 1,
      dialogVisible: false,
      pageSize: 100,
      total: 0,
      bankVisible: false,
      choseData: [],
      ClassifyData:[],

      // currentChose: {},
      tableData: [],
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
      refesh:false
    };
  },
  mounted() {
    this.init();

  },
  methods: {
    filterHandler(value, row, column) {
      const property = column["property"];
      return row[property] === value;
    },
    set(){
      this.init()
    },
    handleSelectionChange(selection) {
          this.choseData = selection
          this.courseIds=this.choseData.map((res)=>{
            return res.id
          })
    },
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据！')
        return
      }
      this.$refs.classifyRef.openDialog('exam_paper')
      this.$refs.classifyRef.gitClassList()
      // this.initTag()
      // this.editVisible = true
    },
    filterTag(value, row) {
      return row.state == value;
    },
    // 复制
    copy(row){
       let _this = this
      let clipborad = new clipboard('#copy',{
         text: function(trigger) {
			        return window.location.origin + `/explain?examId=${row.encodeId}`;
			    }
      })
      //复制成功
      clipborad.on('success', function(e) {
        _this.$message.success('复制成功！')
      clipborad.destroy()
      })
      //复制失败
      clipborad.on('error', function(e) {
        _this.$message.error('复制失败！')
        clipborad.destroy()
      })
    },
    async init() {
      if(this.choseData.length>0){
            this.refesh=true
          }
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        startTime: publishedStartTime,
        endTime: publishedEndTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
        classifyIds:this.ClassifyValue
      };
      examList(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data;
        this.filterData = this.tableData;
        this.btnStatus = false;
      });
    },
    toPreview(row){
       window.open(window.location.origin + `/examPreview?examId=${row.encodeId}`);
    },
    add() {
      this.$emit("types", "addAdnEdit");
    },
    // 编辑页面
    editPaper(row) {
      this.$emit("types", "addAdnEdit", row);
    },
    // 复制考试
    async copyPaper(row){
     let res =await copy(row.id)
     this.$router.push(`/exam?type=addAdnEdit&id=${res.data}`)
    },
    // 查看配置
    searchExamPeizhi(row) {
      this.$emit("types", "addAdnEdit", "searchSet", row.id);
    },
    reset() {
      this.title = "";
      this.name = "";
      this.lineTime = "";
      this.init();
      // this.$refs.multipleTable.clearSelection();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 添加题库并跳转
    async goDetailPage() {
      let params = {
        title: this.bankName,
      };
      let res = await addQuestionBank(params);
      if (res.status === 200) {
        this.$message.success("添加考试成功");
        this.$emit("goDetail", res.data);
      }
    },
    // 查看考卷
    searchExam(row) {
      this.$emit("types", "search",row);
    },
    del(id) {
      this.delId = id;
      this.dialogVisible = true;
    },
    // 删除题库
    async delQuestionBank(id) {
      let params = {
        id: this.delId,
      };
      let res = await deleteExam(params);
      if (res.status === 200) {
        this.$message.success("删除考试成功");
        this.dialogVisible = false;
        this.init();
      }
    },
    // 题库详情页
    editQuestionBank(data) {
      this.$emit("goDetail", data);
    },
    outBank() {
      this.bankName = "";
      this.bankVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.cur{
  color:#1890FF
}
.cur:hover{
  cursor: pointer;
}
.header {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .left {
    display: flex;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .tl {
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
    }
  }
}
.table {
  ::v-deep .el-icon:hover{
    cursor: pointer;
  }
 
}
.dialog {
  // .col {
  //   display: flex;
  //   align-items: center;
  //   margin-right: 20px;
  // }
  .tl {
    width: 25%;
    opacity: 1;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    margin-left: 50px;
    white-space: nowrap;
  }
}
.dialog-footer {
  text-align: center;
}
::v-deep {
  .el-table__body-wrapper .el-table__row {
    td:nth-child(2) {
      text-align: left;
      .cell {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
</style>

