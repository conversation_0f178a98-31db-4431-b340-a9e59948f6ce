<template>
 <div class="questionBank">
    <bank-index @goDetail="goDetailPage" v-if="type==='bank'"></bank-index>
    <bank-detailPage
      @goRecognition="goRecognition"
      :edit="bankName"
      v-else-if="type==='detailPage'"
    ></bank-detailPage>
    <bank-textRecognition
      :edit="bankName"
      @goDetailPage="goDetailPage"
      v-else-if="type==='textRecognition'"
    ></bank-textRecognition>
  </div>
   <!-- @back="type = 'bank'" -->
   
</template>

<script>
import bankIndex from "@/components/Topic/index";
import bankDetailPage from "@/components/Topic/detailPage";
import bankTextRecognition from "@/components/Topic/textRecognition";
export default {
  
  name: "questionBank",
  components: {
    bankIndex,
    bankDetailPage,
    bankTextRecognition
  },
  data() {
    return {
      type: 'bank',
      bankName: "",
      id:''
    };
  },
  watch: {
    $route() {
      if(JSON.stringify(this.$route.query)!=='{}'){
        this.type = this.$route.query?.type
        this.bankName = {
          id:this.$route.query?.id,
          title:this.$route.query?.title,
        }
      }else {
        this.type = 'bank'
      }
    }
  },
  created(){
    this.estimateType()
  },
  mounted() {},
  methods: {
    // 去题目列表页
    goDetailPage(obj) {
      // 记录当前题库
      this.$router.push({path:'/exam',query:{id:obj.id , title:obj.title,type:'detailPage'}})
      this.type = 'detailPage'
      this.bankName = obj
    },
    // 返回题库列表页
    goTopicBank(){
      this.$router.push({path:'/exam'})
      this.type = 'bank'
    },
    // 去文本识别页
    goRecognition(obj){
      this.$router.push({path:'/exam',query:{id:obj.id , title:obj.title,type:'textRecognition'}})
      this.type = 'textRecognition'
      this.bankName = obj
    },
    // 判断当前所在页面
    estimateType(){
     if(JSON.stringify(this.$route.query)!=='{}'){
      this.type = this.$route.query?.type
      this.bankName = {
        id:this.$route.query?.id,
        title:this.$route.query?.title,
      }
     }
    }
  },
};
</script>

<style scoped lang="scss">
.questionBank {
}
</style>
