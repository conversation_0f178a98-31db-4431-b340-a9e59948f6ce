<template>
<div class="title">
           {{form.title}} <span>{{form.state == "0"
                ? "草稿"
                : form.state == "1"
                ? "待开始"
                : form.state == "2"
                ? "进行中"
                : "已结束"}}</span>
      </div>
  <div class="header">
      
    <div class="left">
        <el-button @click="exportLog"  style="margin-right:10px;" v-if="btnList.export_record_btn">导出记录  </el-button><span >共{{form.examineesCount}}人<span v-if="form.passScore||form.notPassCount">，通过考试{{form.passCount}}人</span><span v-if="form.passScore||form.notPassCount">，未通过{{form.notPassCount}}人</span></span>
    </div>
    <div class="right">
         <el-input v-model="userName" clearable placeholder="输入考生姓名搜索" style="margin-right:20px;" @keydown.enter="init"></el-input>
      <el-button @click="init" type="primary">查询</el-button>
    </div>
  </div>

  <div class="table">
    <el-table
      ref="multipleTable"
      border
      :data="tableData"
      tooltip-effect="dark"
    >

      <el-table-column prop="userName" label="姓名" >
      </el-table-column>
      <el-table-column prop="realName" label="真实姓名" >
        <template #default="scope">
          {{scope.row.realName?scope.row.realName:'--'}}
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机号" >
      </el-table-column>
      <el-table-column prop="roleName" label="用户组" >
      </el-table-column>
      <el-table-column prop="finishTime" label="完成时间" >
      </el-table-column>
      <el-table-column prop="score" label="成绩" >
      </el-table-column>
      <el-table-column prop="costTime" label="考试用时" >
      </el-table-column>
       <el-table-column prop="type" label="状态" align="center">
          <template #default="scope">
            {{scope.row.isPass}}
          </template>
      </el-table-column>
      <el-table-column label="操作" width="200px"  align="center" >
        <template #default="scope">
         <span type="primary" class="cur"  @click="toPreview(scope.row)" v-if="btnList.view_record_btn">查看考卷</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>

  <!-- <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
    <h3>确定删除这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveDelete" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog> -->
</template>

<script>
import {
  examRecordList
} from "@/request/service.js";
import {
  examExport
} from "@/request/download.js";
import topicModule from "@/mixins/topicModule.js";

import { ElMessage } from "element-plus";
// import dayjs from "dayjs";
export default {
  name: "Topic",
  emits:['goDetail'],
  mixins: [topicModule],
  data() {
    return {
      title: "",
      lineTime: "",
      bankName:'',
      currentPage: 1,
      pageSize: 100,
      total: 0,
      bankVisible: false,
      form:{},
      userName:"",
      // choseData: [],
      // currentChose: {},
      tableData: [],
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
    };
  },
  mounted() {
    this.form=JSON.parse(decodeURI(this.$route.query.data))
    this.init();
  },
  methods: {
    toPreview(row){
       window.open(window.location.origin + `/admin#/examResult?examId=${this.form.encodeId}&examUserId=${row.examUserId}&userId=${row.userId}&userName=${row.userName}`);
    },
    async init() {
      let params = {
       examId: this.form.id,
       pageIndex:this.currentPage,
       pageSize:this.pageSize,
       userName: this.userName
      };
      examRecordList(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data.examineesRecordList
        this.form.examineesCount=res.data.examineesCount
        this.form.passCount=res.data.passCount
        this.form.notPassCount=res.data.notPassCount
      });
   
    },

    // 编辑页面
    editPaper(row){
      this.$emit("edit",row)
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 导出
    exportLog(){
      examExport(this.form.id).then((res)=>{
        if(res.size==57){
          ElMessage.warning("暂无数据导出")
          return
        }
          let name = this.form.title+".xlsx"
          let link = document.createElement('a')
          link.href = window.URL.createObjectURL(res)
          link.download = name
          link.click()
      })
    },
    // 查看考卷
    searchExam(){
      this.$emit("types",'search')
    },
  },
};
</script>

<style scoped lang="scss">
.cur{
  color:#1890FF
}
.cur:hover{
  cursor: pointer;
}
.header {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .left {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666666;
  }
  .right{
      display: flex;
  }
}
.table{
    ::v-deep .el-link.el-link--primary{
        color: #66b1ff;
    }
}
.dialog{
    // .col {
    //   display: flex;
    //   align-items: center;
    //   margin-right: 20px;
    // }
     .tl {
        width: 25%;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        margin-left: 50px;
        white-space: nowrap;
      }
}
.dialog-footer{
  text-align: center;
}
::v-deep{
  .el-table__body-wrapper .el-table__row {
    td:nth-child(2){
      text-align: left;
      .cell{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  } 
}
.title{
    margin-top: 20px;
    color: #333333;
    font-size: 25px;
    span{
        font-size: 14px;
        display: inline-block;
        padding: 5px 10px;
        background: #F2F2F2;
    }
}
</style>
