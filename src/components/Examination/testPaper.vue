<template>
  <el-dialog v-model="dialog" title="创建试卷" width="600px">
    <div class="dialog_box">
      <div>选择试卷类型</div>
      <el-radio-group v-model="radio1" class="ml-4">
        <el-radio label="1" size="large"
          >固定试卷
          <div class="r_title">
            选择题库中的题目组成试卷，参与考试的人题目都一样
          </div>
          <div class="r_title" v-if="radio1==1">
            <el-radio-group v-model="radio2" class="ml-4">
              <el-radio label="1" size="large">考试类</el-radio>
              <el-radio label="3" size="large">练习题</el-radio>
              <el-radio label="2" size="large">量表类</el-radio>
            </el-radio-group>
          </div>
        </el-radio>
        <el-radio label="2" size="large"
          >随机试卷
          <div class="r_title">
            设置抽题规则，考试发布时每个人随机分配不同的题目
          </div>
          <div class="r_title" v-if="radio1==2">
            <el-radio-group v-model="radio3" class="ml-4">
              <el-radio label="1" size="large">考试类</el-radio>
              <el-radio label="2" size="large">练习题</el-radio>
            </el-radio-group>
          </div>
          </el-radio
        >
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog = false">取消</el-button>
        <el-button type="primary" @click="addTrue"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <div class="header">
    <div class="left">
      <div class="col">
        <el-input
          v-model="title"
          clearable
          placeholder="请输入试卷名称"
          @keydown.enter="init"
        ></el-input>
      </div>
      <div class="col">
        <el-date-picker
          v-model="lineTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="创建开始时间"
          end-placeholder="创建结束时间"
          :default-time="defaultTime"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="right">
      <el-button @click="init" icon="el-icon-search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
  </div>
  <div class="operate">
    <el-button type="primary" @click="add" v-if="btnList.add_examination_paper"
      >新建试卷</el-button
    >
  </div>

  <div class="table">
    <el-table
      ref="multipleTable"
      border
      :data="tableData"
      tooltip-effect="dark"
    >
      <el-table-column prop="id" label="试卷ID" width="80" align="center">
      </el-table-column>
      <el-table-column prop="title" label="试卷名称"> </el-table-column>
      <el-table-column
        prop="property"
        label="试卷类型"
        align="center"
        :filters="[
          { text: '固定练习', value: '固定练习' },
          { text: '随机练习', value: '随机练习' },
          { text: '随机考试', value: '随机考试' },
          { text: '固定考试', value: '固定考试' },
          { text: '固定量表', value: '固定量表' },
        ]"
        :filter-method="filterHandler"
      >
        <template #default="scope">
          {{ scope.row.property }}试卷
        </template>
      </el-table-column>
      <el-table-column prop="type" label="题库类型"> 
         <template #default="scope">
           {{scope.row.mode==1?'量表类':'考试类'}}
         </template>
      </el-table-column>

      <el-table-column prop="questionCount" label="题目数量" align="center">
        <template #default="scope">
          {{scope.row.questionCount?scope.row.questionCount:'--'}}
        </template>
      </el-table-column>
      <el-table-column prop="totalScore" label="总分" align="center">
        <template #default="scope">
          {{scope.row.totalScore?scope.row.totalScore:'--'}}
        </template>
      </el-table-column>
      <el-table-column prop="createdName" label="创建人" align="center">
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" align="center">
      </el-table-column>
      <el-table-column label="操作" width="300px" align="center">
        <template #default="scope">
          <el-button
            size="mini"
            type="primary"
            v-if="btnList.paper_view_btn && scope.row.type == 1"
            @click="toPreview(scope.row)"
            >预览</el-button
          >
          <el-button
            size="mini"
            type="primary"
            v-if="btnList.paper_edit_btn"
            @click="editPaper(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            plain
            type="danger"
            v-if="btnList.paper_remove_btn"
            @click="del(scope.row.id)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="删除确认"
    width="30%"
    :before-close="handleClose"
  >
    <span>试卷删除后无法恢复，确定删除吗?</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="delQuestionBank"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
    <h3>确定删除这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveDelete" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog> -->
</template>

<script>
import {
  addQuestionBank,
  deleteQuestionBank,
  paperList,
  deletePaper,
  listTag,
} from "@/request/service.js";
import topicModule from "@/mixins/topicModule.js";

// import { ElMessage } from "element-plus";
// import dayjs from "dayjs";
export default {
  name: "Topic",
  emits: ["goDetail"],
  mixins: [topicModule],
  data() {
    return {
      radio1: "1",
      radio2: "1",
      radio3: "1",
      dialog: false,
      delId: "",
      title: "",
      lineTime: "",
      bankName: "",
      currentPage: 1,
      pageSize: 100,
      total: 0,
      bankVisible: false,
      dialogVisible: false,
      // choseData: [],
      // currentChose: {},
      tableData: [],
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
    };
  },
  watch:{
      radio1(val){
        if(val==2){
          this.radio2=""
        }
      }
  },
  mounted() {
    this.init();
  },
  methods: {
    filterHandler(value, row, column) {
      const property = column["property"];
      return row[property] === value;
    },
    // 跳转到预览页
    toPreview(row) {
      window.open(window.location.origin + `/paperPreview?id=${row.encodeId}`);
    },
    async init() {
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        startTime: publishedStartTime,
        endTime: publishedEndTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
      };
      paperList(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data;
      });
    },

    add() {
      this.dialog = true;
    },
    // 选择添加
    addTrue() {
      //考试
      if (this.radio1 == 1) {
        // 固定
        this.$emit("types", "add1");
      } else {
        if(this.radio3==1){
           // 随机考试类
          this.$emit("types", "add2");
        }else{
        this.$emit("types", "add4");
        }
      }
      // 量表
      if (this.radio2 == 2) {
        this.$emit("types", "add3");
      }
      // 练习题
      if (this.radio2 == 3) {
         this.$emit("types", "add5");
      }
      this.dialog=false
    },
    // 编辑页面
    editPaper(row) {
      if(row.mode==1){
      this.$emit("edit", row,1);
      }else if(row.mode==2){
        if(row.type==2){
          this.$emit("edit", row,2);
        }
        if(row.type==1){
          this.$emit("edit", row,3);
        }
      }else{
        this.$emit("edit", row);
      }
    },
    reset() {
      this.title = "";
      this.name = "";
      this.lineTime = "";
      // this.$refs.multipleTable.clearSelection();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 添加试卷并跳转
    async goDetailPage() {
      let params = {
        title: this.bankName,
      };
      let res = await addQuestionBank(params);
      if (res.status === 200) {
        this.$message.success("添加试卷成功");
        this.$emit("goDetail", res.data);
      }
    },
    del(id) {
      this.delId = id;
      this.dialogVisible = true;
    },
    // 删除试卷
    async delQuestionBank() {
      let params = {
        id: this.delId,
      };
      let res = await deletePaper(params);
      if (res.status === 200) {
        this.$message.success("删除试卷成功");
        this.init();
        this.dialogVisible = false;
      }
    },
    // 试卷详情页
    editQuestionBank(data) {
      this.$emit("goDetail", data);
    },
    outBank() {
      this.bankName = "";
      this.bankVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.dialog_box {
  display: flex;
  > div:first-child {
    width: 200px;
  }
  ::v-deep .el-radio__label {
    font-weight: 700;
    color: #000000;
  }
  .r_title {
    padding-left: 24px;
    margin-bottom: 20px;
    font-weight: 400;
    color: #999999;
  }
}
.header {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .left {
    display: flex;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .tl {
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
    }
  }
}

.dialog {
  // .col {
  //   display: flex;
  //   align-items: center;
  //   margin-right: 20px;
  // }
  .tl {
    width: 25%;
    opacity: 1;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    margin-left: 50px;
    white-space: nowrap;
  }
}
.dialog-footer {
  text-align: center;
}
::v-deep {
  .el-table__body-wrapper .el-table__row {
    td:nth-child(2) {
      text-align: left;
      .cell {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
</style>
