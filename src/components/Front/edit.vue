<template>
  <div class="yxd_front_edit">
    <div class="container">
      <h3>前台权限</h3>
      <span class="txt">
        前台权限定义了什么人可以在您的项目中使用服务，您可以将权限授权给小组和个人。
      </span>
      <div class="col">
        <span>用户组前台权限</span>
        <el-table
          border
          tooltip-effect="dark"
          :data="groupData"
          style="width: 100%"
        >
          <el-table-column prop="name" label="组名称" align="center">
          </el-table-column>
          <el-table-column prop="information" label="资讯" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.information"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="course" label="课程" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.course"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="live" label="直播" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.live"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="评论" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.comment"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="leave" label="留言" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.leave"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="btn">
        <span>添加用户组</span>
        <div class="textBox">
          <el-input placeholder="请输入用户组名称" v-model="groupValue">
            <template #append>
              <el-button
                icon="el-icon-search"
                @click.stop="searchGroup"
              ></el-button>
            </template>
          </el-input>
          <ul v-if="showGroup">
            <li
              v-for="item in groupList"
              :key="item.value"
              :value="item.value"
              @click.stop="choseGroup(item)"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
        <el-button @click.stop="addGroup">添加</el-button>
      </div>
      <div class="col">
        <span>用户前台权限</span>
        <el-table
          border
          tooltip-effect="dark"
          :data="userData"
          style="width: 100%"
        >
          <el-table-column prop="name" label="组名称" align="center">
          </el-table-column>
          <el-table-column prop="information" label="资讯" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.information"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="course" label="课程" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.course"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="live" label="直播" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.live"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="评论" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.comment"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="leave" label="留言" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.leave"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="btn">
        <span>添加用户</span>
        <div class="textBox">
          <el-input placeholder="请输入用户名称" v-model="userValue"
            ><template #append>
              <el-button
                icon="el-icon-search"
                @click.stop="searchUser"
              ></el-button>
            </template>
          </el-input>
          <ul v-if="showUser">
            <li
              v-for="item in userList"
              :key="item.value"
              :value="item.value"
              @click.stop="choseUser(item)"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
        <el-button @click.stop="addUser">添加</el-button>
      </div>
      <div class="allBtn">
        <el-button @click.stop="save" type="primary">保存所有</el-button>
        <el-button @click.stop="cancel">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "front-edit",
  data() {
    return {
      userData: [
        {
          name: "黑眼圈1",
          information: true,
          course: true,
          live: true,
          comment: true,
          leave: true,
        },
        {
          name: "黑眼圈1",
          information: true,
          course: false,
          live: true,
          comment: true,
          leave: true,
        },
      ],
      groupData: [
        {
          name: "黑眼圈1",
          information: true,
          course: true,
          live: true,
          comment: true,
          leave: true,
        },
        {
          name: "黑眼圈1",
          information: true,
          course: false,
          live: true,
          comment: true,
          leave: true,
        },
      ],
      userList: [
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
      ],
      groupList: [
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
        {
          value: "1",
          label: "黑眼圈1",
        },
      ],
      groupValue: "",
      userValue: "",
      showUser: false,
      showGroup: false,
    };
  },
  mounted() {
    document.body.addEventListener(
      "click",
      () => {
        this.showUser = false;
        this.showGroup = false;
      },
      false
    );
  },
  methods: {
    save() {
      this.goBack();
    },
    cancel() {
      this.goBack();
    },
    goBack() {
      this.$emit("before");
    },
    choseGroup(item) {
      this.groupValue = item.label;
      this.showGroup = false;
    },
    choseUser(item) {
      this.userValue = item.label;
      this.showUser = false;
    },
    searchUser() {
      this.showUser = true;
    },
    searchGroup() {
      this.showGroup = true;
    },
    addUser() {},
    addGroup() {},
  },
};
</script>

<style scoped lang="scss">
.yxd_front_edit {
  h3 {
    margin-bottom: 30px;
  }
  .txt {
    display: block;
    margin-bottom: 30px;
  }
  .col {
    margin-bottom: 30px;
    span {
      display: block;
      margin-bottom: 30px;
    }
  }
  .btn {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    span {
      width: 100px;
      white-space: nowrap;
    }
    .textBox {
      margin-right: 20px;
      position: relative;
      ul {
        width: 100%;
        position: absolute;
        top: 50px;
        left: 0;
        z-index: 999;
        overflow: auto;
        border-radius: 4px;
        box-sizing: border-box;
        background: #fff;
        border: 1px solid #e4e7ed;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        word-wrap: break-word;
        font-size: 12px;
        line-height: 1.2;
        min-width: 10px;
        padding: 6px 0;
        li {
          font-size: 14px;
          padding: 0 32px 0 20px;
          position: relative;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #606266;
          height: 34px;
          line-height: 34px;
          box-sizing: border-box;
          cursor: pointer;
        }
        li:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}
</style>
