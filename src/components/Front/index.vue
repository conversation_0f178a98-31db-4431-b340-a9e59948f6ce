<template>
  <div class="yxd_front">
    <div class="container">
      <div class="header">
        <h3>前台权限</h3>
        <el-button @click="goEdit">编辑权限</el-button>
      </div>
      <span class="txt">
        前台权限定义了什么人可以在您的项目中使用服务，您可以将权限授权给小组和个人。
      </span>
      <div class="col">
        <span>用户组前台权限</span>
        <el-table
          border
          tooltip-effect="dark"
          :data="groupData"
          style="width: 100%"
        >
          <el-table-column prop="name" label="组名称" align="center">
          </el-table-column>
          <el-table-column prop="information" label="资讯" align="center">
            <template #default="scope">
              <i v-if="scope.row.information" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="course" label="课程" align="center">
            <template #default="scope">
              <i v-if="scope.row.course" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="live" label="直播" align="center">
            <template #default="scope">
              <i v-if="scope.row.live" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="评论" align="center">
            <template #default="scope">
              <i v-if="scope.row.comment" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="leave" label="留言" align="center">
            <template #default="scope">
              <i v-if="scope.row.leave" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="col">
        <span>用户前台权限</span>
        <el-table
          border
          tooltip-effect="dark"
          style="width: 100%"
          :data="userData"
        >
          <el-table-column prop="name" label="组名称" align="center">
          </el-table-column>
          <el-table-column prop="information" label="资讯" align="center">
            <template #default="scope">
              <i v-if="scope.row.information" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="course" label="课程" align="center">
            <template #default="scope">
              <i v-if="scope.row.course" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="live" label="直播" align="center">
            <template #default="scope">
              <i v-if="scope.row.live" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="评论" align="center">
            <template #default="scope">
              <i v-if="scope.row.comment" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="leave" label="留言" align="center">
            <template #default="scope">
              <i v-if="scope.row.leave" class="el-icon-check"></i>
              <i v-else class="el-icon-close"></i>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "front-index",
  data() {
    return {
      userData: [
        {
          name: "黑眼圈1",
          information: true,
          course: true,
          live: true,
          comment: true,
          leave: true,
        },
        {
          name: "黑眼圈1",
          information: true,
          course: false,
          live: true,
          comment: true,
          leave: true,
        },
      ],
      groupData: [
        {
          name: "黑眼圈1",
          information: true,
          course: true,
          live: true,
          comment: true,
          leave: true,
        },
        {
          name: "黑眼圈2",
          information: true,
          course: false,
          live: true,
          comment: true,
          leave: true,
        },
      ],
    };
  },
  mounted() {},
  methods: {
    goEdit() {
      this.$emit("next");
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_front {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .col {
    margin-top: 20px;
    span {
      display: block;
      margin-bottom: 20px;
    }
  }
}
</style>
