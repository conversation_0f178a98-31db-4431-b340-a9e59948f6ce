<template>
    <div class="container">
      <el-form ref="form" :model="form" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="商品名称"
              :rules="{
                required: true,
                message: '请输入商品名称',
                trigger: 'blur',
                max: 64,
              }"
            >
              <el-input v-model="form.name" style="width: 300px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="商品属性"
              :rules="{
                required: true,
                message: '请选择商品属性',
                trigger: 'change',
              }"
            >
              <el-select v-model="form.attribute" placeholder="请选择">
                <!-- options go here -->
              </el-select>
              <el-select
              style="margin-left:20px"
                v-model="selectedItem"
                remote
                filterable
                :remote-method="searchItems"
                :loading="loading"
                :loading-text="'Searching...'"
                :label="'Search'"
                placeholder="请搜索选择券码ID"
              >
                <el-option
                  v-for="item in items"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="支付方式"
              :rules="{
                required: true,
                message: '请选择支付方式',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="form.payment"
                placeholder="请选择"
                style="width: 300px"
              >
                <!-- options go here -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="预支数量"
              :rules="{
                required: true,
                message: '请输入预支数量',
                trigger: 'blur',
              }"
            >
             <span  style="color:#999999">可追加500个</span><span style="color:#999999">可预支500个</span> <el-input-number v-model="form.prepaid"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="支付金额"
              :rules="{
                required: true,
                message: '请输入支付金额',
                trigger: 'blur',
              }"
            >
              <el-input-number v-model="form.amount"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="兑换次数"
              :rules="{
                required: true,
                message: '请输入兑换次数',
                trigger: 'blur',
              }"
            >
              <el-input-number v-model="form.exchange"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="列表页图片"
              :rules="{
                required: true,
                message: '请上传列表页图片',
                trigger: 'blur',
              }"
            >
              <div class="upload-box">
                <div class="upload">
                  <el-upload
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :http-request="customRequestPC"
                    :before-upload="beforeAvatarUpload"
                    :show-file-list="false"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <span class="text">上传文件</span>
                    <div></div>
                    <!-- <el-button v-else icon="el-icon-upload">上传文件</el-button> -->
                  </el-upload>
                  <el-image
                    :fit="'cover'"
                    v-if="adImage"
                    :src="adImage"
                    class="avatar"
                  ></el-image>
                  <!-- <el-upload
              class="avatar-uploader"
              action="https://jsonplaceholder.typicode.com/posts/"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload">
              <img v-if="imageUrl" :src="imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload> -->
                </div>
                <!-- <span>支持扩展名：.jpg，.png.</span> -->
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="详情页头图"
              :rules="{
                required: true,
                message: '请上传详情页头图',
                trigger: 'blur',
              }"
            >
              <div class="upload-box">
                <div class="upload">
                  <el-upload
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :http-request="customRequestPC"
                    :before-upload="beforeAvatarUpload"
                    :show-file-list="false"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <span class="text">上传文件</span>
                    <div></div>
                    <!-- <el-button v-else icon="el-icon-upload">上传文件</el-button> -->
                  </el-upload>
                  <el-image
                    :fit="'cover'"
                    v-if="adImage"
                    :src="adImage"
                    class="avatar"
                  ></el-image>
                  <!-- <el-upload
              class="avatar-uploader"
              action="https://jsonplaceholder.typicode.com/posts/"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload">
              <img v-if="imageUrl" :src="imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload> -->
                </div>
                <!-- <span>支持扩展名：.jpg，.png.</span> -->
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item
              label="商品详情"
              :rules="{
                required: true,
                message: '请输入商品详情',
                trigger: 'blur',
              }"
            >
              <ms-editor class="editor" v-model="form.content"></ms-editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
            <el-button type="default" @click="()=>{$router.go(-1)}">取消</el-button>
          <el-button type="primary" @click="submitForm('form')">提交</el-button>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
  <script>
  import msEditor from "@/components/ms-editor/index";
  export default {
    data() {
      return {
        form: {
          name: "",
          attribute: "",
          payment: "",
          prepaid: "",
          amount: "",
          exchange: "",
          content: "",
          selectedItem: "",
          items: [],
          loading: false,
          remote: true,
        },
      };
    },
    components: { msEditor },
    methods: {
      //远程搜索
      searchItems(query) {
        this.loading = true;
        // 发送远程搜索请求，获取匹配的选项数据
        // 示例中使用 setTimeout 模拟异步请求
        setTimeout(() => {
          // 假设从服务器返回的数据格式为 { id: string, name: string }
          const response = [
            { id: "1", name: "Item 1" },
            { id: "2", name: "Item 2" },
            { id: "3", name: "Item 3" },
          ];
          this.items = response;
          this.loading = false;
        }, 1000);
      },
      //提交
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            alert("submit!");
          } else {
            return false;
          }
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .upload-box {
    // margin-right: 100px;
    width: 400px;
    .upload {
      display: flex;
      // ::v-deep{
      //   .el-image,.avatar{
      //     width: 188px;
      //     height: 108px;
      //     display: block;
      //   }
      // }
      .upload-demo,
      .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        margin-right: 20px;
        background-color: #fafafa;
        &:hover {
          border-color: #409eff;
        }
        .text {
          position: absolute;
          color: #797979;
          bottom: 5px;
          left: 0;
          right: 0;
        }
      }
      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 108px;
        height: 108px;
        line-height: 108px;
        text-align: center;
      }
      .avatar {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 88px;
        height: 88px;
        display: block;
        padding: 10px;
      }
  
      span {
        display: block;
        margin-top: 20px;
      }
    }
  }
  </style>