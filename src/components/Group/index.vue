<template>
  <div class="yxd_group">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="input" placeholder="请输入查找组"></el-input>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="init">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button
          v-if="btnList.add_docter_user_role_btn || btnList.add_patient_user_role_btn"
          @click="createGroup"
          type="primary"
          icon="el-icon-plus"
          >创建用户组</el-button
        >
      </div>
      <div class="table">
          <el-table
            border
            :data="tableData"
            row-key="id"
            tooltip-effect="dark"
            style="width: 100%"
            default-expand-all
            :tree-props="{children: 'childRoles', hasChildren: 'hasChildren'}"
            >
            <el-table-column prop="id" label="组ID" width="160" align="center">
            </el-table-column>
            <el-table-column prop="title" label="用户组名称" align="center">
            </el-table-column>
            <el-table-column
            prop="remark"
            label="描述"
            width="120"
            align="center"
          >
          </el-table-column>
            <el-table-column
            prop="userNum"
            label="人数"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="300"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.edit_docter_btn ||btnList.edit_patient_btn"
                size="mini"
                type="primary"
                @click="createGroup(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="btnList.remove_docter_btn ||btnList.remove_patient_btn"
                size="mini"
                type="danger"
                plain
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
              <el-button
                v-if="btnList.role_docter_users_btn || btnList.role_patient_users_btn"
                size="mini"
                @click="handleDown(scope.$index, scope.row)"
                >成员管理</el-button
              >
              <el-button
                size="mini"
                type="primary"
                plain
                @click="handleChildrenAdd(scope.$index, scope.row)"
                >添加子角色</el-button
              >
            </template>
          </el-table-column>
          </el-table>
      </div>
      <el-dialog :title="addTitle" v-model="addVisible" width="50%">
        <div class="edit-main">
          <div class="col">
            <span class="tl"><span style="color: red">*</span> 组名称</span>
            <el-input v-model.trim="groupName" placeholder="请输入组名称"></el-input>
          </div>
          <div class="col">
            <span class="tl">描述</span>
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              v-model="description"
            >
            </el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancel" size="mini">取消</el-button>
            <el-button type="primary" @click="saveAdd" size="mini"
              >保存</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- <el-dialog title="编辑用户组" v-model="editVisible" width="50%">
        <div class="edit-main">
          <div class="col">
            <span class="tl"><span style="color: red">*</span> 组名称</span>
            <el-input v-model="groupName" placeholder="请输入组名称"></el-input>
          </div>
          <div class="col">
            <span class="tl">描述</span>
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              v-model="description"
            >
            </el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancel" size="mini">取消</el-button>
            <el-button type="primary" @click="saveEdit" size="mini"
              >保存</el-button
            >
          </span>
        </template>
      </el-dialog> -->
      <el-dialog title="删除用户组" v-model="deleteVisible" width="50%">
        <span
          >删除组将会移除所有与之相关的权限限制。
          对该组限制的内容将会对所有项目用户可用。您确定要删除”{{
            currentChose.title
          }}“吗？</span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="saveDelete" size="mini"
              >确定</el-button
            >
            <el-button @click="deleteVisible = false" size="mini"
              >取消</el-button
            >
          </span>
        </template>
      </el-dialog>
    <!-- 添加子角色 -->
       <el-dialog :title="title+'——添加子角色'" v-model="childrenVisible" width="50%">
        <div class="edit-main">
          <div class="col">
            <span class="tl"><span style="color: red">*</span>用户组名称</span>
            <el-input v-model.trim="groupName" placeholder="请输入组名称" label-width="80px"></el-input>
          </div>
          <div class="col">
            <span class="tl">用户组描述</span>
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              v-model="description"
            >
            </el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="childrenVisible = false" size="mini">取消</el-button>
            <el-button type="primary" @click="childrenAdd" size="mini"
              >保存</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  query,
  saveSysRole,
  updateSysRole,
  removeSysRole,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "group-index",
  mixins: [initModule, initScroll],
  props: {
    accountType: {
      type: Number,
      default: 0,
    }
  },
  data() {
    return {
      input: "",
      tableData: [],
      editVisible: false,
      addVisible: false,
      childrenVisible:false,
      deleteVisible: false,
      currentChose: {},
      groupName: "",
      description: "",
      addTitle:'',
      title:'',
      parentId:""
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        accountType: this.accountType,
        title: this.input,
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.initScroll();
        }
      // this.tableData=[{
      //   id: 23,
      //   remark: null,
      //   title: "管理员",
      //   userNum: 64
      // },{
      //   id: 25,
      //   remark: "是的",
      //   title: "用户组001",
      //   userNum: 9,
      //   children: [{
      //         id: 66,
      //         remark: "是的aaa",
      //         title: "用户组001-1",
      //         userNum: 4,
      //       }, {
      //         id: 67,
      //         remark: "是的bbb",
      //         title: "用户组001-2",
      //         userNum: 5,
      //         children:[{
      //         id: 96,
      //         remark: "是的aaa",
      //         title: "用户组001-2-1",
      //         userNum: 4,
      //       }]
      //     }]
      // }]
      });
    },
    reset() {
      this.input = "";
    },
    cancel() {
      this.groupName = "";
      this.description = "";
      this.currentChose ={}
      this.parentId=''
      this.addVisible = false;
    },
    // 编辑/添加用户组
    createGroup(index, row) {
      if(row){
      this.addTitle = '编辑用户组'
      this.currentChose = row;
      this.groupName = row.title;
      this.description = row.remark;
      }else{
      this.addTitle = '添加用户组'
      this.groupName = "";
      this.description = "";
      }
      this.addVisible = true; 
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    handleDown(index, row) {
      this.$emit("edit", row);
    },
    saveAdd() {
      if (this.groupName==='') {
        ElMessage.warning("用户组名称不可为空！");
        return;
      }
      if(this.currentChose.id){
        // 编辑
      let params = {
        accountType:this.accountType,
        roleId:this.currentChose.id,
        remark: this.description,
        title: this.groupName,
      };
       updateSysRole(params).then((res) => {
        if (res.status == 200) {
          let params = {
            accountType:this.accountType,
            title: "",
          };
          ElMessage.success("修改用户组成功!");
          query(params).then((res) => {
            if (res.status == 200) {
              this.tableData = res.data;
              this.initScroll();
              this.cancel()
            }
          });
        }
      });
      }else{
        // 添加
      let params = {
        accountType:this.accountType,
        remark: this.description,
        title: this.groupName,
      };
       saveSysRole(params).then((res) => {
        if (res.status == 200) {
          let params = {
            accountType:this.accountType,
            title: "",
          };
          ElMessage.success("添加用户组成功!");
          query(params).then((res) => {
            if (res.status == 200) {
              this.tableData = res.data;
              this.initScroll();
              this.cancel()
            }
          });
        }
      });
      }
    },
    // saveEdit() {
    //   let params = {
    //     remark: this.description,
    //     roleId: this.currentChose.id,
    //     title: this.groupName,
    //   };
    //   updateSysRole(params).then((res) => {
    //     if (res.status == 200) {
    //       this.init();
    //       this.editVisible = false;
    //     }
    //   });
    // },
    // 删除用户组
    saveDelete() {
      let params = {
        accountType: this.accountType,
        roleId: this.currentChose.id,
      };
      removeSysRole(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
          ElMessage.success("删除用户组成功!");
        }
      });
    },
    // 打开添加子介绍弹框
    handleChildrenAdd(index, row) {
      if(row.childRoles.length>=10){
          ElMessage.warning("不能添加超过10个子角色!");
          return
      }else{
          this.parentId=row.id
          this.title=row.title
          this.groupName = "";
          this.description = "";
          this.childrenVisible = true;
      }
    },
    // 添加子角色
    childrenAdd(){
      if (this.groupName==='') {
        ElMessage.warning("用户组名称不可为空！");
        return;
      }
      this.childrenVisible=false
      // 添加子角色接口
      let params = {
        accountType:this.accountType,
        parentId: this.parentId,
        remark: this.description,
        title: this.groupName,
      };
      saveSysRole(params).then((res) => {
        if (res.status == 200) {
          let params = {
            accountType:this.accountType,
            title: "",
          };
          ElMessage.success("添加子角色成功!");
          query(params).then((res) => {
            if (res.status == 200) {
              this.tableData = res.data;
              this.initScroll();
              this.cancel()
            }
          });
        }
      });
    }
  },
};
</script>

<style scoped lang="scss">
.yxd_group {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }
  
  .table {
     ::v-deep {
       td:nth-child(1) {
        text-align: left;
        padding-left: 15px;
      }
    }
  }
  .edit-main {
    .col {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      .el-input {
        width: 30%;
      }
      .el-textarea {
        width: 30%;
      }
      .tl {
        width: 80px;
        white-space: nowrap;
        margin-right: 10px;
        text-align: right;
      }
    }
  }
  // ::v-deep {
  //   .el-table__row--level-2 .cell button:nth-child(4){
  //         visibility: hidden;
  //   }
  // }
}
</style>
