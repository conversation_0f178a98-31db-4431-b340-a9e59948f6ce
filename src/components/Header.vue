<template>
  <div class="header">
    <!-- 折叠按钮 -->
    <div class="collapse-btn" @click="collapseChage">
      <el-icon style="font-size: 22px" v-if="!collapse">
        <expand />
      </el-icon>
      <el-icon style="font-size: 22px" v-else>
        <fold />
      </el-icon>
    </div>
    <div class="logo">
      <img
        v-if="projectInfo.projectLogo"
        :src="projectInfo.projectLogo"
        alt=""
      /><span>{{ projectInfo.name }}</span>
    </div>
    <div class="header-right">
      <div class="header-user-con">
        <!-- 消息中心 -->
        <!-- <div class="btn-bell">
          <el-tooltip
            effect="dark"
            :content="message ? `有${message}条未读消息` : `消息中心`"
            placement="bottom"
          >
            <router-link to="/tabs">
              <i class="el-icon-bell"></i>
            </router-link>
          </el-tooltip>
          <span class="btn-bell-badge" v-if="message"></span>
        </div> -->
        <div class="goPc" @click="goIndex">
          <span>学习</span>
        </div>
        <!-- 用户头像 -->
        <div class="user-avator">
          <img :src="userInfo.avatar" />
        </div>
        <!-- 用户名下拉菜单 -->
        <el-dropdown class="user-name" trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            {{ userInfo.userName }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="loginout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script>
import { logout } from "@/request/service.js";
import { Expand, Fold } from "@element-plus/icons";
export default {
  data() {
    return {
      fullscreen: false,
      name: "admin",
      message: 2,
    };
  },
  components: {
    Expand,
    Fold,
  },
  computed: {
    userInfo() {
      return this.$store.state.backInfo.userInfo;
    },
    projectInfo() {
      return this.$store.state.projectInfo;
    },
    collapse() {
      return this.$store.state.collapse;
    },
  },
  methods: {
    // 用户名下拉菜单选择事件
    handleCommand(command) {
      if (command == "loginout") {
        let param = {
          userId: this.userInfo.userId,
        };
        logout(param).then((res) => {
          if (res.status == 200) {
            sessionStorage.removeItem("yxd_backstage_info");
            sessionStorage.removeItem("yxd_backstage_project");
            sessionStorage.removeItem("yxd_backstage_menu");
            localStorage.removeItem("isChoseAuto");
            this.$router.push("/login");
          }
        });
      }
    },
    // 侧边栏折叠
    collapseChage() {
      this.$store.commit("hadndleCollapse", !this.collapse);
    },
    goIndex() {
      window.open(`https://` + window.location.host);
    },
  },
  mounted() {
    if (document.body.clientWidth < 1300) {
      this.collapseChage();
    }
  },
};
</script>
<style scoped>
.header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 48px;
  font-size: 20px;
  color: #000;
}
.collapse-btn {
  float: left;
  padding: 0 21px;
  cursor: pointer;
  line-height: 48px;
}
.header .logo {
  float: left;
  line-height: 48px;
  color: rgba(9, 154, 250, 1);
  display: flex;
  align-items: center;
}
.logo span {
  cursor: default;
}
.logo img {
  display: block;
  width: 38px;
  height: 38px;
  border: 3px solid #eee;
  border-radius: 50%;
  margin-right: 20px;
  object-fit: contain;
}
.header-right {
  float: right;
  padding-right: 50px;
}
.header-user-con {
  display: flex;
  height: 48px;
  align-items: center;
}
.btn-fullscreen {
  transform: rotate(45deg);
  margin-right: 5px;
  font-size: 24px;
}
.btn-bell,
.btn-fullscreen {
  position: relative;
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
}
.btn-bell-badge {
  position: absolute;
  right: 0;
  top: -2px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #f56c6c;
  color: #fff;
}
.btn-bell .el-icon-bell {
  color: #fff;
}
.user-name {
  margin-left: 10px;
}
.user-avator {
  margin-left: 20px;
  margin-right: 20px;
}
.user-avator img {
  display: block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.el-dropdown-link {
  color: #000;
  cursor: pointer;
}
.el-dropdown-menu__item {
  text-align: center;
}
.goPc span {
  cursor: pointer;
  color: #169bd5;
  font-size: 14px;
}
</style>
