<template>
  <div v-if="addBox" :class="type=='add' ?'addContainer':'editContainer addContainer' ">
    <el-select v-model="attr"  class="m-2" placeholder="字段类型">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>

    <el-input v-model="fieldDisplayName" placeholder="前台显示名称" />
    <el-input v-model="description" placeholder="字段说明" />
    <div v-if="attr" class="between between_border">
      <div>类型</div>
      <div>{{ attr ? options.filter(item => item.value === attr)[0].label : "" }}</div>
    </div>
    <!-- 单选 -->
    <div class="radio" v-if="attr==6||attr==7" ref="sortableContainer1" id="sortableContainer1">
      <div class="between">
        <div>选项</div>
        <div class="b_right" @click="addOption">+</div>
      </div>
      <div
        style="display: flex"
        v-for="(item, index) in settings"
        :key="index"
      >
        <img
          src="@/assets/svg/tuozhuai.svg"
          draggable="true"
          class="sortable_img"
        />
        <el-input placeholder="选项" v-model="item.option" />
        <el-upload
          class="upload-container"
          :before-upload="beforeUpload(index)"
          :on-success="handleSuccess"
          :show-file-list="false"
          :on-remove="handleRemove"
          ref="uploaded"
          :auto-upload="true"
          :limit="1"
          :list-type="'picture-card'"
          action="#"
        >
          <template v-if="!item.imageUrl">
            <i class="el-icon-plus"></i>
          </template>
          <div v-if="item.imageUrl" class="image-container">
            <img
              :src="item.imageUrl"
              alt="Uploaded Image"
              class="uploaded-image"
              width="50"
              height="50"
            />
            <div @click="replaceImage" class="replace-button"></div>
          </div>
        </el-upload>

        <div class="b_right" @click="delOption(index)">-</div>
      </div>
    </div>

    <!-- 上传类型配置 -->
    <div v-if="attr==8" class="upload-config">
      <div class="between between_border">
        <div>上传类型</div>
        <div>附件上传</div>
      </div>
      <div class="between between_border">
        <div>文件大小限制</div>
        <div>最大 20MB</div>
      </div>
      <div class="between between_border">
        <div>支持格式</div>
        <div>图片、文档、视频等常见格式</div>
      </div>
    </div>
    <!-- 保存按钮、删除按钮 -->
    <div v-if="type=='add'" style="display:flex;justify-content:flex-end">
        <el-button type="primary" @click="addSubmit" >添加字段</el-button>

    </div>
    <div v-if="type=='edit'" style="display:flex;justify-content:space-between;margin-top:8px">
        <el-button type="danger" @click="saveDeleteOne">删除字段</el-button>
        <el-button type="primary" @click="editSubmit" >保存 </el-button>
    </div>
  </div>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { onMounted, ref, nextTick, defineProps, defineEmit, computed, watch } from "vue";
import Sortable from "sortablejs";
import { uploadAvatar,addUserDefinedField,updateUserDefinedFieldById,deleteUserDefinedFieldById  } from "@/request/service.js";
import store from "@/store/index";
const emit = defineEmit(['success', 'delSuccess', 'editSuccess']);
const propsData = defineProps({
  attr: {
    type: String,
    required: 0,
  },
  type: {
    type: String,
    required: "",
  },
  addBox:{
    type: Boolean
  },
  num:{
     type: Number  
  },
  datas:{
     type:Object   
  },
  accountType: {
    type: Number
  }
});
const sortableContainer1 = ref(null);
let settings = ref([{ option: "", imageUrl: "" }]);
const uploaded = ref(false);
const uploadedImageUrl = ref("");
const uploadUrl = "/your-upload-url";
const uploadRef = ref(null);
const imgIndex = ref("");
const fieldDisplayName =ref("")
const fieldName =ref("")
const description =ref("")
const attr = ref("")
const datas = propsData.datas;
const editId = ref("");
let num =propsData.num
const options = computed(() => {
  if(propsData.accountType == 0) {
    return [
      {
        label: "字符串",
        value: "1",
      },
      {
        label: "数字",
        value: "2",
      },
      {
        label: "日期",
        value: "3",
      },
      {
        label: "时间",
        value: "4",
      },
      {
        label: "单选",
        value: "6",
      },
      {
        label: "多选",
        value: "7",
      },
      {
        label: "附件上传",
        value: "8",
      },
    ]
  }else{
    return[
      {
        label: "日期",
        value: "3",
      },
      {
        label: "单选",
        value: "6",
      },
      {
        label: "多选",
        value: "7",
      },
      {
        label: "附件上传",
        value: "8",
      },
    ]
  }
})
watch(
  () => propsData.datas,
  (newValue) => {
    if (newValue) {
      fieldName.value = newValue.fieldName
      attr.value = newValue.fieldType
      attr.value = String(newValue.fieldType)
      description.value = newValue.description
      fieldDisplayName.value = newValue.fieldDisplayName
      editId.value=newValue.id
      if(newValue.sort){
      num=newValue.sort

      }
      if(newValue.settings){
        settings.value=newValue.settings
      }
    }
  },
  {
    deep: true
  }
)
onMounted(() => {
  if (uploadRef.value) {
    uploadRef.value.onSuccess = handleSuccess;
  }
  if(datas){
    fieldName.value = datas.fieldName
    attr.value = datas.fieldType
    attr.value = String(datas.fieldType)
    description.value = datas.description
    fieldDisplayName.value = datas.fieldDisplayName
    editId.value=datas.id
    if(datas.sort){
    num=datas.sort

    }
    if(datas.settings){
        settings.value=datas.settings
    }
}
 
});
 const saveDeleteOne=(item) =>{
      let params = {
        id: editId.value,
        projectId: store.state.projectInfo.id
      }
      deleteUserDefinedFieldById(params).then(() => {
        emit('delSuccess')
      })
    }
const filetoDataUrl = (file, callback) => {
  var reader = new FileReader();
  reader.onload = function () {
    callback(reader.result);
  };
  reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
};
// 修改保存
const editSubmit=async ()=>{
       // 为上传类型字段设置默认配置
       let uploadSettings = settings.value;
       if (attr.value == '8') {
         uploadSettings = [{
           uploadType: 'attachment',
           maxSize: 20,
           allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'mp4', 'avi', 'mov', 'wmv']
         }];
       }

       let params = {
          fieldDisplayName:fieldDisplayName.value ,
          fieldName: fieldDisplayName.value,
          fieldType:attr.value ,
          projectId: store.state.projectInfo.id,
          accountType: propsData.accountType,
          description:description.value,
          settings:uploadSettings,
          sort: num,
          id: editId.value
        }
        await updateUserDefinedFieldById(params).then(() => {
          emit('editSuccess')
        })
}

// 添加保存
const addSubmit=async ()=>{
      // 为上传类型字段设置默认配置
      let uploadSettings = settings.value;
      if (attr.value == '8') {
        uploadSettings = [{
          uploadType: 'attachment',
          maxSize: 20,
          allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'mp4', 'avi', 'mov', 'wmv']
        }];
      }

      let params = {
          fieldDisplayName:fieldDisplayName.value ,
          fieldName: fieldDisplayName.value,
          fieldType:attr.value ,
          projectId: store.state.projectInfo.id,
          accountType: propsData.accountType,
          description:description.value,
          settings:uploadSettings,
          sort: num
        }
        await addUserDefinedField(params).then(() => {
         fieldDisplayName.value="" ,
           fieldDisplayName.value="",
            attr.value="" ,
         description.value="",
          settings.value=[],
           num
           emit('success')
        })

}
const addOption = () => {
  settings.value.push({
              "imageUrl": "",
              "option": ""
            });

  createSortable();
};
const delOption = (index) => {
  settings.value.splice(index, 1);
  createSortable();
};
const createSortable = () => {
  const container = sortableContainer1.value;

  Sortable.create(container, {
    onEnd: (evt) => {
      const originalData = settings.value;
      const oldIndex = evt.oldIndex - 1;
      const newIndex = evt.newIndex - 1;

      originalData.splice(newIndex, 0, originalData.splice(oldIndex, 1)[0]);

      const newArray = originalData.slice(0);
      settings.value = [];
      nextTick(() => {
        settings.value = newArray;
      });
    },
  });
};
const beforeUpload = (index) => {
  return (file) => {
    const maxWidth = 160; // 最大宽度限制
    const maxHeight = 160; // 最大高度限制
    const img = new Image();
    img.src = URL.createObjectURL(file);

    img.onload = () => {
      const width = img.width;
      const height = img.height;
      // width != maxWidth && height != maxHeight
      if (!width || !height) {
        ElMessage.warning(`图片尺寸超过限制，尺寸为${maxWidth}×${maxHeight}`);
        return false;
      } else {
        // 只允许上传一张图片
        if (file.type !== "image/jpeg" && file.type !== "image/png") {
          ElMessage.warning("只能上传JPEG或PNG格式的图片");
          return false;
        }
        filetoDataUrl(file, function (img) {
          let params = {
            base64: img,
          };
          uploadAvatar(params).then((res) => {
            if (res.status == 200) {
              uploaded.value = true;
              settings.value[index].imageUrl = res.data.url;
              uploadedImageUrl.value = res.data.url;
            }
          });
        });
      }
    };

    return false;
  };
};

const handleSuccess = (response, file) => {
  uploaded.value = true;
  uploadedImageUrl.value = response.url;
};

const handleRemove = () => {
  uploaded.value = false;
  uploadedImageUrl.value = "";
  // 清空上传文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

const replaceImage = () => {
  uploaded.value = false;
  uploadedImageUrl.value = "";
  if (uploadRef.value) {
    uploadRef.value.submit();
  }
};


</script>
<style lang="scss" scoped>
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 10px;
}

.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.uploaded-image {
  width: 28px;
  height: 28px;
  object-fit: cover;
  border-radius: 4px;
}

.replace-button {
  width: 28px;
  height: 28px;
  position: absolute;
  margin-top: 8px;
  padding: 8px 16px;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.sortable_img:hover {
  cursor: pointer;
}
.sortable_img {
  width: 15px;
  margin-bottom: 10px;
}
.b_right {
  font-size: 28px !important;
  line-height: 28px;
  font-weight: 600;
  color: #e6e6e6;
}
.between_border {
  border-bottom: solid 1px #e6e6e6;
}
.b_right:hover {
  cursor: pointer;
}
.between {
  display: flex;
  justify-content: space-between;
  font-size: 14px;

  line-height: 30px;
}
.b_right {
  font-size: 28px !important;
  line-height: 28px;
  font-weight: 600;
  color: #e6e6e6;
}
.between_border {
  border-bottom: solid 1px #e6e6e6;
}
.b_right:hover {
  cursor: pointer;
}
.between {
  display: flex;
  justify-content: space-between;
  font-size: 14px;

  line-height: 30px;
}
.editContainer{
    margin-left:0  !important;
}
.addContainer {
  margin-left: 120px;
  width: 300px;
  padding: 20px;
  background: #fff;
  border: solid 1px #e6e6e6;
  ::v-deep {
    .el-input {
      margin-bottom: 10px;
    }
    .el-select {
      width: 100%;
    }
    .el-upload--picture-card {
      height: 28px;
      overflow: hidden;
      line-height: 28px;
      border: none;
      i {
        font-size: 12px;
      }
    }
  }
}
</style>

