<template>
  <div class="container">
    <h3>身份设置</h3>
    <span class="word"
      >这是用于设置是否需要项目用户自行完善信息和身份认证的设置，打开之后需要用户具体完善和认证的信息，可以根据需要自定义设置。</span
    >
    <div class="col">
      <span class="tl">用户认证</span>
      <el-switch
        v-model="isVerify"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="switchVerify"
      ></el-switch>
    </div>
    <div class="col" v-show="isVerify">
      <span class="tl">强制认证后访问</span>
      <el-switch
        v-model="isForceAuth"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="switchForceAuth"
      ></el-switch>
    </div>
    <div class="col">
      <span class="tl">完善信息</span>
      <el-switch
        v-model="isComplete"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="switchMain"
      ></el-switch>
    </div>
    <div class="col" v-show="isComplete">
      <span class="tl">审核完善信息</span>
      <el-switch
        v-model="isPerfectInfoApproval"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="switchPerfectInfoApproval"
      ></el-switch>
    </div>
    <div class="col" v-show="isComplete">
      <span class="tl">二合一登录</span>
      <el-switch
        v-model="isLogin"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="setLogin"
      ></el-switch>
    </div>
    <div v-if="isComplete" class="col sp">
      <span class="tl">需要完善的字段</span>
      <div class="switch-box" >
        <div
          class="switch-item"
          v-for="(item, index) in newIdentityList"
          :key="index"
        >
          <div style="width: 25px"></div>
          <el-checkbox v-model="item.checked" @change="checkFn(item)">{{
            item.title
          }}</el-checkbox>
          <el-switch
            v-if="item.checked"
            v-model="item.required"
            @change="checkFn2(item)"
            :disabled="item.disabled"
            active-color="#2f92ee"
            inactive-color="#f0f2f5"
            inactive-text="必填"
          ></el-switch>
        </div>
        <span ref="sortableContainer">
          <div
            class="switch-item"
            v-for="(item, index) in form.customList"
            :key="index"
          >
            <img src="@/assets/svg/tuozhuai.svg"  @click="add('edit',item)" draggable="true" style="width: 15px" />
            <el-checkbox v-model="item.selected" @click="checkFn3(item)">{{
              item.fieldDisplayName
            }}</el-checkbox>
            <el-switch
              v-if="item.selected"
              v-model="item.perfectRequired"
              @click="checkFn4(item)"
              active-color="#2f92ee"
              inactive-color="#f0f2f5"
              inactive-text="必填"
            ></el-switch>
          </div>
        </span>
        <common
          :accountType="accountType"
          :addBox="addBox"
          :type="type"
          v-if="type=='edit'"
          :datas="editData"
          :num="form.customList.length"
          @edit-success="init"
          @del-success="init"
        ></common>
      </div>
    </div>
    <div>
      <span class="add" @click="add('add')" v-if="!addBox && isComplete">添加字段</span>
      <common 
        :addBox="addBox"
        :type="type"
        v-if="type=='add'"
        :num="form.customList.length"
        :accountType="accountType"
        @success="init"
      ></common>
    </div>
  </div>
</template>
<script>
import {
  perfectInfo,
  updatePerfectInfo,
  getLoginType,
  loginType,
  getDefinedFieldList,
  addUserDefined,
  userSort,
  delUserDefined,
  authentication,
  getAccessAfterMandatoryCertification,
  getPerfectInfoApproval
} from "@/request/service.js";
import Sortable from "sortablejs";
import {
  reactive,
  onMounted,
  toRefs,
  ref,
  computed,
  getCurrentInstance,
  nextTick,
} from "vue";
import common from "./components/commin.vue";
import { ElMessage } from "element-plus";
import store from "@/store/index";
const conditionList = ["职称", "单位", "科室"];
export default {
  name: "identity",
  components: {
    common,
  },
  setup: () => {
    let type =ref("")
    const accountType = ref(0)
    const sortableContainer = ref(null);
    const editData = ref({})
    const reactiveRefs = reactive({
      newIdentityList: [], // 创建一个响应式数组
    });
    const forms = ref(null);
    const isVerify = ref(false);
    const isForceAuth = ref(false);
    const isComplete = ref(false);
    const isPerfectInfoApproval = ref(false);
    const isLogin = ref(false);
    const editVisible = ref(false);
    const instance = getCurrentInstance();
    const addBox = ref(false);
    const _this = instance.appContext.config.globalProperties;
    const name = ref("");
    const description = ref("");
    const newIdentityList = computed(() => {
      return state.identityList.filter(
        (i) => i.title != "头像" && i.title != "昵称"
      );
      // if (isLogin.value) {
      //   return state.identityList.filter(i => i.title != '头像' && i.title != '昵称')
      // } else {
      //   return state.identityList
      // }
    });
    const state = reactive({
      identityList: [],
      mainList: {},
      form: {
        domains: [],
        customList: [],
      },
    });
    const customValue = ref("");
    const authValue = ref({})
    const forceAuthValue = ref({})
    const perfectInfoApprovalValue = ref({})
    const init = () => {
      authentication().then((res)=>{
        if (res.status == 200) {
          isVerify.value = res.data.checked == 1;
          authValue.value = res.data
        }
      })
      // 获取强制认证后访问配置
      getAccessAfterMandatoryCertification().then((res)=>{
        if (res.status == 200) {
          isForceAuth.value = res.data.checked == 1;
          forceAuthValue.value = res.data
        }
      })
      // 获取审核完善信息配置
      getPerfectInfoApproval().then((res)=>{
        if (res.status == 200) {
          isPerfectInfoApproval.value = res.data.checked == 1;
          perfectInfoApprovalValue.value = res.data
        }
      })
      perfectInfo(0).then((res) => {
        if (res.status == 200) {
          isComplete.value = res.data.checked == 1;
          state.mainList.id = res.data.id;
          state.mainList.required = res.data.required;
          state.identityList = res.data.child;
          let isTitle = false;
          state.identityList.forEach((val) => {
            if (conditionList.includes(val.title) && val.checked) {
              isTitle = true;
            }
            if (val.checked == 1) {
              val.checked = true;
            } else {
              val.checked = false;
            }
            if (val.required == 1) {
              val.required = true;
            } else {
              val.required = false;
            }
          });
          state.identityList.forEach((d) => {
            if (isTitle) {
              if (d.title == "身份" && d.checked) {
                d.disabled = true;
              }
            }
          });
        }
      });
      // 二合一
      getLoginType({ projectId: store.state.projectInfo.id }).then((res) => {
        isLogin.value = res.data === "1";
      });
      // 自定义字段表格回显
      setCustomValue();
      // 自定义字段下拉框
      // getDefinedFieldList(store.state.projectInfo.id, 0).then((res) => {
      //    res.data.forEach(element => {
      //     element.selected=element.selected==0?false:true
      //     element.perfectRequired=element.perfectRequired==0?false:true
      //   });
      //   state.form.customList = res.data;
      // });
      if(addBox.value){
        addBox.value = false
      }
    };
    const createSortable = () => {
      const container = sortableContainer.value;
      Sortable.create(container, {
        onEnd: (evt) => {
          const originalData = state.form.customList;
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;
          originalData.splice(
            newIndex,
            0,
            originalData.splice(oldIndex, 1)[0]
          );
          const newArray = originalData.slice(0);
          console.log(evt.oldIndex,evt.newIndex)
          state.form.customList = [];
            nextTick(()=>{
              state.form.customList = newArray;
              state.form.customList.forEach((i,index)=>{
                i.sort=index
              })
              userSort(state.form.customList).then((res)=>{
                console.log(res)
              })
            })
        },
      });
    };
    // #1
    //  二合一登录开关
    const setLogin = async () => {
      await loginType({
        projectId: store.state.projectInfo.id,
        loginType: isLogin.value ? 1 : 0,
      });
    };
    // 添加字段
    const add = (val,item) => {
      // 打开add盒子
      addBox.value = true;
      type.value=val
      if(item){
        editData.value=item
      }
    };
    // 用户认证开关
    function switchVerify(e) {
      let params = {
        ...authValue.value,
        checked: e ? 1 : 0,
      };
      updateInfo(params)
      // 联动逻辑：用户认证关闭时，强制认证后访问也需要关闭
      if (!e && isForceAuth.value) {
        isForceAuth.value = false;
        let forceAuthParams = {
          ...forceAuthValue.value,
          checked: 0,
        };
        updateInfo(forceAuthParams)
      }
    }
    // 强制认证后访问开关
    function switchForceAuth(e) {
      let params = {
        ...forceAuthValue.value,
        checked: e ? 1 : 0,
      };
      updateInfo(params)
    }
    // 完善信息开关
    function switchMain(e) {
      isLogin.value = false;
      let params = {
        checked: e ? 1 : 0,
        id: state.mainList.id,
        required: state.mainList.required,
        accountType: 0
      };
      updatePerfectInfo(params).then((res) => {
        if (res.status == 200) {
          init();
        }
      });
      // 联动逻辑：完善信息关闭时，审核完善信息也需要关闭
      if (!e && isPerfectInfoApproval.value) {
        isPerfectInfoApproval.value = false;
        let approvalParams = {
          ...perfectInfoApprovalValue.value,
          checked: 0,
        };
        updateInfo(approvalParams)
      }
    }
    // 审核完善信息开关
    function switchPerfectInfoApproval(e) {
      let params = {
        ...perfectInfoApprovalValue.value,
        checked: e ? 1 : 0,
      };
      updateInfo(params)
    }
    // 选项开关
    const checkFn = (item) => {
      if (item.checked && conditionList.includes(item.title)) {
        state.identityList.forEach((v) => {
          if (v.title == "身份") {
            v.checked = true;
            v.required = true;
            v.disabled = true;
            // ******单独更新的接口******
            updateInfo(v);
          }
        });
      }
      if (!item.checked && item.title == "身份") {
        state.identityList.forEach((v) => {
          if (conditionList.includes(v.title) && v.checked) {
            v.checked = false;
            updateInfo(v);
          }
        });
      }
      updateInfo(item);
    };
    // 必填选项开关
    const checkFn2 = (item) => {
      if (conditionList.includes(item.title)) {
        state.identityList.forEach((d) => {
          if (d.title == "身份") {
            d.disabled = true;
          }
        });
      }
      if (item.required && conditionList.includes(item.title)) {
        state.identityList.forEach((d) => {
          if (d.title == "身份") {
            d.required = true;
            updateInfo(d);
          }
        });
      }
      updateInfo(item);
    };
    // 自定义字段选项开关
    const checkFn3 =async (item) => {
     if(!item.selected){
       addDomain(item)
     }else{
        updateDomain(item, 1)
        item.perfectRequired=false
        updateDomain(item, 0)
     }
    };
    // 必填非必填
    const checkFn4 =async (item) => {
     if(item.perfectRequired){
         updateDomain(item, 0)
     }else{
         updateDomain(item, 0)
     }
    };
    // 更新数据
    const updateInfo = (item) => {
      let params = {
        // 单个按钮
        checked: item.checked ? 1 : 0,
        id: item.id,
        required: item.required ? 1 : 0,
        accountType: 0
      };
      updatePerfectInfo(params).then((res) => {
        if (res.status == 200) {
          init();
        }
      });
    };
    // 自定义字段表格回显
    const setCustomValue = async () => {
      await getDefinedFieldList(store.state.projectInfo.id, 0).then((res) => {
       res.data.forEach(element => {
          element.selected=element.selected==0?false:true
          element.perfectRequired=element.perfectRequired==0?false:true
        });
        state.form.customList = res.data;
        setTimeout(() => {
          createSortable();
        }, 2000);
      });
    };
    // 自定义字段开关
    const updateDomain =  (item, operateType) => {
      let params = {
        id: item.id,
        perfectRequired: item.perfectRequired ? 1 : 0,
        operateType: operateType,
      };
       delUserDefined(params).then((res) => {
      });
    };
    // 删除自定义内容
    const removeDomain = (item) => {
      _this
        .$confirm("此操作将永久移除该自定义字段, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          let params = {
            id: item.id,
            operateType: 1,
          };
          delUserDefined(params).then((res) => {
            init();
          });
          _this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          _this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    };
    // 添加自定义内容
    const addDomain = async (item) => {
        let params = {
          id: item.id,
        };
        const res = await addUserDefined(params);
        if (res.status === 200) {
          init();
        }
    };
    onMounted(() => {
      init();
    });
    return {
      forms,
      customValue,
      isVerify,
      isForceAuth,
      isComplete,
      isPerfectInfoApproval,
      isLogin,
      newIdentityList,
      ...toRefs(state),
      init,
      switchVerify,
      switchForceAuth,
      switchMain,
      switchPerfectInfoApproval,
      checkFn,
      checkFn2,
      updateInfo,
      setCustomValue,
      removeDomain,
      addDomain,
      // submitForm,
      setLogin,
      updateDomain,
      editVisible,
      addBox,
      add,
      name,
      description,
      createSortable,
      sortableContainer,
      checkFn3,
      checkFn4,
      type,
      editData,
      accountType
    };
  },
};
</script>
<style scoped lang="scss">
.container {
  padding-bottom: 20px;
}
.yxd_identity {
  h3 {
    margin-bottom: 30px;
  }
  .word {
    opacity: 1;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
  }
  .col {
    margin-top: 30px;
    display: flex;
    align-items: center;
    margin-right: 20px;
    .tl {
      width: 100px;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      white-space: nowrap;
      text-align: right;
    }
    .switch-box {
      .switch-item {
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
        img:hover {
          cursor: pointer;
        }
        .el-checkbox {
          margin-right: 30px;
        }
        ::v-deep .el-switch {
          .is-active {
            color: #606266;
          }
        }
      }
    }
  }
  .sp {
    align-items: flex-start;
  }
  .btn {
    margin-left: 20px;
  }
}
.add:hover {
  cursor: pointer;
}
.add {
  margin-left: 120px;
  font-size: 14px;
  color: rgb(47, 146, 238);
}
</style>