<template>
  <div class="container">
    <h3>身份设置</h3>
    <span class="word"
      >这是用于设置是否需要项目用户自行完善信息的设置，打开之后需要用户具体完善的信息，可以根据需要自定义设置。</span
    >
    <div class="col">
      <span class="tl">完善信息</span>
      <el-switch
        v-model="isComplete"
        active-color="#2f92ee"
        inactive-color="#f0f2f5"
        @change="switchMain"
      ></el-switch>
    </div>
    <div class="col" v-if="isComplete">
      <span class="tl">自定义填写引导
        <el-tooltip
              popper-class="atooltip"
              effect="light"
              placement="top"
            >
              <i
                class="el-icon-question"
                style="font-size: 14px; vertical-align: middle"
              ></i>
              <template #content>
                <div>
                  <div>尺寸：750*964px</div>
                </div>
              </template>
            </el-tooltip>
      </span>
      <el-upload
        class="avatar-uploader"
        action="#"
        list-type="picture-card"
        :show-file-list="false"
        :before-upload="beforeAvatarUpload1"
        :auto-upload="true"
         @mouseenter="avatar?isHovered = true:''"
    @mouseleave="avatar?isHovered = false:''"
      >
         <i v-if="isHovered" class="el-icon-delete delete-icon" @click.stop="handleRemove"></i>
        <img v-if="avatar" :src="avatar" class="avatar" />
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </div>
    <div v-if="isComplete" class="col sp">
      <span class="tl">需要完善的字段</span>
      <div class="switch-box">
        <div
          class="switch-item"
          v-for="(item, index) in newIdentityList"
          :key="index"
        >
          <div style="width: 25px"></div>
          <el-checkbox v-model="item.checked" @change="checkFn(item)">{{
            item.title
          }}</el-checkbox>
          <el-switch
            v-if="item.checked"
            v-model="item.required"
            @change="checkFn2(item)"
            :disabled="item.disabled"
            active-color="#2f92ee"
            inactive-color="#f0f2f5"
            inactive-text="必填"
          ></el-switch>
        </div>
        <span ref="sortableContainer">
          <div
            class="switch-item"
            v-for="(item, index) in form.customList"
            :key="index"
          >
            <img
              src="@/assets/svg/tuozhuai.svg"
              @click="add('edit', item)"
              draggable="true"
              style="width: 15px"
            />
            <el-checkbox v-model="item.selected" @click="checkFn3(item)">{{
              item.fieldDisplayName
            }}</el-checkbox>
            <el-switch
              v-if="item.selected"
              v-model="item.perfectRequired"
              @click="checkFn4(item)"
              active-color="#2f92ee"
              inactive-color="#f0f2f5"
              inactive-text="必填"
            ></el-switch>
          </div>
        </span>
        <common
          :accountType="accountType"
          :addBox="addBox"
          :type="type"
          v-if="type == 'edit' && isComplete"
          :datas="editData"
          :num="form.customList.length"
          @edit-success="init"
          @del-success="init"
        ></common>
      </div>
    </div>
    <div>
      <span class="add" @click="add('add')" v-if="!addBox && isComplete"
        >添加字段</span
      >
      <common
        :addBox="addBox"
        :type="type"
        v-if="type == 'add' && isComplete"
        :num="form.customList.length"
        :accountType="accountType"
        @success="init"
      ></common>
    </div>
  </div>
</template>
<script>
import {
  perfectInfo,
  updatePerfectInfo,
  getDefinedFieldList,
  addUserDefined,
  userSort,
  delUserDefined,
} from "@/request/service.js";
import Sortable from "sortablejs";
import {
  reactive,
  onMounted,
  toRefs,
  ref,
  computed,
  getCurrentInstance,
  nextTick,
} from "vue";
import common from "./components/commin.vue";
import store from "@/store/index";
import { beforeAvatarUpload } from "@/utils/upload.js";
const conditionList = ["职称", "单位", "科室"];
export default {
  name: "identity",
  components: {
    common,
  },
  setup: () => {
    const isHovered = ref(false)
    const accountType = ref(1);
    let type = ref("");
    const sortableContainer = ref(null);
    const editData = ref({});
    const forms = ref(null);
    const isComplete = ref(false);
    const editVisible = ref(false);
    const instance = getCurrentInstance();
    const addBox = ref(false);
    const _this = instance.appContext.config.globalProperties;
    const name = ref("");
    const description = ref("");
    const newIdentityList = computed(() => {
      return state.identityList.filter(
        (i) => i.title != "头像" && i.title != "昵称"
      );
    });
    const state = reactive({
      identityList: [],
      mainList: {},
      form: {
        domains: [],
        customList: [],
      },
    });
    const customValue = ref("");
    let avatar = ref("");
    const handleRemove = ()=>{
      isHovered.value=false
      avatar.value= ""
    switchMain(true)
    }
    const beforeAvatarUpload1= async (file)=>{
    avatar.value= await beforeAvatarUpload('','',file)
    switchMain(true)
    }
    const filetoDataUrl = (file, callback) => {
      var reader = new FileReader();
      reader.onload = function () {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    };
    const init = () => {
      perfectInfo(1).then((res) => {
        if (res.status == 200) {
          isComplete.value = res.data.checked == 1;
          state.mainList.id = res.data.id;
          state.mainList.required = res.data.required;
          state.identityList = res.data.child;
          avatar.value=res.data.image
          let isTitle = false;
          state.identityList.forEach((val) => {
            if (conditionList.includes(val.title) && val.checked) {
              isTitle = true;
            }
            if (val.checked == 1) {
              val.checked = true;
            } else {
              val.checked = false;
            }
            if (val.required == 1) {
              val.required = true;
            } else {
              val.required = false;
            }
          });
          state.identityList.forEach((d) => {
            if (isTitle) {
              if (d.title == "身份" && d.checked) {
                d.disabled = true;
              }
            }
          });
        }
      });
      // 自定义字段表格回显
      setCustomValue();
      // 自定义字段下拉框
      // getDefinedFieldList(store.state.projectInfo.id, 1).then((res) => {
      //    res.data.forEach(element => {
      //     element.selected=element.selected==0?false:true
      //     element.perfectRequired=element.perfectRequired==0?false:true
      //   });
      //   state.form.customList = res.data;
      // });
      if (addBox.value) {
        addBox.value = false;
      }
    };
    const createSortable = () => {
      const container = sortableContainer.value;
      Sortable.create(container, {
        onEnd: (evt) => {
          const originalData = state.form.customList;
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;
          originalData.splice(newIndex, 0, originalData.splice(oldIndex, 1)[0]);
          const newArray = originalData.slice(0);
          state.form.customList = [];
          nextTick(() => {
            state.form.customList = newArray;
            state.form.customList.forEach((i, index) => {
              i.sort = index;
            });
            userSort(state.form.customList).then((res) => {
            });
          });
        },
      });
    };
    // 添加字段
    const add = (val, item) => {
      // 打开add盒子
      addBox.value = true;
      type.value = val;
      if (item) {
        editData.value = item;
      }
    };
    // 完善信息开关
    function switchMain(e) {
      let params = {
        checked: e ? 1 : 0,
        id: state.mainList.id,
        required: state.mainList.required,
        accountType: 1,
        image:avatar.value
      };
      updatePerfectInfo(params).then((res) => {
        if (res.status == 200) {
          init();
        }
      });
    }
    // 选项开关
    const checkFn = (item) => {
      if (item.checked && conditionList.includes(item.title)) {
        state.identityList.forEach((v) => {
          if (v.title == "身份") {
            v.checked = true;
            v.required = true;
            v.disabled = true;
            // ******单独更新的接口******
            updateInfo(v);
          }
        });
      }
      if (!item.checked && item.title == "身份") {
        state.identityList.forEach((v) => {
          if (conditionList.includes(v.title) && v.checked) {
            v.checked = false;
            updateInfo(v);
          }
        });
      }
      updateInfo(item);
    };
    // 必填选项开关
    const checkFn2 = (item) => {
      if (conditionList.includes(item.title)) {
        state.identityList.forEach((d) => {
          if (d.title == "身份") {
            d.disabled = true;
          }
        });
      }
      if (item.required && conditionList.includes(item.title)) {
        state.identityList.forEach((d) => {
          if (d.title == "身份") {
            d.required = true;
            updateInfo(d);
          }
        });
      }
      updateInfo(item);
    };
    // 自定义字段选项开关
    const checkFn3 = async (item) => {
      if (!item.selected) {
        addDomain(item);
      } else {
        updateDomain(item, 1);
        item.perfectRequired = false;
        updateDomain(item, 0);
      }
    };
    // 必填非必填
    const checkFn4 = async (item) => {
      if (item.perfectRequired) {
        updateDomain(item, 0);
      } else {
        updateDomain(item, 0);
      }
    };
    // 更新数据
    const updateInfo = (item) => {
      let params = {
        // 单个按钮
        checked: item.checked ? 1 : 0,
        id: item.id,
        required: item.required ? 1 : 0,
        accountType: 1,
      };
      updatePerfectInfo(params).then((res) => {
        if (res.status == 200) {
          init();
        }
      });
    };
    // 自定义字段表格回显
    const setCustomValue = async () => {
      await getDefinedFieldList(store.state.projectInfo.id, 1).then((res) => {
        res.data.forEach((element) => {
          element.selected = element.selected == 0 ? false : true;
          element.perfectRequired = element.perfectRequired == 0 ? false : true;
        });
        state.form.customList = res.data;
        setTimeout(() => {
          createSortable();
        }, 2000);
      });
    };
    // 自定义字段开关
    const updateDomain = (item, operateType) => {
      let params = {
        id: item.id,
        perfectRequired: item.perfectRequired ? 1 : 0,
        operateType: operateType
      };
      delUserDefined(params).then((res) => {
      });
    };
    // 删除自定义内容
    const removeDomain = (item) => {
      _this
        .$confirm("此操作将永久移除该自定义字段, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          let params = {
            id: item.id,
            operateType: 1,
          };
          delUserDefined(params).then(() => {
            init();
          });
          _this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          _this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    };
    // 添加自定义字段 勾选时调用此接口
    const addDomain = async (item) => {
      let params = {
        id: item.id,
      };
      const res = await addUserDefined(params);
      if (res.status === 200) {
        // init();
      }
    };
    const refresh = () => {};
    onMounted(() => {
      init();
    });
    return {
      refresh,
      forms,
      customValue,
      isComplete,
      newIdentityList,
      ...toRefs(state),
      init,
      switchMain,
      checkFn,
      checkFn2,
      updateInfo,
      setCustomValue,
      removeDomain,
      addDomain,
      updateDomain,
      editVisible,
      addBox,
      add,
      name,
      description,
      createSortable,
      sortableContainer,
      checkFn3,
      checkFn4,
      type,
      editData,
      accountType,
      avatar,
      filetoDataUrl,
      beforeAvatarUpload1,
      handleRemove,
      isHovered
    };
  },
};
</script>
<style scoped lang="scss">
.container {
  padding-bottom: 20px;
  ::v-deep .el-upload{
    position: relative;
  }
  ::v-deep .el-upload .el-button span {
    display: flex;
    align-items: center;
  }
  .avatar{
    width: 100%;
    height: 100%;
  }
  ::v-deep .avatar-uploader {
      .el-upload--text {
        width: 120px;
        height: 120px;
        line-height: 120px;
        background-color: #fff;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        img {
          width: 120px;
          height: 120px;
          object-fit: contain;
        }
      }
    }
}
.yxd_identity {
  h3 {
    margin-bottom: 30px;
  }
  .word {
    opacity: 1;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
  }
  .col {
    margin-top: 30px;
    display: flex;
    align-items: center;
    margin-right: 20px;
    .tl {
      width: 100px;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      white-space: nowrap;
      text-align: right;
    }
    .switch-box {
      .switch-item {
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        img {
          margin-right: 10px;
        }
        img:hover {
          cursor: pointer;
        }
        .el-checkbox {
          margin-right: 30px;
        }
        ::v-deep .el-switch {
          .is-active {
            color: #606266;
          }
        }
      }
    }
  }
  .sp {
    align-items: flex-start;
  }
  .btn {
    margin-left: 20px;
  }
}
.add:hover {
  cursor: pointer;
}
.add {
  margin-left: 120px;
  font-size: 14px;
  color: rgb(47, 146, 238);
}
.delete-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: balck;
  cursor: pointer;
  z-index: 100;
  font-size: 16px;
  /* 添加其他样式 */
}
</style>