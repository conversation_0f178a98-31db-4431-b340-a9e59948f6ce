<template>
  <div class="yxd_information-create">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="资讯内容" name="first">
          <div class="content">
            <div class="col">
              <span class="tl"><span style="color:red">*</span>资讯标题</span>
              <el-input style="width:80%" v-model="formData.title"></el-input>
            </div>
            <div class="col">
              <span class="tl">外向链接</span>
              <el-input
                style="width:40%"
                v-model="formData.linkOutUrl"
              ></el-input>
            </div>
            <div class="col">
              <span class="tl"><span style="color:red">*</span>内容</span>
              <ms-editor class="editor" v-model="formData.content"></ms-editor>
            </div>
            <div class="col">
              <span class="tl"><span style="color:red">*</span>摘要</span>
              <el-input
                style="width:80%"
                type="textarea"
                :rows="3"
                @keyup.enter="summaryKeyUp = true"
                maxlength="100"
                show-word-limit
                placeholder="请输入内容"
                v-model="formData.summary"
              >
              </el-input>
            </div>
            <div class="col">
              <span class="tl">发布时间</span>
              <el-date-picker
                v-model="formData.publishedTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="资讯设置" name="second">
          <div class="setting">
            <div class="col">
              <span class="tl">来源</span>
              <el-input
                style="width:30%"
                placeholder="请输入"
                v-model="formData.articleFrom"
              ></el-input>
            </div>
            <div class="col">
              <span class="tl">作者</span>
              <el-input
                style="width:30%"
                placeholder="请输入"
                v-model="formData.author"
              ></el-input>
            </div>
            <div class="col">
              <span class="tl">分类</span>
              <!-- <el-input
                style="width:30%"
                placeholder="请选择分类"
                v-model="formData.categoryText"
              ></el-input> -->
              <el-select
                v-model="formData.categoryText"
                multiple
                value-key="tagId"
                placeholder="请选择分类"
                popper-class="listSelect"
              >
                <el-option
                  v-for="item in classGroup"
                  :key="item.tagId"
                  :label="item.tagName"
                  :value="item"
                >
                </el-option>
              </el-select>
            </div>
            <!-- <div class="col">
              <span class="tl">关键词</span>
              <el-input
                style="width:30%"
                placeholder="请输入并选择关键词"
                v-model="formData.articleKeyword"
              ></el-input>
            </div> -->
            <div class="col">
              <span class="tl">封面图片</span>
              <div class="upload">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :http-request="customRequest"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="formData.cover"
                    :src="formData.cover"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                <div class="warn">支持格式：.jpg .jpeg .png .gif</div>
                <div class="warn">图片尺寸：2M</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="footer">
        <el-button @click="save('draft')">存为草稿</el-button>
        <el-button @click="save('real')" type="primary">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import msEditor from "@/components/ms-editor/index";
import { escapeMap, unescapeMap } from "@/utils/constant.js";
import {
  getAriticleById,
  updateAriticle,
  uploadAvatar,
  addAriticle,
  tagTypeList,
} from "@/request/service.js";
import htmlMethods from "@/utils/htmlFunction.js";
import { getEditContent } from "@/utils/edit.js";
import { ElMessage } from "element-plus";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "information-create",
  mixins: [initScroll],
  components: {
    msEditor,
  },
  props: {
    edit: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      activeName: "first",
      formData: {},
      classGroup: [],
      summaryKeyUp: false,
    };
  },
  watch: {
    "formData.content": function(val) {
      if (val) {
        if (!this.formData.id && !this.summaryKeyUp) {
          this.formData.summary = htmlMethods.unexcapeHtml(getEditContent(val));
        }
      }
    },
  },
  async mounted() {
    this.initScroll();
    await this.initTag();
    if (this.edit !== 0) {
      let params = {
        id: this.edit,
      };
      getAriticleById(params).then((res) => {
        if (res.status == 200) {
          this.formData = res.data;
          this.formData.content = this.unexcapeHtml(this.formData.content);
          this.formData.categoryText = [];
          this.formData.tagList.forEach((d) => {
            this.formData.categoryText.push({
              tagName: d.tagName,
              tagId: d.tagId + "",
            });
          });
        }
      });
    }
  },
  methods: {
    initTag() {
      let params = {
        tagIds: [],
        tagType: "article",
      };
      tagTypeList(params).then((res) => {
        if (res.status == 200) {
          this.classGroup = res.data;
        }
      });
    },
    save(type) {
      this.formData.content = this.excapeHtml(this.formData.content);
      this.formData.tagList = [];
      this.formData.categoryText.forEach((d) => {
        this.formData.tagList.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      if (type == "draft") {
        this.formData.approvalStatus = 2;
      } else {
        this.formData.approvalStatus = 0;
      }
      if (!this.formData.projectId) {
        this.formData.projectId = this.$store.state.projectInfo.id;
        this.formData.recommend = 0;
        this.formData.sticky = 0;
        addAriticle(this.formData).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      } else {
        updateAriticle(this.formData).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      }
    },
    goBack() {
      this.$emit("back");
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        ElMessage.error("上传头像图片大小不能超过 2MB!");
        return false;
      } else {
        return true;
      }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    customRequest(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.formData.cover = res.data.url;
          }
        });
      });
    },
    excapeHtml(content) {
      var escaper = function(match) {
        return escapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(escapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
    unexcapeHtml(content) {
      var escaper = function(match) {
        return unescapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(unescapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
  },
};
</script>

<style scoped lang="scss">
.el-select {
  width: 30% !important;
}
.yxd_information-create {
  .content {
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      .tl {
        width: 70px;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
        text-align: right;
      }
    }
  }
  .setting {
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      .tl {
        width: 70px;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
        text-align: right;
      }
      .upload {
        ::v-deep .avatar-uploader {
          .el-upload--text {
            width: 120px;
            height: 120px;
            line-height: 120px;
            background-color: #fff;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            box-sizing: border-box;
            text-align: center;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }
        }
        .warn {
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }
  .footer {
    text-align: right;
  }
}
</style>
