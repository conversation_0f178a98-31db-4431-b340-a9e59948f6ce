<template>
  <div class="yxd_leave">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入留言标题"></el-input>
          </div>
          <div class="col">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="创建开始时间"
              end-placeholder="创建结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button v-if="btnList.batch_remove_btn" @click="deleteBatch"
          >批量删除</el-button
        >
      </div>
      <div class="table">
        <el-table
          ref="multipleTable"
          border
          :data="tableData"
          :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="序号" width="75" align="center">
          </el-table-column>
          <el-table-column
            prop="mgTitle"
            label="标题"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="mgStatus"
            label="状态"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="留言人"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="留言时间"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="replyUsername"
            label="回复人"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="replyCreateTime"
            label="回复时间"
            sortable
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.reply"
                size="mini"
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                >回复</el-button
              >

              <el-button
                v-if="btnList.remove_btn"
                size="mini"
                type="danger"
                plain
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <h3>确定删除？</h3>
        <el-tag class="delete-box">
          {{ currentChose.mgTitle }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveDeleteEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag
          class="delete-box"
          v-for="(item, index) in choseData"
          :key="index"
        >
          {{ item.mgTitle }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveDelete" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { ElMessage } from "element-plus";
import { batchDelete, leaveQuery } from "@/request/service.js";
export default {
  name: "leave-index",
  mixins: [initModule, initScroll],
  data() {
    return {
      title: "",
      time: "",
      tableData: [],
      currentChose: {},
      settingVisible: false,
      deleteVisible: false,
      choseData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let createEndTime = "";
      let createStartTime = "";
      if (this.time && this.time.length > 0) {
        createStartTime = this.time[0];
        createEndTime = this.time[1];
      }
      let params = {
        mgTitle: this.title,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        createEndTime: createEndTime,
        createStartTime: createStartTime,
      };
      leaveQuery(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.mgStatus == 0) {
              val.mgStatus = "未回复";
            } else {
              val.mgStatus = "已回复";
            }
          });
          this.total = res.totalSize;
        }
      });
    },
    deleteBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.settingVisible = true;
    },
    reset() {
      this.time = "";
      this.title = "";
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    handleEdit(index, row) {
      this.$emit("edit", row);
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    saveDeleteEdit() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        ids: ids,
      };
      batchDelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.init();
        }
      });
    },
    saveDelete() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
      };
      batchDelete(params).then((res) => {
        if (res.status == 200) {
          this.settingVisible = false;
          this.init();
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_leave {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }

  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
</style>
