<template>
  <div class="yxd_leave-reply">
    <div class="container">
      <div class="item">
        <div class="col">
          <span class="tl">标题</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.mgTitle"
            :disabled="true"
          >
          </el-input>
        </div>
        <div class="col">
          <span class="tl">留言人姓名</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.mgRealName"
            :disabled="true"
          >
          </el-input>
        </div>
        <div class="col">
          <span class="tl">留言人</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.createdName"
            :disabled="true"
          >
          </el-input>
        </div>
      </div>
      <div class="item">
        <div class="col">
          <span class="tl">单位</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.mgUnit"
            :disabled="true"
          >
          </el-input>
        </div>
        <div class="col">
          <span class="tl">电话</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.mgTell"
            :disabled="true"
          >
          </el-input>
        </div>
        <!-- <div class="col">
          <span class="tl">邮箱</span>
          <el-input
            placeholder="请输入内容"
            v-model="formData.mgEmail"
            :disabled="true"
          >
          </el-input>
        </div> -->
      </div>
      <div class="item">
        <div class="col">
          <span class="tl">留言时间</span>
          <div class="pick">
            <el-date-picker
              v-model="formData.createdTime"
              type="date"
              placeholder="选择日期"
              :disabled="true"
            >
            </el-date-picker>
          </div>
        </div>
      </div>
      <div class="item">
        <div class="col sp">
          <span class="tl">留言内容</span>
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
            v-model="formData.mgContent"
            :disabled="true"
          >
          </el-input>
        </div>
      </div>
      <div class="item">
        <div class="col">
          <span class="tl">附件</span>
          <div v-if="formData.mgAccessory" class="imgList">
            <div
              class="box"
              v-for="(item, index) in formData.mgAccessory"
              :key="index"
            >
              <el-image
                style="width: 100px; height: 100px"
                :src="item.url"
                :preview-src-list="item.srcList"
              ></el-image>
            </div>
          </div>
          <div v-else>
            暂无附件
          </div>
        </div>
      </div>
      <div class="item">
        <div class="col sp">
          <span class="tl"><span style="color:red">*</span> 内容</span>
          <ms-editor class="editor" v-model="formData.replyContent"></ms-editor>
        </div>
      </div>
      <div class="footer">
        <el-button @click="save" type="primary">提交回复</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import msEditor from "@/components/ms-editor/index";
import { replyMessage } from "@/request/service.js";
import { escapeMap, unescapeMap } from "@/utils/constant.js";
export default {
  name: "leave-reply",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    msEditor,
  },
  data() {
    return {
      content: "",
      formData: {},
    };
  },
  mounted() {
    if (this.info) {
      this.formData = this.info;
      this.formData.content = this.unexcapeHtml(this.formData.content);
      if (this.formData.mgAccessory) {
        this.formData.mgAccessory = eval(this.formData.mgAccessory);
        let arr = [];
        this.formData.mgAccessory.forEach((val) => {
          arr.push(val.url);
          val.srcList = arr;
        });
      }
    }
  },
  methods: {
    save() {
      this.formData.content = this.excapeHtml(this.formData.content);
      let params = {
        id: this.formData.id,
        content: this.formData.replyContent,
      };
      replyMessage(params).then((res) => {
        if (res.status == 200) {
          this.goBack();
        }
      });
    },
    goBack() {
      this.$emit("go");
    },
    excapeHtml(content) {
      var escaper = function(match) {
        return escapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(escapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
    unexcapeHtml(content) {
      var escaper = function(match) {
        return unescapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(unescapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_leave-reply {
  .item {
    display: flex;
    margin-bottom: 20px;
    .col {
      display: flex;
      align-items: center;
      width: 30%;
      margin-right: 3%;
      .tl {
        width: 100px;
        text-align: right;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
      .imgList {
        display: flex;
        .box {
          margin-right: 10px;
        }
      }
      .el-input {
        width: 70%;
      }
    }
    .sp {
      width: 100%;
      margin-right: 0 !important;
      .el-textarea {
        width: 80%;
      }
    }
  }
  .footer {
    text-align: right;
  }
}
</style>
