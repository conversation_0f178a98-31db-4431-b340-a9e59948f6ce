<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               ref="departRef">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="`${item.adSpaceName} · ${item.identification}`"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-advertplace-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        this.api.getAdvertisementSpaceListByName({adSpaceName: ''}).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }, 
      change(val) {
        this.$nextTick(() => {
        //   selectVal = {
        //     id: val,
        //     name: this.$refs.advertRef.selectedLabel 
        //   }
          this.$emit('update:model', val)
        //   this.$emit('bindData', {model: selectVal, operation: 'department'})
        })
      }
    }
  }
</script>
