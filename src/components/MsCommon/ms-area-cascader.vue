<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 :clearable="!multiple"
                 size="mini"
                 :style="{width: width || '100%'}"
                 separator="-"
                 :placeholder="placeholder ? placeholder:'请选择'"
                 :show-all-levels="false"
                 :props="{children: 'child', label: 'name',value: 'id', emitPath: false,multiple: multiple}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  import {mapGetters} from 'vuex';
  export default {
    name: "ms-area-cascader",
    props: [
      "model",
      "label",
      "width",
      "disabled",
      "multiple",
      "modelName",
      "placeholder"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    computed: {
      ...mapGetters(["addressCode"])
    },
    watch: {
      model: function(val) {
        if (this.multiple) {
          if (val.length > 0) {
            this.result = val.map(v => {
              return v.id
            })
          }
        } else {
          this.result = val;
        }
      }
    },
    mounted() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        if (this.addressCode && this.addressCode.length > 0) {
          this.optionSearch = this.addressCode[0].child
        } else {
          this.api.getMedsciTreeAddress({levelType: 2}).then( response => {
            if(response.status === 200 && response.data && response.data.length > 0) {
              this.optionSearch = response.data[0].child
              this.$store.dispatch('SetAddressCode', response.data)
            }
            this.loading = false;
          })
        }
        
      }, 
      change(val) {
        let nodeContent = []
        let params = {}
        
        if (this.multiple) {
          let changeVal = val || []
          if (this.$refs['cascaderRef'].getCheckedNodes()) {
            this.$refs['cascaderRef'].getCheckedNodes().forEach(v => {
              if (changeVal.indexOf(v.value) !== -1) {
                nodeContent.push(v)
              }
            });
          }
          params = nodeContent
        } else {
          nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
          params = {
            operation: 'area',
            model: {
              id: nodeContent.length > 0 ? nodeContent[0].value : 0,
              name: nodeContent.length > 0 ? nodeContent[0].label : '',
              parentId: nodeContent.length > 0 ? nodeContent[0].parent.value : 0,
              parentName: nodeContent.length > 0 ? nodeContent[0].parent.label : 0,
            }
          }
          // 直接绑定值
          this.$emit('update:model', params.model.id)
          this.$emit('update:modelName', params.model.name)
        }
        // this.$emit('update:model', val)
        this.$emit('bindData', params)
      }
    }
  }
</script>
