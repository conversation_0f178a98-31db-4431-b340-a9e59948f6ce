<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                  clearable
                 :props="props"
                 ref="cascaderRef"
                 :style="{width: width || '100%'}"
                 :disabled="disabled"
                 @change="change"
                 v-if="updateCascader">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-area-lazycascader",
    props: [
      "model",
      "label",
      "width",
      "disabled",
      "multiple",
      "countryId"
    ],
    data() {
    //   const that = this;
      return {
        loading: false,
        result: [],
        optionSearch: [],
        props:{},
        updateCascader: true, // 更新cascader数据不回显问题
      }
    },
    created() {
        this.setValOptions(this.countryId)
    },
    destroyed() {
        this.$store.dispatch("SetDistrictId", 0)
    },
    watch: {
      model: function(val) {
          console.log(val)
          if (val && val.length > 0) {
            this.updateCascader = false
            this.result = val
            setTimeout(() => {
              this.updateCascader = true
            })
            
            this.$store.dispatch("SetDistrictId", val[2])
          }
      },
      countryId: function (val) {
        this.updateCascader = false
        setTimeout(() => {
          this.updateCascader = true
        })
        this.setValOptions(val)
      },
    },
    methods: {
      setValOptions (val) {
        this.props = {
            lazy: true,
            lazyLoad: (node,resolve) => {
                const { level } = node;
                const { label } = node;
                this.api.getMedsciAddress({
                    levelType: level+1,
                    parentId: node.data ? node.data.value : val
                }).then(response => {
                    if(response.status == 200){
                       let nodes = response.data.map(item => {
                        return {
                            value: item.id,
                            label: item.name,
                            leaf: level >=2
                        }
                      })
                      resolve(nodes)
                    } else {
                      let nodes = [{
                        value: -1,
                        label:label,
                        leaf: true
                      }]
                      resolve(nodes)
                    }
                })
            }
        }
      },
      change(val) {
        let nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
        let pathNodes = nodeContent && nodeContent.length > 0 && nodeContent[0] ? nodeContent[0].pathNodes : []
        let params = pathNodes.map(v => {
            return {
                id: v.value,
                name: v.label
            }
        })
        this.$store.dispatch("SetDistrictId", val[2])
        this.$emit('bindData', {
            operation: 'area',
            model: params
        })
      }
    }
  }
</script>
