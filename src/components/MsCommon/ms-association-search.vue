<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               remote
               :remote-method="getOptionData"
               :loading="loading"
               ref="associationRef"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.name"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-association-search",
    props: [
      "value",
      "label",
      "width",
      "modelName"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
        filterVal: '',
        initOption: []
      }
    },
    watch: {
      value: function(val) {
        this.result = val || 0
        if(val && this.optionSearch.length === 0) {
            this.optionSearch.push({
                id: val,
                name: this.modelName
            })
            this.initOption.push({
                id: val,
                name: this.modelName
            })
        }
      }
    },
    methods: {
      change(val) {
        this.$emit('input', val)
        this.$nextTick(() => {
          this.$emit('update:modelName', this.$refs.associationRef.selectedLabel)
        })
      },
      getOptionData(val) {
        if (val && val.length > 1) {
          this.loading = true;
          // 接口获取数据
          let params = {
            name : val 
          }
          this.api.getAssociationList(params).then(response => {
            this.loading = false
            let data = response.data || []
            let arr = [
              ...data,
              ...this.initOption
            ]
            this.optionSearch = arr
          })
        } 
        
      },
    }
  }
</script>
