<template>
  <ms-operation-dialog>
    <template slot="content">
		<p class="content-top">
			<span class="top-label"><i class="el-icon-document"></i></span><span class="top-content">{{dataObj.calendarTitle}}</span>
		</p>
		<p class="content-top">
			<span class="top-label"><i class="el-icon-time"></i></span><span class="top-content">{{dataObj.startAt | parseTime('{y}-{m}-{d}')}} ~ {{dataObj.endAt | parseTime('{y}-{m}-{d}')}}</span>
		</p>
        <div v-html="PUBLIC_Methods.unexcapeHtml(dataObj.content)" style="display: inline-block;margin-top: 15px;padding-right: 5px;padding-bottom: 20px;"></div>

		<!-- 相关文章资讯 -->
		<div class="related-article" v-if="relatedArtList && relatedArtList.length > 0">
			<p style="margin-bottom: 12px;"><strong>相关资讯文章</strong></p>
			<p v-for="(item,index) in relatedArtList" :key="index"><el-link :href="item.encodeId | linkFilter" target="_blank">{{item.title}}</el-link></p>
		</div>
		
    </template>
  </ms-operation-dialog>
</template>

<script>
import serveUrl from '@/store/data/serveUrl.js'
export default {
	name: "ms-calendar-preview",
	data () {
		return {
			dataObj: {},
			relatedArtList: []
		}
	},
	created() {
		this.init()
	},
	props: {
		model: Object,
		operation: String
	},
	filters: {
		linkFilter: function(val) {
			let link =  `${serveUrl['article']}${val}`
			return link
		}
	},
	methods: {
		init() {
			this.api.getOperaCalen({id: this.model.id}).then( response => {
				this.getLoading = false;
				if(response.status === 200) {
					this.dataObj = response.data
				} else {
					this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
				}
			}).catch(() => {
				this.getLoading = false;
			})
			this.api.getArticleListLikeByTitle({searchValue: this.model.calendarTitle}).then( response => {
				if (response.status === 200 && response.data) {
					this.relatedArtList = response.data
				}
			})
		}
	}
}
</script>

<style scoped>
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
	}
	.related-article {
		padding-bottom: 12px;
	}
	.related-article strong {
		font-size: 16px;
	}
	.related-article p{
		font-size: 14px;
		margin-bottom: 8px;
	}
</style>
