<template>
  <section class="category-check" :key="symbolKey">
    <template v-if="type =='question'">
      <div class="category-btn" v-for="(item,index) in checkOptions" :key="index">
        <input type="checkbox" :id="`cate_${index}`" :disabled="disabled" v-model="checkModel" :value="item" />
        <label :for="`cate_${index}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{item.categoryName}}</label>
      </div>
    </template>
    <template v-else>
      <div v-for="(item,index) in checkOptions" :key="index">
        <div :ref="'categoryId' + item.categoryId" class="flex" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;" :style="{height: item.titleEn === 'activity' && !show ? '56px' : 'auto', marginBottom: (item.categoryName === '其它科室' && config.moduleName == 'article')? heightTag + 'px' : '6px'}" >
          <template v-if="item.categoryName === '其它科室'">
            <span ref="otherDept" v-show="hasLabel" class="category-check-label">其它科室</span>
          </template>
          <template v-else>
            <span v-show="hasLabel" class="category-check-label">
              <!-- <span v-show="item.categoryName === '横向分类'" class="error-label">*</span> -->
              {{item.categoryName}}
            </span>
          </template>
          
          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemC, indexC) in item.children"
                :key="indexC"
                >
              <input type="checkbox" :id="`cate_${index}_${indexC}`" :disabled="disabled" v-model="checkModel" :value="itemC" />
              <label :for="`cate_${index}_${indexC}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemC.categoryName}}</label>
            </div>
          </div>
        </div>
        <div class="arrows" v-if="item.titleEn === 'activity'" :style="{transform: show ? 'rotate(180deg)' : 'rotate(0deg)'}" @click="show = !show"><i class="el-icon-arrow-down"></i></div>
      </div>
    </template>
  </section>
</template>

<script>
  export default {
    name: 'ms-category-check',
    data() {
      return {
        checkOptions: [],
        checkModel: [],
        hasLabel: this.config.notLabel ? false : true,
        hasBorder: this.config.hasBorder ? true : false,
        show: false,
        top: 0,
        symbolKey: '',
        oldCheckModel: [],
      }
    },
    props: {
      config: {
        type: Object,
        default: () => {
          return {}
        }
      },
      model: {
        type: Array,
        default: () => {
          return []
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      type: {
        type: String,
        default: ''
      },
      heightTag: {
        type: Number,
        default: 150
      },
    },
    watch: {
      model: function(val) {
        this.checkModel = val
      },
      checkModel: function(val) {
        let resfresh = false;
        let diff = {}
        if(this.oldCheckModel.length > val.length) {
          diff = this.oldCheckModel.filter(value => val.indexOf(value) == -1)[0]
        } else {
          diff = val[val.length-1]
        }
        this.checkOptions.forEach((item) => {
          item.children.forEach((child) => {
            if(child.categoryId == diff.categoryId) {
              if(item.categoryName == '内科' || item.categoryName == '外科' || item.categoryName == '其它科室') {
                resfresh = true
              }
            }
          })
        })
        this.oldCheckModel=val;
        this.$emit('changeCategoryT', resfresh)
        this.$emit('update:model', val)
        this.$emit('changeCategory', val)
      }
    },
    created() {
      this.symbolKey = Symbol(new Date().toString())
      if (this.model.length > 0) {
        this.checkModel = this.model
      }
      this.init()
    },
    mounted() {
    },
    updated() {
      // let otherTop = Number(window.sessionStorage.getItem('otherDeptTop'));
      // if(otherTop < 714) {
      //   otherTop = 714
      // }
      // if(otherTop > 750) {
      //   otherTop = 750
      // }
      // let top = otherTop + Number(this.$refs.otherDept[0].offsetTop) + Number(this.$refs.otherDept[0].clientHeight)
      // this.$emit('passTop', top)
      var that = this;
      setTimeout(function() {
        let top = Number(window.sessionStorage.getItem('otherDeptTop')) + Number(that.$refs.otherDept[0].offsetTop) + Number(that.$refs.otherDept[0].clientHeight)
        that.$emit('passTop', top)
      }, 1100)
    },
    destroyed() {
    },
    methods: {
      init() {
        let params = {
          module: this.config && this.config.moduleName || 'article'
        }
        this.api.getProjectModuleAndCategoryList(params).then(response => {
          let arr  = []
          if (response.status === 200) {
            let data = response.data
            for (let i = 0; i< data.length; i++) {
              let dataArr = {categoryId: data[i].categoryId, categoryName: data[i].titleCn, titleEn: data[i].titleEn,children: []}
              let children = data[i].children || []
              for (let j = 0; j < children.length; j++) {
                dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // if(data[i].titleCn == '内科' || data[i].titleCn == '外科' || data[i].titleCn == '其他科室') {
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant,parentId: data[i].categoryId})
                // } else {
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // }
              }
              arr.push(dataArr)
            }

            this.checkOptions = arr
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .arrows {
    cursor: pointer;
    margin-bottom: 16px;
    text-align: center;
    i {
      font-size: 14px;
    }
  }
  .category-check {
    &-label {
      width: 70px;
      font-size: 12px;
      // color: #4B74FF;
      color: #409EFF;
      text-align: right;
      margin-right: 20px;
      position: relative;
      top: 3px;
      font-weight: 600;
      .error-label {
        color: #F56C6C;
      }
    }
    &-group {
      width: 100%;
      text-align: left;
    }
    .category-btn {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 6px;
      position:relative;
      input[type="checkbox"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      label{
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color:#F0F2F5;
        font-size: 11px;
        font-weight: 400;
        display:inline-block;
        cursor: pointer;
      }
      input:checked+label {
        // background-color: #4B74FF;
        background-color: #409EFF;
        color: #fff;
      }
    }
  }
</style>
