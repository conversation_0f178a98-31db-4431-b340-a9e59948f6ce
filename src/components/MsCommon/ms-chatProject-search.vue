<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select
      size="mini"
      v-model="result"
      :style="{width: width || '100%'}"
      :loading="loading"
      @change="change"
      clearable
      :disabled="disabled"
    >
      <el-option
        v-for="(item,index) in optionSearch"
        :key="index"
        :label="item.projectName"
        :value="item.projectName"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: "ms-project-search",
  props: ["model", "label", "width", "disabled"],
  data() {
    return {
      loading: false,
      result: null,
      optionSearch: []
    };
  },
  watch: {
    model: function(val) {
      this.result = val;
    }
  },
  created() {
    this.result = this.model;
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      this.optionSearch = []
      this.api.medsciChatGetProjectName().then(response => {
        this.loading = false;
        this.optionSearch = response
      });
    },
    change(val) {
      this.$emit("update:model", val);
    }
  }
};
</script>
