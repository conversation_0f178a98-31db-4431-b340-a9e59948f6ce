<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :multiple="multiple"
               reserve-keyword
               :style="{width: width || '100%'}"
               remote
               :remote-method="filterMethod"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.name"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-company-search",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "multiple"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      modelName: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
    },
    methods: {
      filterMethod(val) {
        if (val && val.length >= 4) {
          this.getOptionData(val)
        }
      },
      change(val) {
        if (this.PUBLIC_Methods.isArrayFn(val)) {
          let params = val.map(v => {
            return v
          })
          this.$emit('update:model', params)
        } else {
          this.$emit('update:model', val)
          this.optionSearch.some(v => {
            if (v.id === val) {
              this.$emit('update:modelName', v.name)
              return 
            }
          })
        }
      },
      getOptionData(val) {
        this.loading = true;
        // 接口获取数据
        let params = {
          name : val ? val : ''
        }
        this.api.getErpCompanyDiclist(params).then(response => {
          this.loading = false
          if (response.status === 200 && response.data) {
            this.optionSearch = response.data
          }
        })
      },
    }
  }
</script>
