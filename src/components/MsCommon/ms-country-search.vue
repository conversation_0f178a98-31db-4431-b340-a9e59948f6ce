<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               ref="countryRef">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.name"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-country-search",
    props: [
      "model",
      "label",
      "width",
      "disabled",
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        this.api.getMedsciAddress({levelType: 0,parentId: 0}).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
            if(this.model) {
              this.result = this.model;
            }
          }
        })
      },
      change(val) {
        let selectVal = {}
        this.$nextTick(() => {
          selectVal = {
            id: val,
            name: this.$refs.countryRef.selectedLabel 
          }
          this.$emit('update:model', selectVal.id)
          this.$emit('update:modelName', selectVal.name )
          this.$emit('bindData', {model: selectVal, operation: 'country'})
        })
      }
    }
  }
</script>
