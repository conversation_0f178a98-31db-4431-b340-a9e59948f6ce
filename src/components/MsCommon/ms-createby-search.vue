<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               :clearable="page === 'article' ? false : true"
               filterable 
               ref="userRef"
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change">
        <template v-if="page === 'article'">
            <el-option v-for="(item,index) in listCreate"
            :key="index"
            :label="item.userName"
            :value="item.userId">
          </el-option>
        </template>
        <template v-else>
          <el-option v-for="(item,index) in optionSearch"
            :key="index"
            :label="item.userName"
            :value="item.id">
          </el-option>
        </template>
    </el-select>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  export default {
    name: "ms-createby-search",
    props: [
      "model",
      "label",
      "width",
      "modelName",
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
        page: '',
        listCreate: []
      }
    },
    computed: {
      ...mapGetters(["info", "createByList"])
    },
    watch: {
      model: function(val) {
        this.result = val
      },
      createByList: function (val) {
        if (val && val.length > 0) {
          this.optionSearch = val;
          this.loading = false
        }
      }
    },
    mounted() {
      if (this.createByList && this.createByList.length > 0) {
        this.optionSearch = this.createByList;
        this.loading = false
      } else {
        this.loading = true
      }
      if(this.$route.path == '/article-operation') {
        this.page = 'article';
        this.getListCreate()
      } else {
        this.page = '';
      }
      this.result = this.model;
      
    },
    methods: {
      getListCreate() {
        // 获取创建人信息
        let params = {}
        if(window.location.host.includes('backend.editor')) {
          params = {
            pageIndex: 1,
            pageSize: 150,
            roleId: 53
          }
        } else {
          params = {
            pageIndex: 1,
            pageSize: 150,
            roleId: 203
          }
        }
        this.api.getMedsciUserRolePage(params).then( response => {
          if(response.status === 200) {
            this.listCreate = response.data;
          }
        })
      },
      // init() {
      //   this.loading = true;
      //   let params = {
      //     "projectId": this.info.projectId || 1,
      //     "roleIds": null
      //   }
      //   this.api.getProjectIdAndRoleListDicList(params).then( response => {
      //     this.loading = false;
      //     if(response.status === 200) {
      //       this.optionSearch = response.data
      //     }
      //   })
      // }, 
      change(val) {
        console.log(val, 'vbbbbbbbbbbbbbbbbb')
        window.localStorage.setItem('ms_editorId', val)
        this.$nextTick(() => {
          this.$emit('update:model', val)
          this.$emit('update:modelName', this.$refs.userRef.selectedLabel )
        })
        
      }
    },
    destroyed() {
      window.localStorage.setItem('ms_editorId', '')
    }
  }
</script>

