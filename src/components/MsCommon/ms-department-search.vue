<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               :multiple="multiple"
               :placeholder="placeholder || '请选择'"
               @change="change"
               ref="departRef">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.departmentName"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-department-search",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "placeholder",
      "multiple",
      "type",
      "level"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
      if(this.model) {
        this.result = this.model
      }
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          parentId: -1
        }
        if(this.level) {
          params.level = this.level
        }
        this.api.getHospitalDepartmentList(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
            if(this.type == 'all') {
              this.optionSearch.unshift({
                id: -1,
                name: '全部',
                departmentName: '全部'
              })
            }
          }
        })
      }, 
      change(val) {
        if (this.PUBLIC_Methods.isArrayFn(val)) {
          let selectArr = []
          val.map(v => {
            selectArr.push({
              categoryId: v,
              categoryName: this.optionSearch.filter(i=>{return v==i.id})[0].name
            })
          })
          this.$emit('update:model', val)
          this.$emit('bindData', {model: selectArr, operation: 'department'})
        } else {
          let selectVal = {}
          this.$nextTick(() => {
            selectVal = {
              id: val,
              name: this.$refs.departRef.selectedLabel 
            }
            this.$emit('update:model', selectVal.id)
            this.$emit('update:modelName', selectVal.name )
            this.$emit('bindData', {model: selectVal, operation: 'department'})
          })
        }
      }
    }
  }
</script>
