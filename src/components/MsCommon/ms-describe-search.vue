<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable
               filterable 
               remote
               :remote-method="init"
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               >
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.userName"
                   :value="item">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-describe-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        if (val) {
          this.result = val
        }
      }
    },
    created() {},
    methods: {
      init(val) {
        this.loading = true;
        let params = {
          value: val
        }
        this.api.codeLibrarySelectUseFul(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }, 
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
