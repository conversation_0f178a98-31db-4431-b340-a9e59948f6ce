<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               :style="{width: width || '100%'}"
               :loading="loading"
               :clearable="clearable"
               @change="change"
               value-key="userId">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.label"
                   :value="item.value"
                   >
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-dictionary-search",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "type",
      "clearable",
      "disabled"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [
        {
          label: "资讯",
          value: "article",
        },
        {
          label: "课程",
          value: "course",
        },
        {
          label: "直播",
          value: "live_info",
        },
        {
          label: "病例",
          value: "case",
        },
        {
          label: "调研",
          value: "survey",
        },
        {
          label: "医讯达",
          value: "eda",
        },
        {
          label: "指南",
          value: "guider",
        },
        {
          label: "期刊",
          value: "tool_impact_factor",
        }
      ],
      }
    },
    watch: {
      model: function(val) {
        if (val) {
          this.result = val
        } else {
          this.result = null
        }
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          "type": this.type
        }
        this.loading = false;
       
      }, 
      change(val) {
        this.$emit('model', val)
        this.optionSearch.some(v => {
          if (v.id === val) {
            this.$emit('update:modelName', v.label)
            return 
          }
        })
      }
    }
  }
</script>
