<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               filterable
               clearable
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.userName"
                   :value="item.userId">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-editor-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          pageIndex: 0,
          pageSize: 999,
          roleId: 161,// 测试环境161
        }
        this.api.getMedsciUserRolePageV2(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }, 
      change(val) {
        this.$emit('update:model', val)
        this.optionSearch.some(v => {
          if (v.userId === val) {
            this.$emit('update:modelName', v.userName)
            return 
          }
        })
      }
    }
  }
</script>
