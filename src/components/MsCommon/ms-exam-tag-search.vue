<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               ref="userRef"
               :style="{width: width || '100%'}"
               :loading="loading"
               :placeholder="placeholder"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.tagName"
                   :value="item.tagId">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  export default {
    name: "ms-exam-tag-search",
    props: [
      "model",
      "label",
      "width",
      "modelName",
      "placeholder"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    computed: {
      ...mapGetters(["gatherId"])
    },
    watch: {
      model: function(val) {
        this.result = val
      }
    },
    mounted() {
      this.init(this.gatherId)
    },
    methods: {
      init(id) {
        this.loading = true;
        this.api.getExamPaperTagsById({gatherId:id}).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }, 
      change(val) {
        this.$nextTick(() => {
          this.$emit('update:model', val)
          this.$emit('update:modelName', this.$refs.userRef.selectedLabel )
        })
        
      }
    }
  }
</script>
