<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               filterable 
               multiple=""
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               value-key="userId">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.userName"
                   :value="item">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  export default {
    name: "ms-executor-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    computed: {
    ...mapGetters(["info"])
    },
    watch: {
      model: function(val) {
        if (val) {
          this.result = val
        }
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          "projectId": this.info.projectId || 1
        }
        this.api.getUserByAdminRoleDicList(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data.map(v => {
              return {
                userId: v.id,
                userName: v.realName
              }
            })
          }
        })
      }, 
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
