<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               ref="goodsRef">
        <el-option
            v-for="item in optionSearch"
            :key="item.categoryId"
            :label="item.titleCn"
            :value="item.categoryId">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-goods-category",
    props: [
      "model",
      "label",
      "width",
      "modelName"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true
        this.api.getProjectModuleAndCategoryList({module: 'shop'}).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data[0].children
          }
        })
      },
      change(val) {
        this.$nextTick(() => {
          this.$emit('update:model', val)
          this.$emit('update:modelName', this.$refs.goodsRef.selectedLabel )
        })
      }
    }
  }
</script>
