<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               ref="hospitalRef"
               v-if="optionSearch.length > 0">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.name"
                   :value="item.id">
        </el-option>
    </el-select>
    <el-input v-model="result" v-else @change="change"></el-input>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
  export default {
    name: "ms-hospitalArea-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    computed: {
        ...mapGetters(["districtId"])
    },
    watch: {
      model: function(val) {
        this.result = val;
      },
      districtId: function(val) {
        this.init(val)
      }
    },
    methods: {
      init(val) {
        this.loading = true;
        this.api.getHospitalListByAddress({districtId: val}).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data ? response.data : []
          }
        })
      }, 
      change(val) {
        let selectVal = {}
        if (this.optionSearch.length > 0) {
            this.$nextTick(() => {
            selectVal = {
                id: val,
                name: this.$refs.hospitalRef.selectedLabel 
            }
            this.$emit('update:model', selectVal.name)
            this.$emit('bindData', {model: selectVal, operation: 'hospital'})
            })
        } else {
            this.$emit('update:model', val)
            this.$emit('bindData', {model: {id: 0, name: val}, operation: 'hospital'})
        }
      }
    }
  }
</script>
