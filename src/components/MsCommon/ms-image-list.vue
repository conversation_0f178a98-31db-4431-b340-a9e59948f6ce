<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <template v-if="result && result.length > 0">
        <div class="img-list">
            <el-image v-for="(item, index) in result" :key="index" :src="item" :preview-src-list="result"></el-image>
        </div>
    </template>
    <template v-else>
        <div class="no-img-list" >暂无附件</div>
    </template>
  </div>
</template>

<script>
  export default {
    name: "ms-image-list",
    props: [
      "model",
      "label"
    ],
    data() {
      return {
        result: null,
      }
    },
    created() {
        if (this.model && this.model.length > 0) {
            this.result = this.model
        }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    }
  }
</script>

<style lang="scss">
    .img-list .el-image>img {
        width: 120px;
        height: 120px;
        -o-object-fit: cover;
        object-fit: cover;
        margin: 0 10px 10px 0;
        border: 1px solid #E4E7ED;
    }
    .no-img-list {
        width: 120px;
        height: 48px;
        line-height: 48px;
        border-radius: 3px;
        background-color: #F5F7FA;
        color: #C0C4CC;
        text-align: center;
        font-size: 14px;
        border: 1px solid #E4E7ED;
    }
</style>
