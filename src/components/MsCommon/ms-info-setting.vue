<template>
  <section>
    <el-form class="rule-form"
             label-width="70px">
      <el-row v-if="headerShow">
        <el-col :span="14">
          <el-form-item label="已选专题" prop="selectCategory">
            <div style="border-bottom: 1px solid #E4E7ED;min-height: 30px;">
              <el-tag v-for="(item, index) in checkCategory"
                      :key="index"
                      style="margin-right: 5px;"
                      closable
                      @close="categoryClose(index)">{{item.categoryName}}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="5" :offset="5" v-if="categoryType!='question'">
          <!-- <el-input placeholder="请输入关键词搜索"></el-input> -->
          <ms-category-cascader @getNodeData="getNodeData" width="100%" :config="categoryConfig"></ms-category-cascader>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <ms-category-check :model.sync="checkCategory" :type="categoryType" :config="categoryConfig" @changeCategory="changeCategory" :heightTag="heightTag" ref="categoryCheck" @passTop='passTop' @changeCategoryT="changeCategoryT"></ms-category-check>
          <!-- 专题课程-投放项目 -->
          <ms-project-check v-if="categoryType=='specialVideos'" :model.sync="checkProject" @changeProject="changeProject"></ms-project-check>
          <!-- 专题课程-投放项目 -->
          <ms-media-check v-if="categoryType=='mediaVideos'" :model.sync="checkProjectId" @changeProjectId="changeProjectId"></ms-media-check>
          <!-- slot组件 -->
          <slot name="category-tmp"></slot>
        </el-col>
      </el-row>
      <!-- <el-row v-if="labelShow">
        <el-form-item class="label-tag"
          label="内标签"
        >
            <el-cascader ref="cascaderRefArea"
              v-model="labelList"
              :props="props"
              style="width: 300px;"
              @change="labelListChange"
              :options="optionsList"
            ></el-cascader>
        </el-form-item>
      </el-row> -->
      <el-row v-if="labelShow" class="biaoqian-wrap" :style="{top: scrollTop + 'px'}" ref="biaoqian">
        <div class="flex category-check" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;height: auto;">
          <span class="category-check-label">亚专业</span>
          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemA, indexA) in labelList"
                :key="indexA"
                >
              <input type="checkbox" :id="`cate_${indexA}`" :disabled="disabled" v-model="checkModelA" :value="itemA.value" @change="checkModelAChange" />
              <label :for="`cate_${indexA}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemA.label}}</label>
            </div>
          </div>
        </div>
        <div class="flex category-check" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;height: auto;">
          <span class="category-check-label">疾病</span>
          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemB, indexB) in diseaseList"
                :key="indexB"
                >
              <input type="checkbox" :id="`disease_${indexB}`" :disabled="disabled" v-model="checkModelB" :value="itemB.id" />
              <label :for="`disease_${indexB}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemB.tagName}}</label>
            </div>
          </div>
        </div>
        <div class="flex category-check" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;height: auto;">
          <span class="category-check-label">治疗技术</span>
          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemC, indexC) in cureList"
                :key="indexC"
                >
              <input type="checkbox" :id="`cure_${indexC}`" :disabled="disabled" v-model="checkModelC" :value="itemC.id" />
              <label :for="`cure_${indexC}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemC.tagName}}</label>
            </div>
          </div>
        </div>
      </el-row>
    </el-form>
  </section>
</template>
<script>
import MsCategoryCheck from '@/components/MsCommon/ms-category-check'
import MsProjectCheck from '@/components/MsCommon/ms-project-check'
import MsMediaCheck from '@/components/MsCommon/ms-media-check'
import api from "./../../api/index"
let mountedStatus = false
const getData = function(node, checkCategory) {
  return new Promise((resolve) => {
    let searchParams = {
      id: node&&node.value ? node.value : 0,
      // pageIndex: 1,
      // pageSize: 999,
      type: node&&node.value ? 2 : 1,
      categoryIds: [],
      parentIds: [],
    }
    if(checkCategory) {
      searchParams.categoryIds = []
      for (let index = 0; index < checkCategory.length; index++) {
        const element = checkCategory[index];
        searchParams.categoryIds.push(element.categoryId)
      }
    } else {
      return false
    }
    api.tagLabelGetChildClaim(searchParams).then(response => {
      let array = response.data;
      if(!mountedStatus) {
        setTimeout(function() {
          mountedStatus = true;
        }, 1000)
      }
      if(array) {
        const nodes = array.map(item => ({
          value: item.value || item.id,
          label: item.label || item.tagName,
          leaf: node&&(node.level > 1) ? true : false
        }));
        // 通过调用resolve将子节点数据返回，通知组件数据加载完成
        resolve(nodes);
      } else {
        resolve([]);
      }
    })
  })
}
export default {
	name: "ms-info-setting",
	data () {
		return {
      symbolKey: '',
      checkCategory: [],
      checkProject: [],
      checkProjectId: '',
      categoryConfig: {
        moduleName: 'article',
        hasBorder: true
      },
      labelArray: ['/article-operation'],
      labelList: [],
      labelShow: false,
      optionsList: [],
      // props: {
      //   checkStrictly: true,  
      //   multiple: true,
      //   lazy: true,
      //   lazyLoad: this.lazyLoads,
			// 	leaf: "leaf", // 需要指定叶子节点，不然显示有问题
      // },
      disabled: false,
      checkModelA: [],
      checkModelB: [],
      checkModelC: [],
      diseaseList: [],
      cureList: [],
      mountedStatus: false,
      heightTag: 150,
      scrollTop: 3000,
      refresh: false,
      oldCheckModel: [],
		}
  },
  props: {
    infoId: {
      type: Number,
      default: 0
    },
    headerShow: {
      type: Boolean,
      defalut: true
    },
    categoryModel: {
      type: Array,
      default: () => {
        return []
      }
    },
    labelListModel: {
      type: Array,
      default: () => {
        return []
      }
    },
    projectModel: {
      type: Array,
      default: () => {
        return []
      }
    },
    projectIdModel: {
      type: Number,
      default: 1
    },
    categoryConfigChild: {
      type: Object,
      default: () => {
        return {}
      }
    },
    categoryType: {
      type: String,
      default: ''
    }
  },
  components: {
    MsCategoryCheck,
    MsProjectCheck,
    MsMediaCheck
  },
  updated() {
    var that = this;
    setTimeout(function() {
      let heg = that.$refs.biaoqian.$el.offsetHeight;
      if(heg > 120) {
        that.heightTag = that.$refs.biaoqian.$el.offsetHeight + 30
      }
    }, 1000)
  },
  watch: {
    // 亚专业列表变化时亚专业值应该初始化为空
    // labelList: function() {
    //   if(mountedStatus){
    //     this.checkModelA = [];
    //   }
    //   // this.$nextTick(() => {
    //   //   this.heightTag = this.$refs.biaoqian.$el.offsetHeight + 60
    //   // })
    // },
    // 亚专业值变化时，疾病，治疗技术初始化
    checkModelA: function(val) {
      if(mountedStatus){
        this.$emit('modifyLabelList', [val, this.checkModelB, this.checkModelC])
      }
      if(val.length) {
        this.getChildList('亚专业', val);
      } else {
        this.diseaseList = [];
        this.checkModelB = [];
        this.cureList = [];
        this.checkModelC = [];
      }
    },
    // diseaseList: function() {
    //   if(mountedStatus){
    //     this.checkModelB = [];
    //   }
    //   // this.$nextTick(() => {
    //   //   this.heightTag = this.$refs.biaoqian.$el.offsetHeight + 60
    //   // })
    // },
    checkModelB: function(val) {
      if(mountedStatus){
        this.$emit('modifyLabelList', [this.checkModelA, val, this.checkModelC])
      }
      if(val.length) {
        this.getChildList('疾病', val);
      } else {
        this.cureList = [];
        this.checkModelC = [];
      }
    },
    cureList: function(value) {
      // if(mountedStatus){
      //   this.checkModelC = [];
      // }
      console.log(value, 'this.cureList')
      let array = [];
      if(this.checkModelC.length) {
        for (let index = 0; index < value.length; index++) {
          const element = this.checkModelC[index];
          value.forEach(function(el) {
            if(el.id == element) {
              array.push({
                "labelId": el.id,
                "labelName": el.tagName,
              })
            }
          })
        }
      }
      this.$emit('modifyLabel', array)
    },
    checkModelC: function(val) {
      if(mountedStatus){
        this.$emit('modifyLabelList', [this.checkModelA, this.checkModelB, val])
        let array = [];
        if(val.length) {
          for (let index = 0; index < val.length; index++) {
            const element = val[index];
            this.cureList.forEach(function(el) {
              if(el.id == element) {
                array.push({
                  "labelId": el.id,
                  "labelName": el.tagName,
                })
              }
            })
          }
        }
        this.$emit('modifyLabel', array)
      }
    },
    // labelListModel: function(val) {
    //   mountedStatus = false
    //   if(Array.isArray(val)) {
    //     this.checkModelA = val[0] ? val[0] : [];
    //     this.checkModelB = val[1] ? val[1] : [];
    //     this.checkModelC = val[2] ? val[2] : [];
    //   }
    // },
    categoryModel: async function(val) {
      console.log(val, '666666')
      // if(this.oldCheckModel.length > val.length) {
      //   let diff = this.oldCheckModel.filter(value => val.indexOf(value) == -1)
      //   refresh = diff[0].parentId ? true : false
      // } else {
      //   refresh = val[val.length-1].parentId ? true : false
      // }
      // this.oldCheckModel=val;
      this.initTag(this.refresh);
      // this.checkCategory = val
      // // 资讯管理菜单下显示内标签
      // if(this.labelShow) {
      //   // this.$refs.cascaderRefArea.panel.lazyLoad();
      //   let res = await getData(null, this.checkCategory); // 拿一级数据
      //   this.labelList = res;
      //   // 初始化
      //   // if(mountedStatus){
      //   //   this.diseaseList = [];
      //   //   this.cureList = [];
      //   //   this.checkModelA = [];
      //   //   this.checkModelB = [];
      //   //   this.checkModelC = [];
      //   // }
      // }
      // if(this.labelArray.includes(this.$route.path)) {
      //   this.labelShow = true;
      // } else {
      //   this.labelShow = false;
      // }
    },
    projectModel: function(val) {
      this.checkProject = val
    },
    projectIdModel: function(val) {
      this.checkProjectId = val
    },
  },
  created() {
    if (this.categoryModel.length > 0) {
      this.checkCategory = this.categoryModel
    }
    if (this.projectModel.length > 0) {
      this.checkProject = this.projectModel
    }
    if (this.projectIdModel) {
      this.checkProjectId = this.projectIdModel
    }
    this.categoryConfig = {...this.categoryConfig, ...this.categoryConfigChild};
  },
  mounted() {
    mountedStatus = false;
    // this.initTag();
  },
	methods: {
    checkModelAChange() {
      console.log(this.checkModelA, '44')
    },
    initTag(refresh) {
      var that = this;
      setTimeout(async() => {
        // 思路：如果是编辑进来，categoryModel传值进来；labelShow已经改为true,则下面值变化对页面不做影响
        // 如果是新建，labelShow为true内标签则显示出来
        that.checkCategory = that.categoryModel;
        // 如果科室存在表示是编辑，
        if(that.labelArray.includes(that.$route.path)) {
          that.labelShow = true;
          if(mountedStatus) {
            if(refresh) {
              console.log(that.oldCheckModel, 'that.oldCheckModel')
              if(that.oldCheckModel.length > that.checkCategory.length) {
                if(that.checkCategory) {
                  let res = await getData(null, that.checkCategory); // 拿一级数据
                  that.labelList = res;
                }
                let deleA = []
                that.checkModelA.forEach((modela) => {
                  let diffA = that.labelList.filter(a => a.value == modela)
                  if(diffA.length == 0) {
                    deleA.push(modela)
                  }
                })
                that.checkModelA = that.checkModelA.filter(x => deleA.indexOf(x) == -1)
                console.log(that.checkModelA, 'that.checkModelA')
              } else {
                if(that.checkCategory) {
                  let res = await getData(null, that.checkCategory); // 拿一级数据
                  that.labelList = res;
                }
              }
              // that.diseaseList = [];
              // that.cureList = [];
              // that.checkModelA = [];
              // that.checkModelB = [];
              // that.checkModelC = [];
              // that.$emit('modifyLabelList', [that.checkModelA, that.checkModelB, that.checkModelC])
              // if(that.checkCategory) {
              //   let res = await getData(null, that.checkCategory); // 拿一级数据
              //   that.labelList = res;
              // }
            }
            that.oldCheckModel = that.categoryModel;
          } else {
            that.oldCheckModel = that.categoryModel;
            if(!that.labelListModel) {
              that.checkModelA = [];
              that.checkModelB = [];
              that.checkModelC = [];
            } else {
              that.checkModelA = that.labelListModel[0] ? that.labelListModel[0]: [];
              that.checkModelB = that.labelListModel[1] ? that.labelListModel[1]: [];
              that.checkModelC = that.labelListModel[2] ? that.labelListModel[2]: [];
            }
            that.$emit('modifyLabelList', [that.checkModelA, that.checkModelB, that.checkModelC])
            if(that.categoryModel) {
              let res = await getData(null, that.checkCategory); // 拿一级数据
              that.labelList = res;
            }
            if(that.checkModelA.length) {
              that.getChildList('亚专业', that.checkModelA); // 拿二级数据
            }
            if(that.checkModelB.length) {
              that.getChildList('疾病', that.checkModelB); // 拿三级数据
            }
          }
        } else {
          that.labelShow = false;
        }
      }, 1000)
    },
    passTop(value) {
      this.scrollTop = value + 50;
    },
    getChildList(type, array) {
      let params = {
        // 'ids': array,
        type: 2,
        categoryIds: [],
        parentIds: array,
      }
      if(this.checkCategory) {
        for (let index = 0; index < this.checkCategory.length; index++) {
          const element = this.checkCategory[index];
          params.categoryIds.push(element.categoryId)
        }
      } else {
        return false
      }
      api.tagLabelGetChildClaim(params).then(response => {
        if(response.status === 200) {
          let array = response.data;
          if(type == '亚专业') {
            this.diseaseList = array;
            let deleB = []
            this.checkModelB.forEach((modelB) => {
              let diffB = this.diseaseList.filter(a => a.id == modelB)
              if(diffB.length == 0) {
                deleB.push(modelB)
              }
            })
            this.checkModelB = this.checkModelB.filter(x => deleB.indexOf(x) == -1)
            console.log(this.checkModelB, 'that.checkModelB')
          } else {
            this.cureList = array;
            let deleC = []
            this.checkModelC.forEach((modelC) => {
              let diffC = this.cureList.filter(a => a.id == modelC)
              if(diffC.length == 0) {
                deleC.push(modelC)
              }
            })
            this.checkModelC = this.checkModelC.filter(x => deleC.indexOf(x) == -1)
            console.log(this.checkModelC, 'that.checkModelC22')
          }
        }
      })
    },
    // async lazyLoads(node, resolve) {
    //   if (node.level == 0) {
    //     if (this.labelList.length > 0) {
    //       // 存在回显的id我们才去设置回显
    //       let res = await this.format(); // 这里就是获取第一次要回显的内容了
    //       if(res.length === 0) {
    //         resolve([])
    //       } else {
    //         this.optionsList = res; // 有回显第一次就直接给绑定的对象，不要resolve
    //       }
    //     } else {
    //       const nodes = await getData(node, this.checkCategory);
    //       if(!nodes.length) {
    //         if(node.data) {
    //           node.data.leaf = true;
    //         }
    //       }
    //       resolve(nodes);
    //     }
    //   } else {
    //     // 记录选择的，不然会点击字节点加载时丢失数据
    //     // 有children属性了就不要再请求了,不然字节点会重复
    //     if (!node.data.children) {
    //       // 获取子节点数据
    //       let res = await getData(node, this.checkCategory);
    //       resolve(res);
    //       // setTimeout(() => {
    //       //   // 模拟延时，实际不需要
    //       //   resolve(res);
    //       // }, 100);
    //     } else {
    //       resolve([]);
    //     }
    //     // 重新把选择的id放回来-
    //     // setTimeout(() => {
    //     //   this.labelList = [...new Set([...this.list, ...list])]; //差别： 合并并且去重
    //     // }, 250);
    //   }
    // },
    // 这个方法不变哦
    // 根据回显的id获取 第一次的数据， id必须是 一条链， 就是当前节点到此节点的一级节点的整条链 id 都要有
    // 第一级循环之间异步，第二级循环之间同步，等所有执行完返回一个结果
    format() {
      return new Promise(async(resolve) => {
        let res = await getData(null, this.checkCategory); // 拿一级数据
        if(res.length === 0) {
          resolve(res)
          return false
        }
        let arrP = []
        let checkCategory = this.checkCategory;
        this.labelList.forEach((item) => {
          // 遍历回显的id 二维数组
          // 里面多级最好改成递归
          if(item.length > 1) {
            arrP.push(new Promise(async(resolve) => {
              const element = item[0]
              let node = {
                value: element,
                level: 1,
              }
              let arr = await getData(node, checkCategory);
              findItem(res, arr, element)
              if(item.length > 2) { // 就是3级
                const element = item[1]
                let node = {
                  value: element,
                  level: 2,
                }
                let arr = await getData(node, checkCategory);
                findItem(res, arr, element)
              }
              resolve()
            }))
          }
        });
        // 递归判断
        function findItem(res, arr, id) {
          for (let i = 0; i < res.length; i++) {
            if (res[i].value === id) {
              res[i].children = arr; // 有chidlren 也要判断是不是这个children下的子级
              return res;
            }
            if (res[i].children) {
              findItem(res[i].children, arr, id);
            }
          }
        }
        Promise.all(arrP).then(() => {
          console.log(res, 'result')
          resolve(res)
        })
      })
    },
    labelListChange(val) {
      if(val) {
        this.$emit('modifyLabelList', val)
      }
    },
    changeCategory(val) {
      if(val) {
        this.$emit('update:categoryModel', val)
      }
    },
    changeCategoryT(val) {
      this.refresh = val
    },
    changeProject(val) {
      if(val) {
        this.$emit('update:projectModel', val)
      }
    },
    changeProjectId(val) {
      if(val) {
        this.$emit('update:projectIdModel', val)
      }
    },
    categoryClose(index) {
      this.checkCategory.splice(index, 1)
      this.$emit('update:categoryModel', this.checkCategory)
    },
    getNodeData(data) {
      if (data) {
        let params = [...this.checkCategory,{
          categoryId: data.data.categoryId,
          categoryName: data.data.titleCn,
          tenant: data.data.tenant
        }]
        let obj = {}
        this.checkCategory = params.reduce((res,next) => {
          obj[next.categoryId] ? "" : obj[next.categoryId] = true && res.push(next)
          return res
        }, [])
      }
    },
	},
  destroyed() {
    mountedStatus = false;
  }
}
</script>
<style lang="scss" scoped>
.label-tag >>> .el-form-item__label{
  width: 70px;
  font-size: 12px;
  color: #409EFF;
  text-align: right;
  margin-right: 14px;
  font-weight: 600 !important;
  padding-right: 6px;
}
.biaoqian-wrap{
  width: 100%;
  position: absolute;
  top: 500px;
  left: 1px;
}
.category-check {
    &-label {
      width: 70px;
      font-size: 12px;
      // color: #4B74FF;
      color: #409EFF;
      text-align: right;
      margin-right: 20px;
      position: relative;
      top: 3px;
      font-weight: 600;
      .error-label {
        color: #F56C6C;
      }
    }
    &-group {
      width: 100%;
      text-align: left;
    }
    .category-btn {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 6px;
      position:relative;
      input[type="checkbox"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      label{
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color:#F0F2F5;
        font-size: 11px;
        font-weight: 400;
        display:inline-block;
        cursor: pointer;
      }
      input:checked+label {
        // background-color: #4B74FF;
        background-color: #409EFF;
        color: #fff;
      }
    }
  }
</style>