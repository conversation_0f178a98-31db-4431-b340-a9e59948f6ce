<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.title"
                   :value="item.title">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-jobtite-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true
        this.api.getRecruitmentJobTitle({searchValue: ''}).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data
          }
        })
      },
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
