<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               remote
               :remote-method="filterMethod"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.fullname"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-journal-search",
    props: [
      "value",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
        filterVal: '',
        initOption: []
      }
    },
    watch: {
      value: function(val) {
        this.result = val || 0
        if(val && this.optionSearch.length === 0) {
          this.init(val)
        }
      }
    },
    methods: {
      init(id) {
        this.loading = true;
        this.api.showJournal({id: id}).then( response => {
          this.loading = false;
          if(response.status === 200) {
            let res = response.data
            let data = {
              id: res.id,
              fullname: res.fullname,
              abbr: res.abbr,
              issn: res.issn,
              impactFactor: res.impactFactor
            }
            this.initOption.push(data)
            this.optionSearch.push(data)
          }
        })
      }, 
      filterMethod(val) {
        if(val && val.length >= 4) {
          this.getOptionData(val)
        }
      },
      change(val) {
        this.$emit('input', val)
      },
      getOptionData(val) {
        this.loading = true;
        // 接口获取数据
        let params = {
          title : val ? val : ''
        }
        this.api.searchJournal(params).then(response => {
          this.loading = false
          let data = response.data || []
          let arr = [
            ...data,
            ...this.initOption
          ]
          this.optionSearch = arr
        })
      },
    }
  }
</script>
