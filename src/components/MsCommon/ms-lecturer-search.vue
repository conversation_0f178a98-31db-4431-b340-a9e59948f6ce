<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change"
               ref="lecturerRef">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.realName"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-lecturer-search",
    props: [
      "model",
      "modelName",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true
        this.api.getMedsciLecturerDicList({}).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data
          }
        })
      },
      change(val) {
        this.$emit('update:model', val)
        this.optionSearch.some(v => {
          if (v.id === val) {
            this.$emit('update:modelName', v.realName)
            return 
          }
        })
      }
    }
  }
</script>
