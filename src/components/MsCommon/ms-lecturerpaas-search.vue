<template>
  <div :class="{ 'input-label': label }">
    <label v-show="label">{{ label }}</label>
    <el-select
      size="mini"
      v-model="result"
      clearable
      filterable
      remote
      :style="{ width: width || '100%' }"
      :loading="loading"
      @change="change"
      ref="lecturerRef"
      :remote-method="remoteMethod"
    >
      <el-option
        v-for="(item, index) in optionSearch"
        :key="index"
        :label="item.realName"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: "ms-lecturer-search",
  props: ["model", "modelName", "label", "width"],
  data() {
    return {
      loading: false,
      result: null,
      optionSearch: []
    }
  },
  watch: {
    model: function(val) {
      if (val) {
        this.result = val;
        this.init()
      }
    },
  },
  created() {
    this.result = this.model
    this.init()
  },
  methods: {
    init() {
      if (this.model) {
          this.optionSearch = [{
            id: this.model,
            realName: this.modelName
          }]
      }
    },
    change(val) {
      this.$emit("update:model", val)
      this.$nextTick(() => {
        this.$emit('update:modelName', this.$refs.lecturerRef.selectedLabel)
      })
    },

    remoteMethod(v) {
      this.loading = true;
      const params = {
        pageIndex: 1,
        pageSize: 30,
        name: v
      }
      this.api.getMedsciSpeakeLike(params).then(res => {
        this.loading = false
        let data = res.data ? res.data : []
        this.optionSearch = data.map(item => {
          return {
            id: item.id,
            realName: item.name
          }
        })
      })
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
