<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 size="mini"
                 :clearable="true"
                 :style="{width: width || '100%'}"
                 separator="-"
                 :show-all-levels="false"
                 :props="{children: 'children', label: 'name',value: 'id', emitPath: false, multiple: multiple}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-livecategory-cascader",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "disabled",
      "config",
      "multiple"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        if(this.multiple && val) {
          this.result = val.map(v => {
            return v.categoryId
          })
        } else {
          this.result = val;
        }
      }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.loading = true;
            this.api.getLiveCategoryDicList().then( response => {
                if(response.status === 200) {
                    this.optionSearch = response.data || []
                }
                this.loading = false;
            })
        }, 
      change(val) {
        let nodeContent = []
        let params = {}
        if (this.multiple) {
          let changeVal = val || []
          if (this.$refs['cascaderRef'].getCheckedNodes()) {
            this.$refs['cascaderRef'].getCheckedNodes().forEach(v => {
              if (changeVal.indexOf(v.value) !== -1) {
                nodeContent.push({
                  categoryId: v.value,
                  categoryName: v.label
                })
              }
            });
          }
          params = nodeContent
          this.$emit('update:model', params)
        } else {
          nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
          this.$emit('update:model', val)
          this.$emit('update:modelName', nodeContent.length > 0 ? nodeContent[0].label : '')
          this.$emit('getNodeData', nodeContent[0])
        }
      }
    }
  }
</script>
