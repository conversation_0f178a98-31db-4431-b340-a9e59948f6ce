<template>
  <div :class="{ 'input-label': label }">
    <label v-show="label">{{ label }}</label>
    <el-select
      size="mini"
      v-model="result"
      clearable
      filterable
      remote
      :style="{ width: width || '240px' }"
      :loading="loading"
      :remote-method="remoteMethod"
      placeholder="请选择视频素材"
    >
      <el-option
        v-for="(item, index) in optionSearch"
        :key="index"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
    <el-scrollbar class="icon-row" :style="{height: scrollHeight}">
        <div class="video-choose clearfix" v-loading="loading">
          <div class="video-choose-item" v-for="(item,index) in optionSearch" :key="index">
            <input type="radio" :id="`VIDEO_${index}`" :value="item.id" name="videoChoose" v-model="result"/>
            <label :for="`VIDEO_${index}`">
              <el-image style="width: 100%; height: 100%" :src="item.cover" fit="cover"></el-image>
              <i class="el-icon-video-play" style="position:absolute; left: 38%; top: 32%;color: rgba(255,255,255,.7);font-size: 30px;cursor: pointer;" title="播放" @click="openVideo(item.id)"></i>
              <span class="choose-radio">
                <i class="el-icon-upload-success el-icon-check"></i>
              </span>
            </label>
          </div>
        </div>
      </el-scrollbar>
  </div>
</template>

<script>
export default {
  name: "ms-materialVideo-search",
  props: ["model", "modelName", "label", "width"],
  data() {
    return {
      loading: false,
      result: null,
      optionSearch: [],
      scrollHeight: '110px'
    }
  },
  watch: {
    model: function(val) {
      this.result = val;
    },
    result: function(val) {
      this.$emit("update:model", val)
      if(val) {
        let cover = this.optionSearch.filter(item=>{return item.id == val})[0].cover || ''
        this.$emit("update:modelCover", cover)
      }
    }
  },
  created() {
    this.remoteMethod()
  },
  methods: {
    // change(val) {
    //   this.$emit("update:model", val)
    // },

    remoteMethod(v) {
      this.loading = true;
      const params = {
        pageIndex: 1,
        pageSize: 12,
        title: v,
        status: 1
      }
      this.api.getVideoMaterialList(params).then(res => {
        this.loading = false
        let data = res.data ? res.data : []
        if (res.data.length > 3) {this.scrollHeight = '190px'}
        this.optionSearch = data.map(item => {
          return {
            id: item.videoUrl,
            name: item.title,
            cover: item.cover
          }
        })
      })
    },
    openVideo (url) {
      window.open(url, '_blank');
    }
  },
}
</script>

<style lang="scss">
  .icon-row .el-scrollbar__wrap {
      overflow-x: hidden !important; 
  }

  .video-choose {
    margin: 12px 0 0;
    &-item {
      width: 115px;
      height: 75px;
      border: 1px solid #c0ccda;
      border-radius: 6px;
      margin: 0 8px 8px 0;
      overflow: hidden;
      float: left;
      position:relative;
      cursor: pointer;
      input[type="radio"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      label{
        width: 100%;
        height: 100%;
        display: inline-block;
        background-size: cover;
        background-repeat: no-repeat;
      }
      input:checked+label {
        .choose-radio {
          display: block;
        }
      }
      .choose-radio {
        position: absolute;
        right: -15px;
        top: -6px;
        width: 40px;
        height: 24px;
        background: #13ce66;
        text-align: center;
        transform: rotate(45deg);
        box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
        display: none;
        i {
          font-size: 12px;
          margin-top: 11px;
          transform: rotate(-45deg);
          color: #fff;
        }
      }
    } 
  }
  /* .icon-row .icon-col {
    text-align: center;
    cursor: pointer;
  } */
</style>
