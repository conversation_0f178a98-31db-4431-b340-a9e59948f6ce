<template>
  <section class="category-check">
    <div >

      <div class="flex" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;">
        <span class="category-check-label">
          <span class="error-label">* </span>投放项目
        </span>
        
        <div class="category-check-group">
          <!-- <div class="category-btn" 
              v-for="(item, index) in checkOptions"
              :key="index"
              >
            <input type="checkbox" :id="`project_${index}_${index}`" :disabled="disabled" v-model="checkModel" :value="item.projectId" />
            <label :for="`project_${index}_${index}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{item.projectName}}</label>
          </div> -->
          <el-radio-group class="category-btn" v-model="checkModel">
            <el-radio v-for="(item, index) in checkOptions"
              :key="index" :label="item.projectId">{{item.projectName}}</el-radio>
          </el-radio-group>
        </div>
      </div>

    </div>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
  export default {
    name: 'ms-media-check',
    data() {
      return {
        checkOptions: [],
        checkModel: 1,
        hasLabel: this.config.notLabel ? false : true,
        hasBorder: this.config.hasBorder ? true : false,
        show: false
      }
    },
    computed: {
      ...mapGetters(["info"])
    },
    props: {
      config: {
        type: Object,
        default: () => {
          return {}
        }
      },
      model: {
        type: Number,
        default: 1
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      model: function(val) {
        this.checkModel = val
      },
      checkModel: function(val) {
        this.$emit('update:model', val)
        this.$emit('changeProjectId', val)
      }
    },
    created() {
      if (this.model) {
        this.checkModel = this.model
      }
      this.init()
    },
    methods: {
      init() {
        let data = {
          id: this.info.projectId,
          type: 1
        }
        this.api.getChildProjectList(data).then(response => {
          let arr  = []
          if (response.status === 200) {
            let data = response.data
            for (let i = 0; i< data.length; i++) {
              arr.push({
                projectId: data[i].id,
                projectName: data[i].name
              })
            }
            this.checkOptions = arr
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .arrows {
    cursor: pointer;
    margin-bottom: 16px;
    text-align: center;
    i {
      font-size: 14px;
    }
  }
  .category-check {
    &-label {
      width: 70px;
      font-size: 12px;
      // color: #4B74FF;
      color: #409EFF;
      text-align: right;
      margin-right: 20px;
      position: relative;
      top: 3px;
      font-weight: 600;
      .error-label {
        color: #F56C6C;
      }
    }
    &-group {
      width: 100%;
      text-align: left;
    }
    .category-btn {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 6px;
      position:relative;
      input[type="checkbox"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      label{
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color:#F0F2F5;
        font-size: 11px;
        font-weight: 400;
        display:inline-block;
        cursor: pointer;
        margin-bottom: 8px;
      }
      :v-deep .is-checked {
        background-color: #409EFF;
      }
      :v-deep .el-radio__input {
        display: none;
      }
      :v-deep .el-radio__label {
        color: #2c3e50;
        padding-left: 0;
      }
      :v-deep .is-checked .el-radio__label {
          color: #fff;
      }
    }
  }
</style>
