<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 size="mini"
                 separator="-"
                 :show-all-levels="true"
                 :clearable="true"
                 :props="{children: 'meetingCategoryList', label: 'categoryName',value: 'categoryId', emitPath: false}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-meeting-cascader",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val.meetingCategoryId;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        this.api.accordingToSection().then( response => {
          if(response.status === 200) {
            this.optionSearch = response.data.map(v => {
                return {
                    categoryId: v.id,
                    categoryName: v.name,
                    meetingCategoryList: v.meetingCategoryList
                }
            })
          }
          this.loading = false;
        })
      }, 
      change() {
        let nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
        let params = {}
        if (nodeContent && nodeContent.length > 0) {
            params = {
                meetingId: nodeContent[0].parent.value,
                meetingName: nodeContent[0].parent.label,
                meetingCategoryId: nodeContent[0].value,
                meetingCategoryName: nodeContent[0].label
            }
        } else {
            params = {
                meetingId: 0,
                meetingName: '',
                meetingCategoryId: 0,
                meetingCategoryName: ''
            }
        }
        
        this.$emit('update:model', params)
      }
    }
  }
</script>
