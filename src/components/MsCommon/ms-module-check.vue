<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-checkbox-group v-model="checkModel" @change="change">
      <el-checkbox v-for="(item,index) in optionsSearch" 
                   :key="index"
                   :label="item.moduleCode">{{item.moduleName}}</el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
  export default {
    name: 'ms-module-check',
    data() {
      return {
        checkModel: [],
        optionsSearch: [],
        
      }
    },
    props: [
      "label", // => 输入框label
      "model"
    ],
    watch: {
      model: function(val) {
        if (val) {
          this.checkModel = val 
        }
      }
    },
    created() {
        this.api.getProjectCategoryModuleList().then(response => {
            this.loading = false
            if (response.status === 200) {
              this.optionsSearch = [...response.data]
            }
        })
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
