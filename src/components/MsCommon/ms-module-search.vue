<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               :style="{width: width || '100%'}"
               ref="servicelineRef"
               @change="change"
               :disabled="disabled"
               :multiple="multiple">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.moduleName"
                   :value="item.moduleCode">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-module-search",
    props: [
      "model",
      "label",
      "width",
      "modelName",
      "disabled",
      "multiple"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val
      }
    },
    created() {
        this.result = this.model
        this.loading = true;
        this.api.getProjectCategoryModuleList().then(response => {
            this.loading = false
            if (response.status === 200) {
              this.optionSearch = [...response.data]
            }
        })
    },
    methods: {
      change(val) {
        if (this.multiple) {
          this.$emit('update:model', val)
        } else {
          this.$emit('update:model', val)
          this.$nextTick(() => {
            this.$emit('update:modelName', this.$refs.servicelineRef.selectedLabel)
          })
        }
      }
    }
  }
</script>
