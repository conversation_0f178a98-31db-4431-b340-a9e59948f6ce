<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               :style="{width: width || '100%'}"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.label"
                   :value="item.timeVal">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  const nowYear = new Date().getFullYear();
  export default {
    name: "ms-month-search",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        result: null,
        optionSearch: [
            {label: '一月', timeVal: `${nowYear}-01-01 00:00:00`},
            {label: '二月', timeVal: `${nowYear}-02-01 00:00:00`},
            {label: '三月', timeVal: `${nowYear}-03-01 00:00:00`},
            {label: '四月', timeVal: `${nowYear}-04-01 00:00:00`},
            {label: '五月', timeVal: `${nowYear}-05-01 00:00:00`},
            {label: '六月', timeVal: `${nowYear}-06-01 00:00:00`},
            {label: '七月', timeVal: `${nowYear}-07-01 00:00:00`},
            {label: '八月', timeVal: `${nowYear}-08-01 00:00:00`},
            {label: '九月', timeVal: `${nowYear}-09-01 00:00:00`},
            {label: '十月', timeVal: `${nowYear}-10-01 00:00:00`},
            {label: '十一月', timeVal: `${nowYear}-11-01 00:00:00`},
            {label: '十二月', timeVal: `${nowYear}-12-01 00:00:00`}
        ],
      }
    },
    watch: {
      model: function(val) {
        this.result = val
      }
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
