<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 size="mini"
                 separator="-"
                 :show-all-levels="false"
                 :props="{children: 'childMenu', label: 'name',value: 'id', emitPath: false}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-professional-cascader",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        this.api.getTitleList().then( response => {
          if(response.status === 200) {
            this.optionSearch = response.data || []
          }
          this.loading = false;
        })
      }, 
      change(val) {
        let nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
        let userParams = {
          operation: 'professional',
          model: {
            id: nodeContent.length > 0 ? nodeContent[0].value : 0,
            name: nodeContent.length > 0 ? nodeContent[0].label : ''
          }
        }
        this.$emit('update:model', val)
        this.$emit('bindData', userParams)
      }
    }
  }
</script>
