<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 size="mini"
                 :style="{width: width || '100%'}"
                 :props="{children: 'childMenu', label: 'name',value: 'id'}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-professional-cascader",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        this.api.getTitleList().then( response => {
          if(response.status === 200) {
            this.optionSearch = response.data || []
          }
          this.loading = false;
        })
      }, 
      change() {
        let nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
        let pathNodes = nodeContent && nodeContent.length > 0 && nodeContent[0] ? nodeContent[0].pathNodes : []
        let params = pathNodes.map(v => {
            return {
                id: v.value,
                name: v.label,
                en: v.data.comment
            }
        })
        this.$emit('bindData', {
            operation: 'professional',
            model: params
        })
      }
    }
  }
</script>
