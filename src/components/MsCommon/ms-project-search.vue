<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select
      size="mini"
      v-model="result"
      :style="{width: width || '100%'}"
      :loading="loading"
      @change="change"
      clearable
      :disabled="disabled"
    >
      <el-option
        v-for="(item,index) in optionSearch"
        :key="index"
        :label="item.name"
        :value="item.projectId"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "ms-project-search",
  props: ["model", "label", "width", "filterCode", "disabled"],
  data() {
    return {
      loading: false,
      result: null,
      optionSearch: []
    };
  },
  computed: {
    ...mapGetters(["info", "projectId"])
  },
  watch: {
    model: function(val) {
      this.result = val;
    }
  },
  created() {
    this.result = this.model;
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      let id = this.$route.query.id
      let params = {
        projectId: id  ? id : null
      };
      this.optionSearch = []
      this.api.getProjectDicList(params).then(response => {
        this.loading = false;
        if (response.status === 200) {
          if (this.filterCode) {
            this.optionSearch = response.data.filter(v => {return v.projectCode !== this.filterCode})
          } else {
            this.optionSearch = response.data;
          }
          
        }
      });
    },
    change(val) {
      this.$emit("update:model", val);
    }
  }
};
</script>
