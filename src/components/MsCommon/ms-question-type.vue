<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               :style="{width: width || '100%'}"
               :placeholder="placeholder"
               :loading="loading"
               @change="change"
               ref="statusRef">
        <el-option
            v-for="item in optionSearch"
            :key="item.value"
            :label="item.label"
            :value="item.value">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-question-type",
    props: [
      "model",
      "label",
      "width",
      "modelName",
      "placeholder"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [{
          label: '单选题',
          value: 1
        },
        {
          label: '多选题',
          value: 2
        },
        {
          label: '主观题',
          value: 3
        }]
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
    },
    methods: {
      change(val) {
        this.$nextTick(() => {
          this.$emit('update:model', val)
          this.$emit('update:modelName', this.$refs.statusRef.selectedLabel )
        })
      }
    }
  }
</script>
