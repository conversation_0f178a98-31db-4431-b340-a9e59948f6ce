<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               :multiple="true"
               @change="change"
               value-key="roleId">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.roleName"
                   :value="item">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-role-search",
    props: [
      "model",
      "label",
      "width",
      "projectId"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          projectId: this.$route.query.id
        }
        this.api.getMedsciRoleDicList(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }, 
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
