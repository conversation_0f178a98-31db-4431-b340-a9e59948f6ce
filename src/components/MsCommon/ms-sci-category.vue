<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               :loading="loading"
               @change="change">
        <el-option-group
            v-for="group in optionSearch"
            :key="group.title"
            :label="group.title">
            <el-option
                v-for="item in group.childrenList"
                :key="item.value"
                :label="item.title"
                :value="item.value">
            </el-option>
        </el-option-group>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-sci-category",
    props: [
      "model",
      "label",
      "width"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true
        this.api.getClassicSentencesSubject({}).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data
          }
        })
      },
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
