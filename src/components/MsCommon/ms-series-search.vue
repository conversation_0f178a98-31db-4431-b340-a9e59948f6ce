<template>
    <div :class="{'input-label': label}">
        <label v-show="label">{{label}}</label>
        <el-select size="mini"
                v-model="result" 
                clearable 
                filterable 
                :style="{width: width || '100%'}"
                remote
                :remote-method="getOptionData"
                :loading="loading"
                @change="change"
                ref="seriesRef">
            <el-option v-for="(item,index) in optionSearch"
                    :key="index"
                    :label="item.title"
                    :value="item.id">
            </el-option>
        </el-select>
    </div>
</template>

<script>
    export default {
        name: "ms-series-search",
        props: [
            "model",
            "modelName",
            "label",
            "width"
        ],
        data() {
            return {
                loading: false,
                result: null,
                optionSearch: []
            }
        },
        watch: {
            model: function(val) {
                if (!this.optionSearch || this.optionSearch.length <= 0) {
                    this.optionSearch.push({
                        id: val,
                        title: this.modelName
                    })
                }
                this.result = val;
            }
        },
        methods: {
            // filterMethod(val) {
            //     if(val && val.length >= 2) {
            //         this.getOptionData(val)
            //     }
            // },
            change(val) {
                this.$nextTick(() => {
                    this.$emit('update:model', val)
                    this.$emit('update:modelName', this.$refs.seriesRef.selectedLabel )
                })
            },
            getOptionData(val) {
                if (val && val.length >= 2) {
                    this.loading = true;
                    // 接口获取数据
                    let params = {
                        pageIndex: 1,
                        pageSize: 30,
                        title : val ? val : '',
                        moduleType: 0
                    }
                    this.api.getVideosPage(params).then(response => {
                        this.loading = false
                        if (response.status === 200) {
                            this.optionSearch = response.data
                        }
                    })
                }
            },
        }
    }
</script>
