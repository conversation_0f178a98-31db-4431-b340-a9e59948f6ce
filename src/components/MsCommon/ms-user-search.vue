<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               ref="userRef"
               :style="{width: width || '100%'}"
               remote
               :remote-method="getOptionData"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.userName"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  import { Debounce } from '@/utils/debounce'
  export default {
    name: "ms-user-search",
    props: [
      "model",
      "label",
      "width",
      "modelName"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
        filterVal: '',
        initOption: [],
        debounceApi: null
      }
    },
    watch: {
      model: function(val) {
        this.result = val || 0
        if(val && this.optionSearch.length === 0) {
            this.optionSearch.push({
                id: val,
                userName: this.modelName
            })
            this.initOption.push({
                id: val,
                userName: this.modelName
            })
        }
      }
    },
    created() {
      this.debounceApi = Debounce(this.getUserData, 600)
    },
    methods: {
        getOptionData(val) {
            if (val && val.length > 1) {
                this.filterVal = val;
                this.debounceApi()
            } 
        },
        getUserData () {
          this.loading = true;
          // 接口获取数据
          let params = {
              userName : this.filterVal,
              identityFlag: 0,
              status: 1,
              pageIndex: 1,
              pageSize: 30
          }
          this.api.getUserList(params).then(response => {
              this.loading = false
              let data = response.data || []
              let arr = [
                  ...data,
                  ...this.initOption
              ]
              this.optionSearch = arr
          })
        },
        change(val) {
            this.$nextTick(() => {
            this.$emit('update:model', val)
            this.$emit('update:modelName', this.$refs.userRef.selectedLabel )
            })
            
        }
    }
  }
</script>
