<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               remote
               :remote-method="filterMethod"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.title"
                   :value="item.id">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-video-search",
    props: [
      "model",
      "label",
      "width",
      "modelName"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    created() {
      this.result = this.model
    },
    methods: {
      filterMethod(val) {
        this.getOptionData(val)
      },
      change(val) {
        let nodeData = {}
        this.$emit('update:modelName', val)
        this.optionSearch.some((v) => {
          if (v.id === val) {
            nodeData = v
            return true
          } 
        })
        this.$emit('getNodeData', nodeData)
      },
      getOptionData(val) {
        this.loading = true;
        // 接口获取数据
        let params = {
          pageIndex: 1,
          pageSize: 30,
          title : val ? val : '',
          status: 1
        }
        this.api.getVideoMaterialList(params).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data
          }
        })
      },
    }
  }
</script>
