<template>
  <div class="yxd_notice-create">
    <div class="container">
      <div class="col">
        <span class="tl"><span style="color:red">*</span> 公告标题</span>
        <el-input
          v-model="formData.title"
          placeholder="请输入100字以内的标题"
        ></el-input>
      </div>
      <div class="col">
        <span class="tl"><span style="color:red">*</span>公告正文</span>
        <ms-editor class="editor" v-model="formData.content"></ms-editor>
      </div>
      <div class="col">
        <span class="tl">定时上线</span>
        <el-date-picker
          v-model="formData.publishedTime"
          type="datetime"
          placeholder="选择日期"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </div>
      <div class="footer">
        <el-button @click="goBack">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import msEditor from "@/components/ms-editor/index";
import { insertSinglePage, updateSinglePage } from "@/request/service.js";
import { escapeMap, unescapeMap } from "@/utils/constant.js";
export default {
  name: "notice-create",
  data() {
    return {
      content: "",
      formData: {},
    };
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: "",
    },
  },
  components: {
    msEditor,
  },
  mounted() {
    if (this.info) {
      this.formData = this.info;
      this.formData.content = this.unexcapeHtml(this.formData.content);
    }
  },
  methods: {
    goBack() {
      this.$emit("close");
    },
    save() {
      this.formData.content = this.excapeHtml(this.formData.content);
      let params = {
        content: this.formData.content,
        projectId: this.$store.state.projectInfo.id,
        publishedTime: this.formData.publishedTime
          ? this.formData.publishedTime
          : "",
        title: this.formData.title,
        type: 2,
      };
      if (this.type == "add") {
        insertSinglePage(params).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      } else {
        params.id = this.formData.id;
        updateSinglePage(params).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      }
    },
    excapeHtml(content) {
      var escaper = function(match) {
        return escapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(escapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
    unexcapeHtml(content) {
      var escaper = function(match) {
        return unescapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(unescapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_notice-create {
  .col {
    display: flex;
    align-items: center;
    margin-right: 20px;
    .tl {
      width: 70px;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      white-space: nowrap;
      text-align: right;
    }
    .editor {
      margin-top: 30px;
      margin-bottom: 30px;
    }
  }
  .footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
