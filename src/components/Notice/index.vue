<template>
  <div class="yxd_notice_index">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入公告标题"></el-input>
          </div>
          <div class="col">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="创建开始时间"
              end-placeholder="创建结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button
          v-if="btnList.add_notice_btn"
          type="primary"
          @click="create"
          icon="el-icon-plus"
          >创建公告</el-button
        >
      </div>
      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column
            prop="title"
            label="公告标题"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="clickNumber"
            label="浏览次数"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="clickNumber"
            label="浏览人数"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            sortable
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="updatedTime"
            label="最后一次更新时间"
            sortable
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            width="120"
            align="center"
          >
            <template #default="scope">
              <span
                :class="{ start: scope.row.status == '已上线' }"
                class="default"
              ></span>
              <span>{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="300"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.start_btn"
                size="mini"
                @click="handleDown(scope.$index, scope.row)"
                >{{ scope.row.status == "已上线" ? "下线" : "上线" }}</el-button
              >
              <el-button
                v-if="btnList.edit_btn"
                size="mini"
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="btnList.remove_btn"
                size="mini"
                plain
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span
          >是否{{ lineText == "上线提示" ? "上线" : "下线" }}
          <el-tag>{{ currentChose.title }}</el-tag></span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="lineSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <span
          >是否删除 <el-tag>{{ currentChose.title }}</el-tag></span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="deleteSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { getPageForSinglePage, dealSinglePage } from "@/request/service.js";
export default {
  name: "notice-index",
  mixins: [initModule, initScroll],
  data() {
    return {
      title: "",
      time: "",
      dialogVisible: false,
      deleteVisible: false,
      lineText: "上线提示",
      lineValue: "下线",
      currentChose: {},
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let createStartTime = "";
      let createEndTime = "";
      if (this.time && this.time.length > 0) {
        createStartTime = this.time[0];
        createEndTime = this.time[1];
      }
      let params = {
        createEndTime: createEndTime,
        createStartTime: createStartTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
        title: this.title,
        type: 2,
      };
      getPageForSinglePage(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.status == 1) {
              val.status = "已上线";
            } else {
              val.status = "已下线";
            }
          });
          this.total = res.totalSize;
        }
      });
    },
    create() {
      this.$emit("create", {});
      this.$emit("types", "add");
    },
    reset() {
      this.title = "";
      this.time = "";
    },
    handleDown(index, row) {
      if (row.status == "待上线") {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleEdit(index, row) {
      this.$emit("create", row);
      this.$emit("types", "edit");
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    lineSave() {
      let params = {
        id: this.currentChose.id,
        projectId: this.$store.state.projectInfo.id,
        type: this.lineText == "上线提示" ? 1 : 2,
      };
      dealSinglePage(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    deleteSave() {
      let params = {
        id: this.currentChose.id,
        projectId: this.$store.state.projectInfo.id,
        type: 3,
      };
      dealSinglePage(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_notice_index {
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

}
</style>
