<template>
  <el-tabs v-model="editableTabsValue" type="card">
    <el-tab-pane
      :key="item.templateId"
      v-for="item in editableTabs"
      :label="item.templateName"
      :name="item.id"
    >
      <div class="export">
        <el-button @click="derive">导出病例</el-button>
      </div>
      <div class="table">
        <el-table border :data="tableData" tooltip-effect="dark">
          <el-table-column prop="id" label="病例ID" width="100" align="center">
          </el-table-column>

          <el-table-column v-if="question1" :label="question1">
            <template #default="props">
              <div>{{ props.row.dynamicColumnData[0].headerValue }}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="question2" :label="question2">
            <template v-slot="props">
              <div>{{ props.row.dynamicColumnData[1].headerValue }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            align="center"
            width="200"
          >
          </el-table-column>
          <el-table-column
            prop="updateAt"
            label="更新时间"
            align="center"
            width="200"
          >
          </el-table-column>
          <el-table-column
            prop="attachment"
            label="附件"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button type="primary" size="mini" @click="preview(scope.row)"
                >点击预览</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="审核状态"
            width="100"
            align="center"
          >
            <template #default="scope">
              <span :class="{ start: scope.row.status }" class="default"></span>
              <span>{{ scope.row.status == 1 ? "审核通过" : "待审核" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="200"
          >
            <template #default="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleDetail(scope.$index, scope.row)"
                >查看详情</el-button
              >
              <el-button
                v-if="scope.row.status != 2"
                plain
                size="mini"
                type="danger"
                @click="handleCheck(scope.$index, scope.row)"
                >{{ scope.row.status ? "去审" : "审核" }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-tab-pane>
  </el-tabs>
  <el-dialog title="附件预览" v-model="attachVisible" width="70%">
    <div class="attach">
      <div v-for="(item, index) in demoTable" class="col" :key="index">
        <div
          v-if="item.type == 'img'"
          style="display: flex; justify-content: center"
        >
          <el-image style="width: 400px; object-fit: contain" :src="item.value">
          </el-image>
        </div>
        <div v-else>
          <a :href="item.value"></a>
        </div>
      </div>
    </div>
  </el-dialog>
  <el-dialog title="病例审核" v-model="dialogVisible" width="33%">
    <span>{{ lineText }} </span>
    <el-input
      v-if="currentChose.status == 1"
      style="margin-top: 30px"
      type="textarea"
      :rows="4"
      :maxlength="100"
      placeholder="请输入审核不通过原因，＜100字；选填"
      v-model="remark"
    >
    </el-input>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="lineSave" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <div v-if="table">
    <el-drawer title="查看详情" v-model="table" direction="rtl" size="80%">
      <div class="detail_box">
        <!-- <div v-for="(item, index) in demoTable" :key="index" class="col">
        <span>{{ item.headerLabel }}</span>
        <el-input v-model="item.headerValue" disabled></el-input>
      </div> -->
        <div id="subapp"></div>
        <div class="btn" v-if="currentChose.status != 2">
          <el-button @click="check('no')">审核不通过</el-button>
          <el-button @click="check('ok')" type="primary">审核通过</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  caseList,
  pageListCase,
  excelExport,
  updateCaseById,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import { loadMicroApp } from "qiankun";
export default {
  name: "case1",
  data() {
    return {
      editableTabsValue: "",
      editableTabs: [],
      table: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      currentChose: {},
      dialogVisible: false,
      lineText: "",
      remark: "",
      demoTable: [],
      tableData: [],
      currentId: "",
      tableInfo: {},
      dynamicColumnData: [],
      question1: "",
      question2: "",
      attachVisible: false,
      backupData: [],
      microApp: null,
      firstCome: false,
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    currentCss() {
      let val;
      this.editableTabs.forEach((d) => {
        if (d.id == this.currentId) {
          val = d.cssContent;
        }
      });
      return val;
    },
  },
  watch: {
    editableTabsValue(val) {
      this.currentId = val;
      if (this.firstCome) {
        this.initSu();
      }
    },
  },
  methods: {
    init() {
      caseList().then((res) => {
        this.currentId = res.data[0].id;
        this.editableTabs = res.data;
        this.editableTabsValue = res.data[0].id;
        this.initSu();
      });
    },
    initSu() {
      this.tableData = [];
      let params = {
        currentPageNo: this.currentPage,
        id: this.currentId,
        pageSize: this.pageSize,
        userId: this.$store.state.backInfo.userInfo.userId,
        userName: this.$store.state.backInfo.userInfo.userName,
      };
      pageListCase(params).then((res) => {
        this.firstCome = true;
        this.total = res.data.total;
        this.backupData = JSON.parse(JSON.stringify(res.data.content));
        let filterData = res.data.content[0].dynamicColumnData.filter((d) => {
          return d.headerType == "input";
        });
        res.data.content.forEach((t) => {
          if (res.data.content[0].dynamicColumnData.length > 0) {
            t.dynamicColumnData.forEach((b, index) => {
              if (b.headerType != "input") {
                t.dynamicColumnData.splice(index, 1);
              }
            });
          }
        });
        if (filterData.length > 1) {
          this.question1 = res.data.content[0].dynamicColumnData[0].headerLabel;
          this.question2 = res.data.content[0].dynamicColumnData[1].headerLabel;
        }
        if (filterData.length == 1) {
          this.question1 = res.data.content[0].dynamicColumnData[0].headerLabel;
          this.question2 = "";
        }
        this.tableData = res.data.content.length ? res.data.content : [];
      });
    },
    derive() {
      let params = {
        id: this.editableTabsValue,
        userId: this.$store.state.backInfo.userInfo.userId,
        userName: this.$store.state.backInfo.userInfo.userName,
      };
      excelExport(params).then((res) => {
        if (res.status == 200) {
          window.location.href = res.data;
        }
      });
    },
    tabClick(e) {
    },
    lineSave() {
      let params = {
        id: this.currentChose.id,
        status: this.currentChose.status == 1 ? 0 : 1,
        remark: this.remark,
      };
      updateCaseById(params).then((res) => {
        this.dialogVisible = false;
        this.remark = "";
        this.initSu();
      });
    },
    isJson(obj) {
      var isjson =
        typeof obj == "object" &&
        Object.prototype.toString.call(obj).toLowerCase() ==
          "[object object]" &&
        !obj.length;
      return isjson;
    },
    handleDetail(index, val) {
      this.currentChose = val;
      this.demoTable = val.dynamicColumnData.map((d) => {
        return {
          ...d,
          headerArr: this.isJson(d.headerValue)
            ? JSON.parse(d.headerValue)
            : "",
        };
      });
      this.table = false;
      if (this.microApp) {
        this.microApp.unmount();
      }
      this.tableInfo = {
        userId: this.$store.state.backInfo.userInfo.userId,
        userName: this.$store.state.backInfo.userInfo.userName,
        encodeId: this.currentCss,
        account: `medsci_postscase${this.currentChose.id}`,
        projectId: this.$store.state.projectInfo.id,
        objectType: 66,
        objectId: this.currentChose.id,
      };
      if (this.tableInfo.encodeId) {
        this.$nextTick(() => {
          let url =
            process.env.NODE_ENV == "production"
              ? `//${window.location.host}/msform/`
              : "http://localhost:7101/";
          this.microApp = loadMicroApp({
            name: "popup",
            entry: url,
            container: "#subapp",
            props: {
              tableInfo: this.tableInfo,
            },
          });
          this.table = true;
        });
      }
    },
    handleCheck(index, val) {
      if (val.status) {
        this.lineText = "确认该病例审核不通过吗？";
      } else {
        this.lineText = "确定将当前病例的状态调整为审核通过吗？";
      }
      this.dialogVisible = true;
      this.currentChose = val;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    check(type) {
      this.table = false;
      if (type == "ok") {
        let params = {
          id: this.currentChose.id,
          status: 1,
          remark: this.remark,
        };
        updateCaseById(params).then((res) => {
          ElMessage.success("提交成功！");
          this.dialogVisible = false;
          this.remark = "";
          this.initSu();
        });
      } else {
        let params = {
          id: this.currentChose.id,
          status: 0,
          remark: this.remark,
        };
        updateCaseById(params).then((res) => {
          this.lineText = "确定将当前病例的状态调整为审核通过吗？";
          this.dialogVisible = true;
          this.remark = "";
        });
      }
    },
    isImg(val) {
      //判断是否是图片 - strFilter必须是小写列举
      var strFilter = ".jpeg|.gif|.jpg|.png|.bmp|.pic|";
      if (val.indexOf(".") > -1) {
        var p = val.lastIndexOf(".");
        //alert(p);
        //alert(this.length);
        var strPostfix = val.substring(p, this.length) + "|";
        strPostfix = strPostfix.toLowerCase();
        //alert(strPostfix);
        if (strFilter.indexOf(strPostfix) > -1) {
          //alert("True");
          return true;
        }
      }
      //alert('False');
      return false;
    },
    preview(val) {
      let curArr = [];
      this.backupData.forEach((d) => {
        if (val.id == d.id) {
          curArr = d;
        }
      });
      let arr = [];
      curArr.dynamicColumnData.forEach((a) => {
        if (new RegExp("http").test(a.headerValue)) {
          arr.push(1);
        }
      });
      if (arr.length == 0) {
        ElMessage.warning("暂无附件");
      } else {
        this.attachVisible = true;
      }
      this.demoTable = [];
      curArr.dynamicColumnData.forEach((d) => {
        if (new RegExp("http").test(d.headerValue)) {
          this.demoTable = JSON.parse(d.headerValue).map((t) => {
            return {
              ...t,
              type: this.isImg(t.value) ? "img" : "file",
            };
          });
        } else {
          this.demoTable.push("");
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
#subapp {
  pointer-events: none;
}
.export {
  display: flex;
  justify-content: flex-end;
}
.table {
  cursor: pointer;
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }
}
.detail_box {
  margin-top: -80px;
  .col {
    width: 70%;
    margin: 0 auto;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    span {
      min-width: 150px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .btn {
    position: absolute;
    bottom: 50px;
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
