<template>
  <div>
    <div class="header">
      <div class="left">
        <div class="col">
          <el-input v-model="title" placeholder="请输入病例标题"></el-input>
        </div>
        <div class="col">
          <el-input v-model="name" placeholder="请输入创建人"></el-input>
        </div>
        <div class="col">
          <span class="tl"></span>
          <el-date-picker
            v-model="lineTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="创建开始时间"
            end-placeholder="创建结束时间"
            :default-time="defaultTime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </div>
        <!-- 请选择用户类型 -->
        <div class="col">
          <el-select
            v-model="accountTypeValue"
            clearable
            class="m-2"
            placeholder="请选择用户类型"
            @change="handleChange"
          >
            <el-option
              v-for="item in accountTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!-- 筛选权限 -->
        <div class="col" v-if="true">
          <el-cascader
            placeholder="请选择权限"
            :options="RoleList"
            ref="cascader"
            collapse-tags
            @change="change"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'title',
              children: 'childRoles',
              multiple: true,
            }"
            clearable
          >
          </el-cascader>
        </div>
        <!-- 筛选分类 -->
        <div class="col" v-if="true">
          <el-cascader
            placeholder="请选择分类"
            :options="ClassifyData"
            ref="Classify"
            collapse-tags
            @change="ClassifyChange"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'tagName',
              children: 'childTags',
              multiple: true,
            }"
            clearable
          >
          </el-cascader>
        </div>
      </div>
      <div class="right">
        <el-button @click="init" icon="el-icon-search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
    </div>
    <div class="operate">
      <div class="left">
        <el-button v-if="btnList.batchSetRole" @click="setPower"
          >批量设置权限</el-button
        >
        <el-button v-if="btnList.batchSetTag" @click="setClass"
          >批量设置分类</el-button
        >
        <el-button v-if="btnList.batch_start_btn" @click="lineBatch('on')"
          >批量上线</el-button
        >
        <el-button v-if="btnList.batch_end_btn" @click="lineBatch('off')"
          >批量下线</el-button
        >
        <el-button
          v-if="btnList.batch_remove_btn"
          @click="deleteClass"
          icon="el-icon-close"
          >批量删除</el-button
        >
        <el-button v-if="btnList.export_case" @click="deriveData('', '', 'all')"
          >全部导出</el-button
        >
        <el-button @click="filterList()" :type="btnStatus ? 'primary' : ''"
          >未设置分类</el-button
        >
      </div>
    </div>
    <div class="table">
      <el-table
        ref="multipleTable"
        border
        :data="tableData"
        @selection-change="handleSelectionChange"
        tooltip-effect="dark"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column prop="id" label="序号" width="100" align="center">
        </el-table-column>
        <el-table-column
          prop="title"
          label="标题"
          min-width="200"
          align="center"
        >
          <template #default="scope">
            <a
              :href="'/squareDetail/' + scope.row.encryptionId"
              target="_blank"
              >{{ scope.row.title }}</a
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="onOffLine"
          label="状态"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            <span
              :class="{ start: scope.row.onOffLine }"
              class="default"
            ></span>
            <span>{{ scope.row.onOffLine == 0 ? "下线" : "上线" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="publishedTime"
          label="更新时间"
          align="center"
          min-width="200"
        >
        </el-table-column>
        <el-table-column prop="num" label="PV/UV/点赞/评论/收藏" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              v-if="btnList.start_btn"
              plain
              size="mini"
              @click="handleLine(scope.$index, scope.row)"
              >{{ scope.row.onOffLine == 0 ? "上线" : "下线" }}</el-button
            >
            <el-button
              v-if="btnList.remove_btn"
              size="mini"
              type="danger"
              plain
              @click="handleDelete(scope.$index, scope.row)"
              :disabled="scope.row.onOffLine === 1"
              >删除</el-button
            >
            <el-button
              v-if="btnList.export_case"
              size="mini"
              @click="deriveData(scope.$index, scope.row)"
            >
              导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
      <span
        >是否{{ lineText == "上线提示" ? "上线" : "下线" }}
        <el-tag>{{ currentChose.title }}</el-tag>
      </span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="lineSave('one')" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog :title="lineAllText" v-model="onlineVisible" width="70%">
      <h3>确定{{ currentChose.status == 1 ? "下线" : "上线" }}这些内容吗？</h3>
      <el-tag
        class="delete-box"
        v-for="(item, index) in choseData"
        :key="index"
      >
        {{ item.title }}
      </el-tag>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onlineVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="lineSave('more')" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 批量删除 -->
    <el-dialog title="批量删除" v-model="settingVisible" width="70%">
      <h3>确定删除这些内容吗？</h3>
      <el-tag
        class="delete-box"
        v-for="(item, index) in offLineData"
        :key="index"
      >
        {{ item.title }}
      </el-tag>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="settingVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="saveDeleteEdit" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 删除单条 -->
    <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
      <span
        >是否删除 <el-tag>{{ currentChose.title }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="deleteSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 批量设置权限弹框 -->
    <control-dialog
      ref="controlRef"
      :choseData="choseData"
      :RoleList="RoleList"
    ></control-dialog>
    <!-- 批量设置分类弹框 -->
    <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
  </div>
</template>
<script>
import {
  getMedsciCaseAdminPage,
  caseOnOffLine,
  getRoleList,
  setDelePower,
  query,
  listTag,
  batchLineModule,
  edaBatchEdit,
} from "@/request/service.js";
import { exportUserCase, exportsAll } from "@/request/download.js";
import { ElMessage } from "element-plus";
import initModule from "@/mixins/initModule.js";
import filterList from "@/mixins/filterList.js";
import initScroll from "@/mixins/initScroll.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import controlDialog from "../controlDialog.vue";
import classifyDialog from "../classifyDialog.vue";
export default {
  name: "case2",
  mixins: [initModule, initScroll, initAccountTypeList, filterList],
  components: {
    controlDialog,
    classifyDialog,
  },
  data() {
    return {
      lineAllText: "",
      choseData: [],
      lineText: "上线提示",
      lineValue: "下线",
      currentChose: {},
      dialogVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      settingVisible: false,
      deleteVisible: false,
      title: "",
      name: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      lineTime: "",
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
      selectvalue: [],
      RoleList: [],
      ClassifyData: [],
      // powerVisible: false,
      radio: 1,
      checkList: [],
      ClassifyValue: [],
      offLineData: {},
      accountTypeValue: "",
      refesh:false
    };
  },
  mounted() {
    this.init();
    this.getClassifyData();
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val) {
      if (val === 0 || val === 1) {
        this.getPowerData(val);
      } else if (val === "") {
        this.RoleList = [];
      }
    },
    // 全部导出
    exportsAll() {
      exportsAll(this.lineTime[0], this.lineTime[1]).then((res) => {
        // if (res.size > 57) {
        //   let name = row.title+`数据详情.xlsx`
        //   let link = document.createElement("a");
        //   link.href = window.URL.createObjectURL(res);
        //   link.download = name;
        //   link.click();
        // } else {
        //   ElMessage.warning("暂无数据导出");
        // }
      });
    },
    init() {
      if(this.choseData.length>0){
            this.refesh=true
          }
      this.tableData = [];
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        createdEndTime: publishedEndTime === undefined ? "" : publishedEndTime,
        createdName: this.name,
        createdStartTime:
          publishedStartTime === undefined ? "" : publishedStartTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
        roleIds: this.selectvalue,
        classifyIds: this.ClassifyValue,
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      getMedsciCaseAdminPage(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data?.map((d) => {
          return {
            ...d,
            num: `${d.allHits}/${d.userHits}/${d.userLikes}/${d.userComments}/${d.userCollects}`,
          };
        });
        this.filterData = this.tableData;
        this.btnStatus = false;
      });
    },
    // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds: [],
        tagType: "case",
      };
      listTag(params).then((res) => {
        this.ClassifyData = this.removeClassifyChild(res.data);
      });
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    // 清除空的子类
    removeClassifyChild(data) {
      return data.filter((item) => {
        if (item.childTags.length === 0) {
          delete item.childTags;
          return item;
        } else {
          return this.removeClassifyChild(item.childTags);
        }
      });
    },
    // 批量设置权限
    setPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.$refs.controlRef.openDialog("case");
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog("case");
      this.$refs.classifyRef.gitClassList();
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData = this.choseData.filter((item) => item.onOffLine === 0);
      this.settingVisible = true;
    },
    changeCheck() {},
    // savePower() {
    //   let paramsList = [];
    //   this.checkList.forEach(item => {
    //     const value = item.split(':')[0] - 0;
    //     // const label = item.split(':')[1];
    //     paramsList.push(value)
    //   });
    //   let ids = [];
    //   this.choseData.forEach((val) => {
    //     ids.push(val.id);
    //   });
    //   let params = {
    //     "moduleIds": [...ids],
    //     "moduleType": "eda",
    //     "operateType": this.radio,
    //     "roleIds": [...paramsList]
    //   }
    //   setDelePower(params).then((res) => {
    //     if (res.status == 200) {
    //       if (this.radio === 1) {
    //         ElMessage({
    //           message: '设置权限成功',
    //           type: 'success',
    //         })
    //       } else {
    //         ElMessage({
    //           message: '取消权限成功',
    //           type: 'warning',
    //         })
    //       }
    //     }
    //   });
    //   this.powerVisible = false;
    //   this.radio = 1;
    //   this.checkList = [];
    // },
    reset() {
      this.title = "";
      this.name = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {};
      obj.stopPropagation = () => {};
      try {
        this.$refs.cascader.clearValue(obj);
        this.$refs.Classify.clearValue(obj);
      } catch (err) {
        this.$refs.cascader.handleClear(obj);
        this.$refs.Classify.handleClear(obj);
      }
    },
    batch(type) {
    },
    lineBatch(type) {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.lineAllText = type == "on" ? "批量上线" : "批量下线";
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    // 批量上/下线按钮
    lineSave(type) {
      let ids = [];
      let params;
      if (type == "one") {
        ids.push(this.currentChose.id);
        params = {
          moduleType: "case",
          moduleIds: ids,
          operationType: this.currentChose.onOffLine == 1 ? 0 : 1,
        };
      } else {
        this.choseData.forEach((val) => {
          ids.push(val.id);
        });
        params = {
          operationType: this.lineAllText == "批量上线" ? 1 : 0,
          moduleType: "case",
          moduleIds: ids,
        };
      }
      batchLineModule(params).then(() => {
        this.init();
        if (type == "one") {
          this.currentChose.onOffLine == 1
            ? ElMessage.success("下线成功")
            : ElMessage.success("上线成功");
        } else {
          this.lineAllText === "批量上线"
            ? ElMessage.success("批量上线成功")
            : ElMessage.success("批量下线成功");
        }
        this.dialogVisible = false;
        this.onlineVisible = false;
      });
    },
    handleLine(index, row) {
      if (row.onOffLine == 1) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleCheck(index, val) {
    },
    handleSelectionChange(selection) {
          this.choseData=selection
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 数据导出
    deriveData(index, row, types) {
      if (types == "all") {
        // 全部导出
        exportsAll(this.lineTime[0], this.lineTime[1]).then((res) => {
          if (res.size == 51) {
            ElMessage.warning("暂无数据导出");
          } else if (res.size == 63) {
            ElMessage.warning("上线时间不能为空");
          } else {
            let name = `病例数据详情.xlsx`;
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(res);
            link.download = name;
            link.click();
          }
        });
      } else {
        exportUserCase(row.id).then((res) => {
          if (res.size > 57) {
            let name = row.title + `数据详情.xlsx`;
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(res);
            link.download = name;
            link.click();
          } else {
            ElMessage.warning("暂无数据导出");
          }
        });
      }
    },
    // 批量删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        objectType: "case",
        type: 1,
        ids: ids,
      };
      edaBatchEdit(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
    // 删除单条
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        objectType: "case",
        type: 1,
        ids: ids,
      };
      edaBatchEdit(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.init();
        }
      });
    },
    //  点击权限多选框选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.selectvalue = [];
      nodesObj.forEach((item) => this.selectvalue.push(item.data.id));
    },
    //  点击分类多选框选项
    ClassifyChange() {
      let nodesObj = this.$refs["Classify"].getCheckedNodes();
      this.ClassifyValue = [];
      nodesObj.forEach((item) => this.ClassifyValue.push(item.data.id));
    },
  },
};
</script>
<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .tl {
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
    }
  }
  .right {
    display: flex;
    .tl {
      margin-right: 20px;
    }
  }
}
.power-head {
  table {
    .line {
      height: 45px;
    }
    tr {
      padding: 30px 0;
      .title {
        width: 70px;
        // font-size: 20px;
      }
    }
  }
}
.delete-box {
  margin-top: 10px;
  margin-right: 10px;
}
</style>