<template>
  <div class="header">
    <div class="left">
      <div class="col">
        <el-input v-model="title" placeholder="请输入帖子标题"></el-input>
      </div>
      <div class="col">
        <el-input v-model="name" placeholder="请输入创建人"></el-input>
      </div>
      <div class="col">
        <el-date-picker
          v-model="lineTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="创建开始时间"
          end-placeholder="创建结束时间"
          :default-time="defaultTime"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="right">
      <el-button @click="init" icon="el-icon-search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
  </div>
  <div class="operate">
    <el-button @click="lineBatch('yes')">批量审核</el-button>
    <el-button @click="lineBatch('no')">批量去审</el-button>
    <el-button @click="deleteBatch" icon="el-icon-close">批量删除</el-button>
  </div>
  <div class="table">
    <el-table
      ref="multipleTable"
      border
      :data="tableData"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      <el-table-column prop="id" label="ID" width="100" align="center">
      </el-table-column>
      <el-table-column prop="title" label="标题" width="200" align="center">
        <template #default="scope">
          <a :href="scope.row.gotoUrl" target="_blank">{{ scope.row.title }}</a>
        </template>
      </el-table-column>
      <el-table-column
        prop="num"
        label="PV/赞/评/藏"
        width="120"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="120"
        align="center"
        :filters="tagList"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <span>{{ statusFn(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdName"
        label="创建人"
        width="150"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="createdTime"
        label="创建时间"
        align="center"
        width="200"
      >
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleDetail(scope.$index, scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.status != 2"
            size="mini"
            @click="handleDown(scope.$index, scope.row)"
            >{{ scope.row.status == 1 ? "去审" : "审核" }}</el-button
          >

          <el-dropdown v-if="scope.row.status != 2" style="margin-left: 10px">
            <el-button
              size="mini"
              type="danger"
              plain
              @click="handleMore(scope.$index, scope.row)"
              >更多</el-button
            >
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="fix('1', scope.row)"
                  >推荐</el-dropdown-item
                >
                <el-dropdown-item @click="fix('2', scope.row)"
                  >固顶</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
  <el-dialog :title="lineAllText" v-model="onlineVisible" width="70%">
    <h3>确定{{ currentChose.status == 1 ? "下线" : "上线" }}这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveCheck('one')" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <el-dialog title="审核操作" v-model="checkVisible" width="33%">
    <div class="col">
      <span class="tl"><span style="color: red">*</span>操作</span>
      <div>
        <el-radio-group v-model="radio">
          <el-radio label="1" size="large">通过审核</el-radio>
          <el-radio label="2" size="large">去除审核</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="col">
      <span class="tl">&nbsp;说明</span>
      <el-input
        style="margin-top: 30px"
        type="textarea"
        :rows="4"
        :maxlength="100"
        v-model="remark"
      >
      </el-input>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="checkVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveCheck" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
    <h3>确定删除这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveDelete" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <el-drawer title="查看详情" v-model="table" direction="rtl" size="50%">
    <div class="detail_box" style="height: 500px; overflow: auto">
      <div class="coll">
        <span>创建人: {{ currentChose.createdName }}</span>
        <span>创建时间: {{ currentChose.createdTime }}</span>
      </div>
      <div class="coll">
        <span>标题：{{ currentChose.title }}</span>
      </div>
      <div class="row">
        <span>帖子正文：</span>
        <div v-html="currentChose.content"></div>
      </div>
    </div>
    <div v-if="currentChose.status != 2" class="detail_btn">
      <div class="coll">
        <span class="tl">审核：</span>
        <div>
          <el-radio-group v-model="radio">
            <el-radio label="1" size="large">通过审核</el-radio>
            <el-radio label="2" size="large">去除审核</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="coll">
        <span class="tl">说明：</span>
        <el-input
          style="margin-top: 30px"
          type="textarea"
          :rows="4"
          :maxlength="100"
          v-model="currentChose.remark"
        >
        </el-input>
      </div>
      <div class="btn_box">
        <el-button size="mini" @click="table = false">取消</el-button>
        <el-button type="primary" size="mini" @click="saveCheck"
          >确认</el-button
        >
      </div>
    </div>
  </el-drawer>
  <el-dialog :title="obTitle" v-model="obVisible" direction="rtl" size="50%">
    <div class="obDetail">
      <div class="col">
        <span><span style="color: red">*</span> <span>结束时间</span> </span>
        <div class="date">
          <el-date-picker v-model="date" type="date" placeholder="选择日期">
          </el-date-picker>
        </div>
      </div>
      <div class="btn">
        <el-button type="primary" size="mini" @click="saveOb">确定</el-button>
        <el-button size="mini" @click="saveOb">取消{{ obTitle }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  getPostsPage,
  banchDealPosts,
  recommendOrStickyPosts,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
export default {
  name: "case2",
  data() {
    return {
      radio: "1",
      title: "",
      name: "",
      lineTime: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      table: false,
      checkVisible: false,
      deleteVisible: false,
      remark: "",
      choseData: [],
      tableData: [],
      currentChose: {},
      obTitle: "",
      obVisible: false,
      date: "",
      operateType: "one",
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
      tagList: [
        {
          text: "待审核",
          value: 0,
        },
        {
          text: "审核通过",
          value: 1,
        },
        {
          text: "用户删除",
          value: 2,
        },
        {
          text: "未通过",
          value: 3,
        },
      ],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    filterTag(value, row) {
      return row.status == value;
    },
    init() {
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        createdBy: null,
        createdName: this.name,
        createdEndTime: publishedEndTime,
        createdStartTime: publishedStartTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
      };
      getPostsPage(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data.map((d) => {
          return {
            ...d,
            num: `${d.pvCount}/${d.likeCount}/${d.commentCount}/${d.collectCount}`,
            gotoUrl:
              `https://` + window.location.host + `/caseDetail/${d.encodeId}`,
          };
        });
      });
    },
    statusFn(val) {
      let value;
      switch (val) {
        case 0:
          value = "待审核";
          break;
        case 1:
          value = "审核通过";
          break;
        case 2:
          value = "用户删除";
          break;
        case 3:
          value = "未通过";
          break;
      }
      return value;
    },
    reset() {
      this.title = "";
      this.name = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
    },
    batch(type) {
    },
    handleDetail(index, val) {
      this.radio = val.status == 1 ? "2" : "1";
      this.currentChose = val;
      this.table = true;
    },
    handleCheck(index, val) {
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    handleDown(index, val) {
      this.currentChose = val;
      this.operateType = "one";
      this.checkVisible = true;
    },
    handleMore(index, val) {
      this.currentChose = val;
    },
    deleteBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.deleteVisible = true;
    },
    saveCheck() {
      let ids = [];
      if (this.operateType == "one") {
        ids.push(this.currentChose.id);
      } else {
        this.choseData.forEach((val) => {
          ids.push(val.id);
        });
      }
      let params = {
        ids: ids,
        remark: this.table ? this.currentChose.remark : this.remark,
        type: this.radio == 1 ? 1 : 2,
      };
      banchDealPosts(params).then(() => {
        this.remark = "";
        this.checkVisible = false;
        this.table = false;
        this.init();
      });
    },
    lineBatch(type) {
      this.operateType = "more";
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      if (type == "yes") {
        this.radio = "1";
      } else {
        this.radio = "2";
      }
      this.checkVisible = true;
    },
    saveDelete() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
        remark: "",
        type: 3,
      };
      banchDealPosts(params).then(() => {
        this.remark = "";
        this.deleteVisible = false;
        this.init();
      });
    },
    saveOb() {
      if (!this.date) {
        ElMessage.warning("结束时间不可为空！");
        return;
      }
      let params = {
        endTime: dayjs(this.date).format("YYYY-MM-DD HH:mm:ss"),
        id: this.currentChose.id,
        type: this.obTitle == "推荐" ? 1 : 2,
      };
      recommendOrStickyPosts(params).then(() => {
        this.obVisible = false;
        this.init();
      });
    },
    fix(type, obj) {
      if (type == 1) {
        this.date = obj.recommendTime;
      } else {
        this.date = obj.stickyTime;
      }
      this.obTitle = type == 1 ? "推荐" : "固顶";
      this.obVisible = true;
    },
  },
};
</script>

<style scoped lang="scss">
.col {
  display: flex;
  align-items: center;
  margin-right: 20px;
  .tl {
    opacity: 1;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    white-space: nowrap;
  }
}
.header {
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .tl {
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
    }
  }
}

.coll {
  width: 70%;
  margin: 0 auto;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.row {
  width: 70%;
  margin: 0 auto;
  & > span {
    display: block;
    margin-bottom: 30px;
  }
}
.obDetail {
  .col {
    display: flex;
    justify-content: center;
    align-items: center;
    & > span {
      margin-right: 10px;
    }
  }
  .btn {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.detail_box {
  .coll {
    .tl {
      min-width: 50px;
    }
  }
  .row {
    & > span {
      min-width: 50px;
    }
  }
}
.detail_btn {
  .coll {
    justify-content: flex-start;
    .tl {
      min-width: 50px;
    }
  }
  .row {
    & > span {
      min-width: 50px;
    }
  }
}
.btn_box {
  width: 70%;
  margin: 0 auto;
  display: flex;
  justify-content: flex-end;
}
</style>
