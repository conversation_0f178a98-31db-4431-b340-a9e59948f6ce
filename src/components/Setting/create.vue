<template>
  <div class="yxd_setting">
    <div class="container">
      <div class="header">
        <h3>{{ type == "add" ? "添加图标" : "编辑图标" }}</h3>
      </div>
      <div class="col">
        <span class="tl"><span style="color:red">*</span> 图标名称</span>
        <el-input
          v-model="title"
          placeholder="请输入5个字或者15个字母以内的图标名称"
        ></el-input>
      </div>
      <div class="col">
        <span class="tl">跳转地址</span>
         <el-cascader
            style="margin-right:10px;width:180px"
            ref="cascader"
            :options="options"
            :disabled="value>0"
            v-model="cascaderId"
            @change="optionChange"
            :props="{ 
            checkStrictly: true,
            value:'tagId',
            label:'label',
            children:'childRoles'
            }"
            clearable>
          </el-cascader>

        <!-- <el-select
          style="margin-right:10px;width:180px"
          v-model="typeValue"
          @change="optionChange"
          placeholder="请选择"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select> -->
        <el-input
          v-if="showHttp"
          v-model="url"
          :disabled="value>0"
          placeholder="请输入图标跳转链接"
        ></el-input>
      </div>
      <div class="col">
        <span class="tl"></span>
        <div class="warn">
          <p>1.头部地址需包含http或https</p>
          <p>2.填写的地址，域名必须已经通过ICP备案</p>
        </div>
      </div>
      <div class="col">
        <span class="tl">跳转定位</span>
        <el-select v-model="value" class="m-2" clearable :disabled="String(cascaderId).length>0"  @change="changeSelect"  placeholder="请选择" >
          <el-option
            v-for="item in options1"
            :key="item.tagId"
            :label="item.label"
            :value="item.tagId"
          />
        </el-select>
      </div>
      <div class="col">
        <span class="tl"><span style="color:red">*</span> 图标icon</span>
        <div class="upload-box">
          <div class="upload">
            <el-upload
              class="upload-demo"
              action="#"
              :http-request="customRequest"
              :before-upload="beforeAvatarUpload"
              ><img v-if="icon" :src="icon" class="avatar" />
              <el-button icon="el-icon-upload">上传图片</el-button>
            </el-upload>
          </div>
          <span>支持扩展名：.png 图片尺寸：64*64 仅限上传1张</span>
        </div>
      </div>
      <div class="footer">
        <el-button @click="cancel">取消</el-button>
        <el-button @click="save" type="primary">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { addTool, updateTool, uploadAvatar, listTag } from "@/request/service.js";
import { ElMessage } from "element-plus";
import { urlList } from "@/utils/constant.js";
export default {
  name: "setting-create",
  props: {
    type: {
      type: String,
      default: "",
    },
    tableData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    characterCount() {
      const chineseCount = this.title.replace(/[^\u4e00-\u9fa5]/g, '').length; // 汉字数量
      const englishCount = this.title.replace(/[\u4e00-\u9fa5]/g, '').length; // 英文字符数量
      return chineseCount + englishCount;
    },
  },
  watch: {
     title(newValue) {
      if (this.byteLength > this.maxLength) {
        this.title = this.truncateText(newValue);
        this.isExceedLimit = true;
      } else {
        this.isExceedLimit = false;
      }
    },
  },
  data() {
    return {
      maxLength: 15,
      isExceedLimit: false,
      title: "",
      url: "",
      icon: "",
      typeValue: "",
      showHttp: true,
      tagName:'',
      selectvalue:'',
      options: [
        {
          value: "1",
          label: "无跳转",
          tagId: 1,
        },
        // {
        //   value: "/article",
        //   label: "资讯",
        // },
        // {
        //   value: "/course",
        //   label: "课程",
        // },
        // {
        //   value: "/live",
        //   label: "直播",
        // },
        // {
        //   value: "/case",
        //   label: "病例",
        // },
        // {
        //   value: "/survey",
        //   label: "调研",
        // },
        // {
        //   value: "/eda",
        //   label: "医讯达",
        // },
        {
          value: "2",
          label: "网址",
          tagId: 2,
        },
      ],
      options1:[

],
value:"",
      cascaderId:''
    };
  },
   computed: {
    byteLength() {
      return this.getByteLength(this.title);
    },
  },
  mounted() {
    this.init()
    if(this.type == "add"){
      this.typeValue = "1";
    }else if (this.type == "edit") {
      this.title = this.tableData.name;
      this.url = this.tableData.url;
      this.value=this.tableData.directTagId
      this.cascaderId =this.tableData.tagId?this.tableData.tagId:this.tableData.directTagId?true:this.tableData.directTagId
      // this.aaa=this.tableData.title
      if (this.url == "") {
        this.showHttp = false;
        this.typeValue = "1";
        this.disabled = false;
      } else if (!urlList.includes(this.url)) {
        this.showHttp = true;
        this.typeValue = "2";
        this.disabled = false;
      } else {
        this.showHttp = true;
        this.disabled = true;
        this.typeValue = this.url;
      }
      this.icon = this.tableData.pic;
    }
  },
  watch:{
    value(val){
        if(val){
            this.cascaderId=""
            this.url=""
        }else{
            this.value=""
        }
    },
    cascaderId(val){
      if(val){
        this.value=''
      }else{
        this.cascaderId=""
            this.url=""
      }
    }
  },
  methods: {
    // 方法
    changeSelect(){
      if(this.value){
        this.cascaderId=""
        this.url=""
      }
    },
    // 获取字节

 getByteLength(str) {
      // 计算字符串字节长度的方法，与之前提供的示例相同
      let byteLength = 0;
      for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        byteLength += charCode < 128 ? 1 : 3; // 假设使用UTF-8编码，1个字节对应ASCII字符，3个字节对应汉字
      }
      return byteLength;
    },
    truncateText(text) {
      // 截断文本，使其不超过最大字节长度
      let truncatedText = "";
      let byteLength = 0;

      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i);
        const charByteLength = charCode < 128 ? 1 : 3; // 假设使用UTF-8编码，1个字节对应ASCII字符，3个字节对应汉字

        if (byteLength + charByteLength <= this.maxLength) {
          truncatedText += text.charAt(i);
          byteLength += charByteLength;
        } else {
          break;
        }
      }

      return truncatedText;
    },
     init() {
      let params = {
        // pageIndex: 1,
        // pageSize: 100,
        // tagName: this.tagName,
        tagIds:[],
        tagType:'',
        filterContent:true
        // tagType: type?.slice(1),

      };
      listTag(params).then((res) => {
        if (res.status == 200) {
          res.data.forEach(item=>{
           if(item.tagType==='article'){
             this.options.push({
             value: '/news',
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/news',
             label: item.tagName,
             tagId:item.id
            })
           }else if(item.tagType==='course'){
             this.options.push({
             value: '/class',
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/class',
             label: item.tagName,
             tagId:item.id
            })
           }else if(item.tagType==='live_info'){
             this.options.push({
             value: '/live',
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/live',
             label: item.tagName,
             tagId:item.id
            })
           }else if(item.tagType==='case'){
             this.options.push({
             value: '/square',
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/square',
             label: item.tagName,
             tagId:item.id
            })
           }else if(item.tagType==='eda'){
             this.options.push({
             value: '/yxd',
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/yxd',
             label: item.tagName,
             tagId:item.id
            })
           }else{
             this.options.push({
             value: '/'+item.tagType,
             label: item.tagName,
             tagId:item.id
            }) 
            this.options1.push({
               value: '/'+item.tagType,
             label: item.tagName,
             tagId:item.id
            })
           }
          })
          // this.total = res.totalSize;
          // this.typeValue = res.data;
        }
      });
    },
    optionChange(e) {

      if (e == "1") {
        this.disabled = false;
        this.showHttp = false;
        this.typeValue = "1";
        this.url = "";
        this.value=""
      } else if (e == "2") {
        this.url = "";
        this.typeValue = '';
        this.disabled = false;
        this.showHttp = true;
        this.value=""
      }  else {
        this.typeValue = '';
        this.disabled = true;
        this.url = this.options.filter(item=>item.tagId===e?.[0])[0]?.value;
        this.showHttp = true;
        this.value=""
        // if(this.options.some(item=>item.value===e?.[0]&&item.childRoles===undefined)){
        //   this.init(e?.[0])
        // }
      }
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=''
       nodesObj.forEach(item=>this.selectvalue=item.data.tagId)
    },
    goBack() {
      this.$emit("back");
    },
    cancel() {
      this.goBack();
    },
    verificate() {
      if (!this.title) {
        ElMessage.warning("图标名称不可为空！");
        return false;
      } else if (!this.url &&this.cascaderId&& this.cascaderId!=1) {
        ElMessage.warning("跳转链接不可为空！");
        return false;
      } else if (!this.icon) {
        ElMessage.warning("图标icon不可为空！");
        return false;
      } else {
        return true;
      }
    },
    beforeAvatarUpload(file) {
      const isPng = file.type.includes('image');
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isPng) {
        ElMessage.error("上传的图片格式不对!");
        return false;
      }
      if (!isLt2M) {
        ElMessage.error("上传的图片大小不能超过 2MB!");
        return false;
      } else {
        return true;
      }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    customRequest(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.icon = res.data.url;
          }
        });
      });
    },
    save() {
      if (!this.verificate()) {
        return;
      }
      if (this.type == "edit") {
        let params1 = {
          id: this.tableData.id,
          isCanShare: this.tableData.isCanShare,
          isFixed: this.tableData.isFixed,
          isLogin: this.tableData.isLogin,
          name: this.title,
          pic: this.icon ? this.icon : this.tableData.pic,
          projectId: this.$store.state.projectInfo.id,
          recommend: this.tableData.recommend,
          sort: this.tableData.sort,
          url: `${this.url}`,
          tagId:this.selectvalue,
          directTagId:this.value
          // userId:'',
          // username:''
        };
        updateTool(params1).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      } else {
        let params = {
          name: this.title,
          pic: this.icon,
          projectId: this.$store.state.projectInfo.id,
          url: `${this.url}`,
          tagId:this.selectvalue,
          directTagId:this.value
        };
        addTool(params).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_setting {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .avatar {
    width: 60px;
    height: 60px;
    object-fit: contain;
  }
  .col {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
    .tl {
      width: 80px;
      min-width: 80px;
      text-align: right;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      white-space: nowrap;
    }
    .warn {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: red;
      line-height: 22px;
      margin-right: 20px;
      margin-top: -20px;
    }
    .upload-box {
      .upload {
      }
      span {
        display: block;
        margin-top: 20px;
      }
    }
  }
}
</style>
