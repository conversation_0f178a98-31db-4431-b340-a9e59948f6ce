<template>
  <div class="yxd_setting">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <span
          >可以设置首页icon功能区是否展示，同时调整展示顺序。<br />
          可以自定义添加icon，并定义icon信息。</span
        >
        <el-button v-if="btnList.add_icon" @click="addIcon" type="primary"
          >添加图标</el-button
        >
      </div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="sort" align="center" label="序号" width="180">
        </el-table-column>
        <el-table-column
          prop="name"
          align="center"
          label="图标名称"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop="createdTime"
          align="center"
          label="创建时间"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop="updatedTime"
          align="center"
          label="更新时间"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop="status"
          align="center"
          label="状态"
          class-name="special"
        >
          <template #default="scope">
            <span :class="{ start: scope.row.status }" class="default"></span>
            <span>{{ scope.row.status ? "已开启" : "已关闭" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          min-width="200"
        >
          <template #default="scope">
            <el-button
              v-if="btnList.edit_btn"
              size="mini"
              @click="handleEdit(scope.$index, scope.row)"
              >编辑</el-button
            >
            <el-button
              plain
              v-if="btnList.disable_btn"
              :type="scope.row.status ? 'danger' : 'primary'"
              size="mini"
              @click="handleDelete(scope.$index, scope.row)"
              >{{ scope.row.status ? "禁用" : "启用" }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination> -->
      </div>
      <el-dialog title="编辑首页功能" v-model="settingVisible" width="50%">
        <div class="setting-main">
          <div class="box">
            <span>名称</span>
            <el-input v-model="name" :maxlength="4"></el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini"
              >取消</el-button
            >
            <el-button type="primary" @click="saveEdit" size="mini"
              >保存</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import Sortable from "sortablejs";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import {
  batchDealTools,
  getToolPage,
  updateTool,
  setSort,
} from "@/request/service.js";
export default {
  name: "setting-index",
  mixins: [initModule, initScroll],
  components: {
    // Sortable,
  },
  data() {
    return {
      settingVisible: false,
      tableData: [],
      name: "",
      currentChose: {},
      currentPage: 1,
      pageSize: 20,
      total: 0,
    };
  },
  mounted() {
    this.rowDrop();
    this.init();
  },
  methods: {
    init() {
      let params = {
        pageIndex: 1,
        pageSize: 100,
        projectId: this.$store.state.projectInfo.id,
      };
      getToolPage(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data.records;
          this.initScroll();
          this.total = res.data.total;
          this.tableData.forEach((val) => {
            if (val.status == 1) {
              val.status = true;
            } else {
              val.status = false;
            }
          });
        }
      });
    },
    handleEdit(index, row) {
      // 主站推送的图标只可编辑名称 projectId 1为主站推送
      if (row.projectId != 1) {
        this.$emit("go", "edit");
        this.$emit("edit", row);
        return;
      }
      this.name = row.name;
      this.currentChose = row;
      this.settingVisible = true;
    },
    handleDelete(index, row) {
      let ids = [];
      ids.push(row.id);
      let params = {
        ids: ids,
        type: row.status ? 2 : 1,
        userId:row.id,
        username:row.name,
      };
      batchDealTools(params).then((res) => {
        if (res.status == 200) {
          this.currentChose = row;
          this.currentChose.status = !row.status;
        }
      });
    },
    addIcon() {
      this.$emit("go", "add");
    },
    saveEdit() {
      let params = {
        id: this.currentChose.id,
        isCanShare: this.currentChose.isCanShare,
        isFixed: this.currentChose.isFixed,
        isLogin: this.currentChose.isLogin,
        name: this.name,
        pic: this.currentChose.pic,
        projectId: this.$store.state.projectInfo.id,
        recommend: this.currentChose.recommend,
        sort: this.currentChose.sort,
        url: this.currentChose.url,
      };
      updateTool(params).then((res) => {
        if (res.status == 200) {
          this.init();
        }
      });
      this.settingVisible = false;
    },
    rowDrop() {
      const tbody = document.querySelector(".el-table__body-wrapper tbody");
      let that = this;
      Sortable.create(tbody, {
        onEnd: function(evt) {
          //拖拽结束发生该事件
          that.tableData.splice(
            evt.newIndex,
            0,
            that.tableData.splice(evt.oldIndex, 1)[0]
          );
          var newArray = that.tableData.slice(0);
          that.tableData = [];
          that.$nextTick(function() {
            that.tableData = newArray;
            let ids = [];
            that.tableData.forEach((d) => {
              ids.push(d.id);
            });
            let params = {
              ids: ids,
            };
            setSort(params).then((res) => {
              if (res.status == 200) {
                this.init();
              }
            });
          });
        },
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_setting {
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    span {
      font-size: 13px;
      color: #7f7f7f;
    }
  }
  span {
    margin-left: 10px;
  }
  .setting-main {
    .box {
      display: flex;
      align-items: center;
      height: 50px;
      .switch-box {
        display: flex;
        justify-content: space-between;
        width: 30%;
      }
      .el-input {
        width: 30%;
      }
      span {
        width: 100px;
        white-space: nowrap;
        margin-right: 10px;
      }
    }
  }
}
</style>
