<template>
  <div class="sidebar">
    <el-menu
      class="sidebar-el-menu"
      :default-active="onRoutes"
      :collapse="collapse"
      background-color="rgb(48, 65, 86)"
      text-color="#fff"
      active-text-color="#20a0ff"
      unique-opened
      router
    >
      <template v-for="item in filteredMenu">
        <template v-if="item.childMenu.length > 0 && item.menuType === 1">
          <el-submenu :index="'/' + item.route" :key="item.id">
            <template #title>
              <i :class="item.icon"></i>
              <span>{{ item.title }}</span>
            </template>
            <template v-for="subItem in item.childMenu">
              <template
                v-if="
                  subItem.childMenu &&
                  subItem.childMenu.length > 0 &&
                  subItem.menuType === 1
                "
              >
                <el-submenu :index="'/' + subItem.route" :key="subItem.id">
                  <template #title>{{ subItem.title }}</template>
                  <template v-for="threeItem in subItem.childMenu">
                    <template
                      v-if="
                        threeItem.childMenu &&
                        threeItem.childMenu.length > 0 &&
                        threeItem.menuType === 1
                      "
                    >
                      <el-submenu
                        :index="'/' + threeItem.route"
                        :key="threeItem.id"
                      >
                        <template #title>{{ threeItem.title }}</template>
                        <template v-for="fourItem in threeItem.childMenu">
                          <template v-if="fourItem.menuType === 1">
                            <el-menu-item
                              :index="'/' + fourItem.route"
                              :key="fourItem.id"
                            >
                              {{ fourItem.title }}
                            </el-menu-item>
                          </template>
                        </template>
                      </el-submenu>
                    </template>
                    <template v-else-if="threeItem.menuType === 1">
                      <el-menu-item
                        :index="'/' + threeItem.route"
                        :key="threeItem.id"
                      >
                        <span>{{ threeItem.title }}</span>
                      </el-menu-item>
                    </template>
                  </template>
                </el-submenu>
              </template>
              <template v-else-if="subItem.menuType === 1">
                <el-menu-item
                v-if=" subItem.title === '操作方法' || subItem.title === '使用流程'"
                  :key="subItem.id"
                  class="defa"
                  @click="toHelp(subItem)"
                >
                  <span>{{ subItem.title }}</span>
                </el-menu-item>
                <el-menu-item v-else :index="'/' + subItem.route" :key="subItem.id">
                {{ subItem.title }}
                </el-menu-item>
                
              </template>
            </template>
          </el-submenu>
        </template>
        <template v-else-if="item.menuType === 1">
          <el-menu-item :index="'/' + item.route" :key="item.id">
            <i :class="item.icon"></i>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script>
import { sysMenuTree } from "@/request/service.js";
export default {
  data() {
    return {
      treeMenu: [],
      onRoutes: "home",
    };
  },
  computed: {
    filteredMenu() {
      return this.filterMenuByType(this.treeMenu, 1);
    },
    collapse() {
      return this.$store.state.collapse;
    },
  },
  created() {
    this.onRoutes = this.$route.path;
  },
  mounted() {
    let param = {
      title: "",
    };
    sysMenuTree(param).then((res) => {
      this.$store.commit("setMenuInfo", JSON.parse(JSON.stringify(res.data)));
      this.treeMenu = res.data;
    });
    window.addEventListener("resize", () => {
      const width = document.documentElement.clientWidth;
      if (width < 800) {
        this.$store.commit("hadndleCollapse", true);
      }
      if (width > 1300) {
        this.$store.commit("hadndleCollapse", false);
      }
    });
  },
  methods: {
    filterMenuByType(menu) {
      return menu.reduce((filtered, item) => {
        if (item.menuType !== 2) {
          if (item.childMenu && item.childMenu.length > 0) {
            item.childMenu = this.filterMenuByType(item.childMenu);
          }
          filtered.push(item);
        }
        return filtered;
      }, []);
    },
    toHelp(e) {
      window.open(e.remark);
    },
  },
};
</script>

<style scoped>
.sidebar {
  display: block;
  position: absolute;
  left: 0;
  top: 48px;
  bottom: 0;
  overflow-y: scroll;
}

.sidebar::-webkit-scrollbar {
  width: 0;
}
.sidebar-el-menu:not(.el-menu--collapse) {
  width: 210px;
}
.sidebar > ul {
  height: 100%;
}
.sidebar >>> .el-menu-item:hover {
  background: #1c88cf !important;
  color: #fff !important;
}
.sidebar >>> .el-submenu__title:hover {
  background: #1c88cf !important;
  color: #fff !important;
}
.defa {
  color: #fff !important;
}
</style>
