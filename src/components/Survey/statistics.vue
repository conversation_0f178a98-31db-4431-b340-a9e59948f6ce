<template>
  <div class="container">
    <!-- 填空题 -->
    <el-dialog v-model="visible" :show-close="false">
      <template #title="{ titleId, titleClass }">
        <div class="my-header">
          <div :id="titleId" :class="titleClass">
            {{ diaLabel }} <span style="color: darkgray"> [填空题]</span>
          </div>
          <el-button type="default" @click="exportExcel"> 导出excel </el-button>
          <!-- <el-icon @click="close" class="my_icon"><Close /></el-icon> -->
        </div>
      </template>
      <el-table :data="gridData" border="1">
        <el-table-column
          property="sortNum"
          width="70"
          label="序号"
          align="center"
        />
        <el-table-column
          property="submitTime"
          label="提交时间"
          align="center"
        />
        <el-table-column property="userName" label="用户姓名" align="center" />
        <el-table-column property="content" min-width="250" label="答案文本" />
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <div class="header">
      <span style="font-weight: 600">{{ $route.query.templateName }}</span>
      <div>
        <div>
          <div class="_b">
            回收量<el-tooltip
              popper-class="atooltip"
              effect="light"
              placement="top"
            >
              <i
                class="el-icon-question"
                style="font-size: 14px; vertical-align: middle"
              ></i>
              <template #content>
                <div>
                  <div>1.问卷上线后，累计提交的用户数；</div>
                  <div>
                    2.当根据时间、用户组、省市自定义筛选时，会统计符合要求的问卷。
                  </div>
                </div>
              </template>
            </el-tooltip>
          </div>
          <div>{{ tableData.recycleNum }}</div>
        </div>
        <div>
          <div class="_b">
            浏览量<el-tooltip
              popper-class="atooltip"
              effect="light"
              placement="top"
            >
              <i
                class="el-icon-question"
                style="font-size: 14px; vertical-align: middle"
              ></i>
              <template #content>
                <div>
                  <div>1.问卷上线后，累计查看问卷详情的用户数；</div>
                  <div>
                    2.当根据时间、用户组、省市自定义筛选时，浏览量会根据这3个字段去重。
                  </div>
                </div>
              </template>
            </el-tooltip>
          </div>
          <div>{{ tableData.viewNum }}</div>
        </div>
        <div>
          <div class="_b">
            回收率<el-tooltip
              popper-class="atooltip"
              effect="light"
              placement="top"
            >
              <i
                class="el-icon-question"
                style="font-size: 14px; vertical-align: middle"
              ></i>
              <template #content>
                <div>
                  <div>1.回收量/浏览量*100%</div>
                  <div>
                    2.当根据时间、用户组、省市自定义筛选（统计周期改变）时，可能会存在回收率高于100%的情况。
                  </div>
                </div>
              </template>
            </el-tooltip>
          </div>
          <div>{{ tableData.recyclePercent }}%</div>
        </div>
        <img id="no" src="@/assets/img/export.png" alt="" @click="sub" />
      </div>
    </div>
    <div class="search">
      <div class="s_content" id="no1">
        <div style="display: flex">
          <div class="s_item">
            <el-date-picker
              v-model="allTime"
              type="daterange"
              range-separator="~"
              value-format="YYYY-MM-DD HH:mm:ss"
              start-placeholder="提交开始时间"
              end-placeholder="提交结束时间"
              :size="size"
            />
          </div>
          <div class="s_item">
            <el-cascader
              placeholder="请选择用户组"
              :options="RoleList"
              ref="cascader"
              collapse-tags
              @change="change"
              :props="{
                checkStrictly: true,
                value: 'id',
                label: 'title',
                children: 'childRoles',
                multiple: true,
              }"
              clearable
            >
            </el-cascader>
          </div>
          <div class="s_item">
            <el-select v-model="addressId" class="m-2" placeholder="请选择位置">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div class="search_btn">
          <el-button @click="search" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div
        class="c_item"
        v-for="(item, index) in tableData.statistics"
        :key="index"
      >
        <div class="title d_item">
          <div class="btn2">
            <div>
              第{{ item.sortId }}题 {{ item.label }}
              <span class="t_type"
                >[{{
                  item.type == "checkbox"
                    ? "多选"
                    : item.type == "radio"
                    ? "单选"
                    : item.type == "input"
                    ? "填空题"
                    : item.type == "textarea"
                    ? "填空题"
                    : ""
                }}]</span
              >
            </div>
            <span>
              <el-button
                id="no"
                v-if="item.type == 'input' || item.type == 'textarea'"
                @click="details(item)"
                >详细作答情况</el-button
              >
              <el-button
                v-if="
                  (item.type == 'input' || item.type == 'textarea') &&
                  item.wordClouds.length > 0
                "
                id="no"
                @click="subPng(index + 1)"
                >保存</el-button
              ></span
            >
          </div>
        </div>
        <div class="d_item" style="overflow: hidden">
          <el-table
            v-if="item.type == 'checkbox' || item.type == 'radio'"
            :data="item.choiceStatistics"
            style="width: 100%"
          >
            <el-table-column prop="label" label="选项" align="center" />
            <el-table-column
              prop="selectCount"
              label="小计"
              sortable
              align="center"
            />
            <el-table-column prop="proportion" label="比例" align="center">
              <template #default="scope">
                <el-progress
                  style="width: 300px; margin: 0 auto"
                  :percentage="scope.row.proportion"
                />
              </template>
            </el-table-column>
          </el-table>
          <div
            class="tb"
            v-if="item.type == 'checkbox' || item.type == 'radio'"
          >
            <div style="width: 33.3%">本题有效填写人次</div>
            <div style="width: 33.3%">{{ item.totalCompleteCount }}</div>
          </div>
          <div
            class="zhanweiBox"
            v-if="item.type == 'input' || item.type == 'textarea'"
          >
            <div class="zhanwei" v-if="item.wordClouds.length > 0">
              <div class="wordcloud-container">
                <div
                  :id="'linesChart' + (index + 1)"
                  :style="{ width: '100%', height: '300px' }"
                  :colors="colors"
                  :data="words"
                ></div>
              </div>
            </div>
            <div class="zhanwei1" v-else>
              <img src="@/assets/img/noPoint.png" alt="" />
              <span style="font-size: 12px; color: #999999">-暂无数据-</span>
            </div>
          </div>
        </div>
        <div
          v-if="item.type == 'checkbox' || item.type == 'radio'"
          class="btn d_item"
        >
          <span id="no">
            <el-button>表格</el-button>
            <el-button @click="checkEcharts('pie', index + 1)">饼图</el-button>
            <el-button @click="checkEcharts('pie1', index + 1)">圆环</el-button>
            <el-button @click="checkEcharts('bar', index + 1)">柱图</el-button>
            <el-button @click="checkEcharts('shadow', index + 1)"
              >条形</el-button
            >
            <el-button @click="checkEcharts('category', index + 1)"
              >折线</el-button
            >
          </span>
        </div>
        <div
          style="border: solid 1px #eceef5"
          v-show="
            (activeList &&
              activeList.length > 0 &&
              activeList.indexOf(index + 1) != -1 &&
              item.type == 'checkbox') ||
            (activeList &&
              activeList.length > 0 &&
              activeList.indexOf(index + 1) != -1 &&
              item.type == 'radio')
          "
        >
          <div
            :id="'linesChart' + (index + 1)"
            style="width: 800px; height: 300px; margin: 20px auto 0"
            class="d_item"
          ></div>
        </div>
        <div
          class="btn1 d_item"
          v-if="
            activeList &&
            activeList.length > 0 &&
            activeList.indexOf(index + 1) != -1 &&
            index < 10
          "
        >
          <el-button id="no" @click="subPng(index + 1)" v-if="index < 10"
            >保存</el-button
          >
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="下载报告" width="30%">
      <div style="margin-bottom: 20px">选择文档格式：</div>
      <div>
        <el-radio-group v-model="radio1" class="ml-4">
          <el-radio label="1" size="large">.docx</el-radio>
          <el-radio label="2" size="large">.doc</el-radio>
          <el-radio label="3" size="large">PDF</el-radio>
        </el-radio-group>
        <div v-if="radio1==1||radio1==2" style="color:#999999;margin-top:10px;font-size:12px;">办公软件升级成新版，方可正常显示图片</div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import JsPDF from "jspdf";
import html2canvas from "html2canvas";
import * as echarts from "echarts";
import "echarts-wordcloud/dist/echarts-wordcloud";
import "echarts-wordcloud/dist/echarts-wordcloud.min";
import {
  statisticsDetail,
  subjectiveList,
  provinceList,
  query,
} from "@/request/service.js";
import { exportSubjectiveList } from "@/request/download.js";
import { exportWord, getBase64Sync } from "@/utils/exportFile.js";
import { Close } from "@element-plus/icons";
import { ElMessage } from "element-plus";
export default {
  components: {
    Close,
  },
  data() {
    return {
      visible: false,
      options: [{ item: "", label: "" }],
      echartsList: [],
      echartsListTitle: [],
      echartsListValue: [],
      diaLabel: "",
      tableData: {},
      lineOptions: {},
      active: 0,
      type: "",
      dialogVisible: false,
      radio1: "3",
      activeList: [],
      addressId: "",
      submitEndTime: "",
      submitStartTime: "",
      roleIds: [],
      RoleList: [],
      allTime: [],
      gridData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      propItem: "",
    };
  },
  watch: {
    allTime: {
      handler(val) {
        if (val && val.length > 0) {
          this.submitStartTime = val[0];
          this.submitEndTime = val[1];
        }
      },
    },
    lineOptions: {
      handler(val) {
        this.$nextTick(() => {
          var myChart = echarts.init(
            document.getElementById(`linesChart${this.active}`),
            null,
            {
              renderer: "svg",
            }
          );
          myChart.setOption(this.lineOptions);
        });
      },
    },
  },
  mounted() {
    if (this.$route.query.type) {
      this.init();
      this.getPowerData();
      this.province();
    }
  },
  methods: {
    //导出主观题
    exportExcel() {
      let params = {
        addressId: this.addressId,
        formId: this.$route.query.formId,
        roleIds: this.roleIds,
        submitEndTime: this.submitEndTime,
        submitStartTime: this.submitStartTime,
        surveyId: this.$route.query.surveyId,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        prop: this.propItem.prop,
      };
      exportSubjectiveList(params).then((res) => {
        if (res.size == 51) {
          ElMessage.warning("暂无数据导出");
        } else if (res.size == 63) {
          ElMessage.warning("服务错误，请稍后再试......");
        } else {
          let name = `${this.$route.query.templateName}.xlsx`;
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        }
      });
    },
    // 词云
    yun() {
      let arr = [];
      this.tableData.statistics.forEach((element, index) => {
        element.isImg = false;
        if (element.type == "input" || element.type == "textarea") {
          arr.push(index + 1);
        }
        element.wordClouds.forEach((res) => {
          res.name = res.word;
          res.value = res.weight;
        });
      });
      arr.forEach((element) => {
        setTimeout(() => {
          // 获取要保存为 PNG 的 DOM 元素
          const element1 = document.getElementById(`linesChart${element}`);
          // 将该元素转换为 Canvas 对象并导出为 PNG 图片
          html2canvas(element1).then((canvas) => {
            const pngUrl = canvas.toDataURL("image/png");
            if (pngUrl == "data:,") {
            } else {
              this.tableData.statistics[element - 1].img = pngUrl;
              this.tableData.statistics[element - 1].isImg = true;
            }
          });
        }, 1000);
        this.initChart(
          element,
          this.tableData.statistics[element - 1].wordClouds
        );
      });
    },
    initChart(index, words) {
      this.chart = echarts.init(document.getElementById(`linesChart${index}`));
      const option = {
        backgroundColor: "#fff",
        // tooltip: {
        //   pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>"
        // },

        series: [
          {
            type: "wordCloud",
            //用来调整词之间的距离
            gridSize: 15,
            //用来调整字的大小范围
            // Text size range which the value in data will be mapped to.
            // Default to have minimum 12px and maximum 60px size.
            sizeRange: [14, 60],
            // Text rotation range and step in degree. Text will be rotated randomly in range [-90,                                                                             90] by rotationStep 45
            //用来调整词的旋转方向，，[0,0]--代表着没有角度，也就是词为水平方向，需要设置角度参考注释内容
            rotationRange: [0, 0],
            // rotationStep :90,
            //随机生成字体颜色
            // maskImage: maskImage,
            textStyle: {
              fontFamily: "微软雅黑",
              fontWeight: "600",
              color: function (i) {
                // Random color
                 return (
                  "rgb(" +
                  [
                    Math.round(Math.random() * 200),
                    Math.round(Math.random() * 250),
                    Math.round(Math.random() * 250),
                  ].join(",") +
                  ")"
                );
              },
            },
            //位置相关设置
            // Folllowing left/top/width/height/right/bottom are used for positioning the word cloud
            // Default to be put in the center and has 75% x 80% size.
            left: "center",
            top: "center",
            right: null,
            bottom: null,
            width: "200%",
            height: "200%",
            //数据
            data: words,
          },
        ],
      };
      this.chart.setOption(option);
    },
    async toWord() {
      // this.wordData.src = await getBase64Sync(this.url)
      if (this.radio1 == "1") {
        exportWord(
          "./template1.docx",
          this.tableData,
          this.$route.query.templateName + `.docx`
        );
      }
      if (this.radio1 == "2") {
        exportWord(
          "./template1.doc",
          this.tableData,
          this.$route.query.templateName + `.doc`
        );
      }
    },
    close() {
      this.visible = false;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.details(this.propItem);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.details(this.propItem);
    },
    // 获取省市
    province() {
      let params = {
        levelType: 1,
      };
      provinceList(params).then((res) => {
        if (res.status == "200") {
          this.options = res.data;
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    //  点击查询用户组选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.roleIds = [];
      nodesObj.forEach((item) => this.roleIds.push(item.data.id));
    },
    // 查询统计详情
    init() {
      let params = {
        addressId: this.addressId,
        formId: this.$route.query.formId,
        roleIds: this.roleIds,
        submitEndTime: this.submitEndTime,
        submitStartTime: this.submitStartTime,
        surveyId: this.$route.query.surveyId,
      };
      statisticsDetail(params)
        .then((res) => {
          res.data.statistics.forEach((ele) => {
            ele.isCheckbox = false;
            ele.isInput = false;
            ele.isRadios = false;
            ele.isTextarea = false;
          });
          res.data.statistics.forEach((j) => {
            if (j.type == "input") {
              j.isInput = true;
            }
            if (j.type == "checkbox") {
              j.isCheckbox = true;
            }
            if (j.type == "radio") {
              j.isRadios = true;
            }
            if (j.type == "textarea") {
              j.isTextarea = true;
            }
          });
          this.tableData = res.data;
          this.tableData.templateName = this.$route.query.templateName;
          setTimeout(() => {
            this.yun();
          }, 0);
        })
        .catch((err) => {});
    },
    // 重置
    reset() {
      this.allTime = [];
      this.RoleList = [];
      this.roleIds = [];
      this.addressId = "";
      let params = {
        addressId: "",
        formId: this.$route.query.formId,
        roleIds: [],
        submitEndTime: "",
        submitStartTime: "",
        surveyId: this.$route.query.surveyId,
      };
      statisticsDetail(params)
        .then((res) => {
          this.tableData = res.data;
        })
        .catch((err) => {});
      this.getPowerData();
    },
    // 查询
    search() {
      this.init();
    },
    // 填空题查看详情
    details(item) {
      this.diaLabel = `第${item.sortId}题 ${item.label}`;
      this.visible = true;
      this.propItem = item;
      let params = {
        addressId: this.addressId,
        formId: this.$route.query.formId,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        prop: this.propItem.prop,
        roleIds: this.roleIds,
        submitEndTime: this.submitEndTime,
        submitStartTime: this.submitStartTime,
      };
      subjectiveList(params).then((res) => {
        if (res.status == 200) {
          this.gridData = res.data;
          this.total = res.totalSize;
        }
      });
    },
    // 导出
    submit() {
      if (this.radio1 == "3") {
        this.dialogVisible = false;
        setTimeout(() => {
          this.exportPdf();
        }, 1000);
      } else if (this.radio1 == "2" || this.radio1 == "1") {
        this.dialogVisible = false;
        this.toWord();
      } else {
        ElMessage.error("请选择");
        return false;
      }
    },
    isSplit(nodes, index, pageHeight) {
      if (nodes[index + 1]) {
      }
      // 计算当前这块dom是否跨越了a4大小，以此分割
      if (
        nodes[index].offsetTop + nodes[index].offsetHeight < pageHeight &&
        nodes[index + 1] &&
        nodes[index + 1].offsetTop + nodes[index + 1].offsetHeight > pageHeight
      ) {
        return true;
      }
      return false;
    },

    exportPdf() {
      window.pageYoffset = 0;

      document.documentElement.scrollTop = 0;

      document.body.scrollTop = 0;
      // 获取要导出为 PDF 的 DOM 元素
      const element = document.getElementsByClassName("container")[0];

      // 定义所需的选项
      const options = {
        dpi: window.devicePixelRatio, //将分辨率提高到特定的DPI 提高四倍
        scale: 1, //按比例增加分辨率
        // 是否忽略指定的元素
        ignoreElements: (element) => {
          return element.id === "no" || element.id === "no1";
        },
      };
      let lableListID = document.getElementsByClassName("d_item");
      let pageHeight = (element.scrollWidth / 592.28) * 841.89;
      // 进行分割操作，当dom内容已超出a4的高度，则将该dom前插入一个空dom，把他挤下去，分割
      for (let i = 0; i < lableListID.length; i++) {
        let multiple = Math.ceil(
          (lableListID[i].offsetTop + lableListID[i].offsetHeight) / pageHeight
        );
        if (this.isSplit(lableListID, i, multiple * pageHeight)) {
          let divParent = lableListID[i].parentNode; // 获取该div的父节点
          let newNode = document.createElement("div");
          newNode.className = "emptyDiv";
          newNode.style.background = "#ffffff";
          let _H =
            multiple * pageHeight -
            (lableListID[i].offsetTop + lableListID[i].offsetHeight);
          newNode.style.height = _H + 80 + "px";
          newNode.style.width = "100%";
          let next = lableListID[i].nextSibling; // 获取div的下一个兄弟节点
          // 判断兄弟节点是否存在
          if (next) {
            // 存在则将新节点插入到div的下一个兄弟节点之前，即div之后
            divParent.insertBefore(newNode, next);
          } else {
            // 不存在则直接添加到最后,appendChild默认添加到divParent的最后
            divParent.appendChild(newNode);
          }
        }
      }

      // 将该元素转换为 canvas 对象，以便于插入到 PDF 中
      html2canvas(element, options).then((canvas) => {
        const contentWidth = canvas.width;
        //内容的高度
        const contentHeight = canvas.height;
        //一页pdf显示htm1页面生成的canvas高度，a4纸的尺寸[595.28,841.89];
        const pageHeight = (contentWidth / 592.28) * 841.89;
        //未生成pdf的htm1页面高度
        let leftHeight = contentHeight;
        //页面偏移
        let position = 0;
        //a4纸的尺寸[595.28,841.89],htm1页面生成的canvas在pdf中图片的宽高
        const imgwidth = 595.28;
        const imgHeight = (592.28 / contentWidth) * contentHeight;
        //canvas转图片数据
        const pageData = canvas.toDataURL("img/jpeg", 1.0);
        //新建JSPDF对象
        const PDF = new JsPDF("p", "pt", "a4");
        if (leftHeight < pageHeight) {
          PDF.addImage(pageData, "JPEG", 0, 0, imgwidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            PDF.addImage(pageData, "JPEG", 0, position, imgwidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;
            if (leftHeight > 0) {
              PDF.addPage();
            }
          }
        }
        PDF.save(this.$route.query.templateName + ".pdf");
        let emptyDiv = document.getElementsByClassName("emptyDiv");
        for (let i = 0; i < emptyDiv.length; i++) {
          emptyDiv[i].remove();
          i = i - 1;
        }
      });
    },
    subPng(index) {
      // 获取要保存为 PNG 的 DOM 元素
      const element = document.getElementById(`linesChart${index}`);
      // 将该元素转换为 Canvas 对象并导出为 PNG 图片
      html2canvas(element).then((canvas) => {
        const pngUrl = canvas.toDataURL("image/png");
        const downloadLink = document.createElement("a");
        downloadLink.href = pngUrl;
        downloadLink.download = "image.png";
        downloadLink.click();
      });
    },
    //   销毁
    destoryEcharts() {
      var myChart = echarts.init(
        document.getElementById(`linesChart${this.active}`),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.dispose();
    },
    // 保存当页
    sub() {
      this.dialogVisible = true;
      this.radio1 = "3";
    },
    getEqualNewlineString(params, length) {
      let text = "";
      let count = Math.ceil(params.length / length); // 向上取整数
      // 一行展示length个
      if (count > 1) {
        for (let z = 1; z <= count; z++) {
          text += params.substr((z - 1) * length, length);
          if (z < count) {
            text += "\n";
          }
        }
      } else {
        text += params.substr(0, length);
      }
      return text;
    },
    // 显示echarts
    checkEcharts(val, index) {
      this.active = index;
      this.activeList.push(index);
      this.type = val;
      if (document.getElementById(`linesChart${this.active}`)) {
        this.destoryEcharts();
      }
      this.echartsListTitle = this.tableData.statistics[
        index - 1
      ].choiceStatistics.map((item) => {
        return item.label;
      });
      this.echartsListValue = this.tableData.statistics[
        index - 1
      ].choiceStatistics.map((item) => {
        return item.proportion;
      });
      this.echartsList = this.tableData.statistics[
        index - 1
      ].choiceStatistics.map((item) => {
        return { name: item.label, value: item.proportion };
      });
      switch (val) {
        case "pie":
          this.lineOptions = {
            tooltip: {
              trigger: "item",
            },
            legend: {
              left: "center",
              top: "bottom",
            },
            series: [
              {
                name: "Access From",
                type: "pie",
                radius: "70%",
                data: this.echartsList,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: (params) => {
                      if (params.data.value) {
                        return `${params.data.value}%`;
                      } else {
                        return "";
                      }
                    }, // 格式化数值百分比输出
                  },
                },
                labelLine: {
                  normal: {
                    position: "inner",
                    show: false,
                  },
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: "rgba(0, 0, 0, 0.5)",
                  },
                },
              },
            ],
          };
          break;
        case "pie1":
          this.lineOptions = {
            tooltip: {
              trigger: "item",
            },
            legend: {
              top: "bottom",
              left: "center",
            },
            series: [
              {
                name: "Access From",
                type: "pie",
                radius: ["40%", "70%"],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: "#fff",
                  borderWidth: 2,
                },
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: (params) => {
                      if (params.data.value) {
                        return `${params.data.value}%`;
                      } else {
                        return "";
                      }
                    }, // 格式化数值百分比输出
                  },
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: "bold",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: this.echartsList,
              },
            ],
          };
          break;
        case "bar":
          this.lineOptions = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            xAxis: {
              type: "category",
              data: this.echartsListTitle,
              axisLabel: {
                interval: 0,
                formatter: (params) => {
                  //调用自定义显示格式
                  return this.getEqualNewlineString(params, 8);
                },
              },
            },
            yAxis: {
              type: "value",
            },
            series: [
              {
                data: this.echartsListValue,
                type: "bar",
                barWidth: 40,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: "{c}%", // 格式化数值百分比输出
                  },
                },
              },
            ],
          };
          break;
        case "shadow":
          this.lineOptions = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            legend: {},
            grid: {
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true,
            },

            xAxis: {
              type: "value",
              boundaryGap: [0, 0.01],
            },
            yAxis: {
              type: "category",
              data: this.echartsListTitle,
            },
            series: [
              {
                type: "bar",
                data: this.echartsListValue,
                label: {
                  normal: {
                    show: true,
                    position: "inner", // 数值显示在内部
                    formatter: "{c}%", // 格式化数值百分比输出
                  },
                },
              },
            ],
          };
          break;
        case "category":
          this.lineOptions = {
            tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
            xAxis: {
              type: "category",
              data: this.echartsListTitle,
              axisLabel: {
                interval: 0,
                formatter: (params) => {
                  //调用自定义显示格式
                  return this.getEqualNewlineString(params, 8);
                },
              },
            },
            yAxis: {
              type: "value",
            },
            series: [
              {
                data: this.echartsListValue,
                type: "line",
              },
            ],
          };
          break;
      }
      setTimeout(() => {
        // 获取要保存为 PNG 的 DOM 元素
        const element = document.getElementById(`linesChart${index}`);
        // 将该元素转换为 Canvas 对象并导出为 PNG 图片
        html2canvas(element).then((canvas) => {
          const pngUrl = canvas.toDataURL("image/png");
          this.tableData.statistics[index - 1].img = pngUrl;
          this.tableData.statistics[index - 1].isImg = true;
        });
      }, 1000);
    },
  },
};
</script>
<style>
.atooltip {
  background: #909399 !important;
  color: #fff;
}
.atooltip.el-popper.is-light .el-popper__arrow::before {
  background: #909399;
}
</style>
<style lang="scss" scoped>
.container {
  height: 100%;
  background: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div {
      display: flex;
      align-items: center;
      img {
        width: 30px;
        height: 30px;
      }
      img:hover {
        cursor: pointer;
      }
      ._b {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      div {
        text-align: center;
        margin-right: 30px;
      }
    }
  }
  .search {
    background: #f8f8f8;
    padding: 10px 20px;
    margin-top: 30px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    .s_content {
      display: flex;
      align-items: flex-end;
      background: #fff;
      padding: 10px 20px;
      width: 100%;
      justify-content: space-between;
      flex-wrap: nowrap;
      .s_item {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }
      .search_btn {
        width: 130px;
      }
      ::v-deep .el-button {
        width: 60px;
        height: 20px !important;
        font-size: 12px;
        text-align: center;
        padding: 0;
      }
      ._mb {
        margin-right: 10px;
      }
    }
  }
  .content {
    .title {
      margin-bottom: 10px;
      .t_type {
        color: #999999;
        margin-left: 10px;
      }
    }
  }
}
#linesChart {
}

.btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100px;
  width: 100%;
}
.btn1 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100px;
  ::v-deep .el-button {
    width: 60px;
    height: 20px !important;
    font-size: 12px;
    text-align: center;
    padding: 0;
  }
}
.btn2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  ::v-deep .el-button {
    height: 20px !important;
    padding: 0 20px;
    font-size: 12px;
    text-align: center;
  }
}
.zhanweiBox {
  text-align: right;
  .zhanwei {
    width: 100%;
    border: solid 1px #f6f6f6;
    display: flex;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 20px;
    img {
      width: 100px;
      margin: 0 auto;
    }
  }
  .zhanwei1 {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border: solid 1px #f6f6f6;
    display: flex;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 20px;
    img {
      width: 100px;
      margin: 0 auto;
    }
  }
}
.tb {
  display: flex;
  text-align: center;
  background: #f9f9f9;
  padding: 12px 0;
  font-size: 14px;
}
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .my_icon:hover {
    cursor: pointer;
  }
}
.el-icon-question:hover {
  cursor: pointer;
}
.wordcloud-container {
  width: 100%;
}
</style>
