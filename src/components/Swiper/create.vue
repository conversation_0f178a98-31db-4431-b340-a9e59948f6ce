<template>
  <div class="yxd_swiper_create">
    <div class="warn">
      {{
        type == "second"
          ? "首页显示的广告位。最多1个，请上传jpg, png格式的图片。建议图片尺寸为 1920x640px，建议图片大小不超过2MB。"
          : "首页显示的轮播图。最多8张，请上传jpg, png格式的图片。建议图片尺寸为1920x640px，建议图片大小不超过2MB。"
      }}
    </div>
    <div class="col">
      <span class="tl"
        ><span style="color:red">*</span
        >{{ type == "second" ? "创建广告位" : "创建轮播图" }}</span
      >
      <el-input v-model="formData.adName" :placeholder="placeholder"></el-input>
    </div>
    <div class="col">
      <span class="tl"><span style="color:red">*</span>PC封面</span>
      <div class="upload-box">
        <div class="upload">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :http-request="customRequestPC"
            :before-upload="beforeAvatarUpload"
            :show-file-list="false"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
            <span class="text" v-if="type=='second'">1180*190px</span>
            <span class="text" v-else>1180*300px</span>
            <div></div>
            <!-- <el-button v-else icon="el-icon-upload">上传文件</el-button> -->
          </el-upload>
           <el-image
              :fit="'cover'"
              v-if="formData.adImage"
              :src="formData.adImage"
              class="avatar"
              ></el-image>
          <!-- <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->
        </div>
        <!-- <span>支持扩展名：.jpg，.png.</span> -->
      </div>
      <!-- h5 -->
      <span class="tl"><span style="color:red">*</span>H5封面</span>
      <div class="upload-box">
        <div class="upload">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :http-request="customRequestH5"
            :before-upload="beforeAvatarUpload"
            :show-file-list="false"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
            <span class="text" v-if="type=='second'" >690*138px</span>
            <span class="text" v-else>690*228px</span>

            <!-- <el-button v-else icon="el-icon-upload">上传文件</el-button> -->
          </el-upload>
           <el-image
              :fit="'cover'"
              v-if="formData.h5Cover"
              :src="formData.h5Cover"
              class="avatar"
              ></el-image>
          <!-- <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->
        </div>
        <!-- <span>支持扩展名：.jpg，.png.</span> -->
      </div>
    </div>
    <div class="col">
      <span class="tl">跳转地址</span>
      <el-select
        style="margin-right:10px;width:180px"
        v-model="typeValue"
        @change="optionChange"
        placeholder="请选择"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.tagId"
        >
        </el-option>
      </el-select>
      <el-input
        v-if="showHttp"
        v-model="formData.adUrl"
        :disabled="disabled"
        placeholder="请输入网址"
      ></el-input>
    </div>
    <div class="col">
      <span class="tl"></span>
      <div class="warn">
        <p>1.头部地址需包含http或https</p>
        <p>2.填写的地址，域名必须已经通过ICP备案</p>
      </div>
    </div>
    <div class="col">
      <span class="tl"><span style="color:red">*</span>设置时间</span>
      <el-date-picker
        v-model="formData.time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="YYYY-MM-DD HH:mm:ss"
      >
      </el-date-picker>
    </div>
    <div class="footer">
      <el-button @click="save" type="primary">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import {
  saveAdvertisement,
  uploadAvatar,
  updateAdvertisementById,
  listTag
} from "@/request/service.js";
import { urlList } from "@/utils/constant.js";
import { ElMessage } from "element-plus";
export default {
  name: "swiper-create",
  props: {
    type: {
      default: "",
      type: String,
    },
    info: {
      default: () => {},
      type: Object,
    },
    operate: {
      default: "",
      type: String,
    },
  },
  data() {
    return {
      searchList: [],
      disabled: false,
      showHttp: true,
      formData: {},
      placeholder: "",
      typeValue: "",
      options: [
        {
          value: "1",
          label: "无跳转",
          tagId:"1",
        },
        {
          value: "2",
          label: "网址",
          tagId:"2",
        },
      ],
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.backInfo.userInfo;
    },
  },
  mounted() {
    if (this.type == "second") {
      this.placeholder = "请输入广告位名称";
    } else {
      this.placeholder = "请输入轮播图名称";
    }
    let params = {
        // tagName: this.title,
        tagIds:[],
        tagType: '',
      };
     listTag(params).then((res) => {
          res.data.forEach(item => {
            if(item.tagType==='article'){
             this.options.push({
             value: '/news',
             label: item.tagName,
             tagId:item.id
            }) 
           }else if(item.tagType==='course'){
             this.options.push({
             value: '/class',
             label: item.tagName,
             tagId:item.id
            }) 
           }else if(item.tagType==='live_info'){
             this.options.push({
             value: '/live',
             label: item.tagName,
             tagId:item.id
            }) 
           }else if(item.tagType==='case'){
             this.options.push({
             value: '/square',
             label: item.tagName,
             tagId:item.id
            }) 
           }else if(item.tagType==='eda'){
             this.options.push({
             value: '/yxd',
             label: item.tagName,
             tagId:item.id
            }) 
           }else{
             this.options.push({
             value: '/'+item.tagType,
             label: item.tagName,
             tagId:item.id
            }) 
           }

          });
    })
    if (Object.keys(this.info).length > 0) {
      this.formData = this.info;
      if (this.info.startAt) {
        this.formData.time = [this.info.startAt, this.info.endAt];
      }
      if (this.formData.adUrl == "") {
        this.showHttp = false;
        this.typeValue = "1";
        this.disabled = false;
      } else if (!urlList.includes(this.formData.adUrl)) {
        this.showHttp = true;
        this.typeValue = "2";
        this.disabled = false;
      } else {
        this.showHttp = true;
        this.disabled = true;
        this.typeValue = this.formData.adUrl;
      }
    }
  },
  methods: {
    optionChange(e) {
      if (e == "1") {
        this.disabled = false;
        this.showHttp = false;
        this.formData.adUrl = "";
      } else if (e == "2") {
        this.formData.adUrl = "";
        this.disabled = false;
        this.showHttp = true;
      } else if (e == "3") {
        this.formData.adUrl = "";
        this.disabled = false;
        this.showHttp = false;
      } else {
        this.disabled = true;
        this.options.map(element => {
          if(element.tagId == e){
             this.formData.adUrl = element.value+'?tagId='+e+'&title='+element.label
          }
        });;
        this.showHttp = true;
      }
    },
    goBack() {
      this.$emit("back");
    },
    verificate() {
      if (!this.formData.adName) {
        ElMessage.warning("轮播图名称不可为空！");
        return false;
      } else if (!this.formData.adImage||!this.formData.h5Cover) {
        ElMessage.warning("图片不可为空！");
        return false;
      } else if (!this.formData.time || this.formData.time.length == 0) {
        ElMessage.warning("时间设置不可为空！");
      } else {
        return true;
      }
    },
    save() {
      if (!this.verificate()) {
        return;
      }
      let startAt = "";
      let endAt = "";
      startAt = this.formData.time[0];
      endAt = this.formData.time[1];
      let adSpaceId;
      switch (this.type) {
        case "first":
          adSpaceId = 145;
          break;
        case "second":
          adSpaceId = 149;
          break;
        case "third":
          adSpaceId = 146;
          break;
        case "fourth":
          adSpaceId = 147;
          break;
        case "five":
          adSpaceId = 148;
          break;
      }
      let params = {
        adImage: this.formData.adImage,
        h5Cover:this.formData.h5Cover,
        adName: this.formData.adName,
        adUrl: this.formData.adUrl,
        endAt: endAt,
        projectId: this.$store.state.projectInfo.id,
        startAt: startAt,
        status: this.info.status==="已上线"? 2: 1,
        directorName: this.userInfo.userName,
        directorId: 1,
        executorId: 1,
        executorName: this.userInfo.userName,
        channel: 1,
        customerId: 1,
        customerName: this.userInfo.userName,
        adSpaceId: adSpaceId,
      };
      params.adUrl = `${this.formData.adUrl}`;
      if (this.operate == "add") {
        saveAdvertisement(params).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      } else {
        params.id = this.info.adId;
        updateAdvertisementById(params).then((res) => {
          if (res.status == 200) {
            this.goBack();
          }
        });
      }
    },
    // 校验图片
    beforeAvatarUpload(file) {
      let isFormat;
      if (
        file.type === "image/png" ||
        file.type === "image/jpg" ||
        file.type === "image/jpeg"
      ) {
        isFormat = true;
      } else {
        isFormat = false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isFormat) {
        ElMessage.error("不支持除jpg,png以外格式的图片！");
        return false;
      }
      if (!isLt2M) {
        ElMessage.error("上传的图片大小不能超过 2MB!");
        return false;
      } else {
        return true;
      }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    // PC上传图片
    customRequestPC(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.formData.adImage = res.data.url;
          }
        });
      });
    },
    // H5上传图片
    customRequestH5(item){
     let _this = this;
      this.filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.formData.h5Cover = res.data.url;
          }
        });
      });
    },
    cancel() {
      this.goBack();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_swiper_create {
  .warn {
    display: block;
    margin-top: 30px;
    color: #7f7f7f;
    font-size: 14px;
  }
  .col {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
    .tl {
      min-width: 80px;
      width: 80px;
      text-align: right;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      white-space: nowrap;
    }
    .warn {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: red;
      line-height: 22px;
      margin-right: 20px;
      margin-top: -20px;
    }
    .upload-box {
      // margin-right: 100px;
      width: 400px;
      .upload {
        display: flex;
        // ::v-deep{
        //   .el-image,.avatar{
        //     width: 188px;
        //     height: 108px;
        //     display: block;  
        //   }
        // }
        .upload-demo,.el-upload {
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          margin-right: 20px;
          background-color: #fafafa;
          &:hover{
            border-color: #409EFF;
          }
          .text{
              position: absolute;
              color: #797979;
              bottom: 5px;
              left: 0;
              right: 0;
          }
        }
        .avatar-uploader-icon {
          font-size: 28px;
          color: #8c939d;
          width: 108px;
          height: 108px;
          line-height: 108px;
          text-align: center;
        }
        .avatar {
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          width: 88px;
          height: 88px;
          display: block;
          padding: 10px;
        }
      
      span {
        display: block;
        margin-top: 20px;
      }
    }
   }
  }
}
</style>
