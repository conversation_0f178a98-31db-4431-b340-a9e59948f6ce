<template>
  <div class="yxd_swiper">
    <div class="header">
      <div class="left">
        <div class="col">
          <span class="tl">{{
            type == "second" ? "广告位名称" : "轮播图名称"
          }}</span>
          <el-input v-model="title" placeholder="请输入内容"></el-input>
        </div>
        <div class="col">
          <span class="tl">上线时间</span>
          <el-date-picker
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="right">
        <el-button @click="init" icon="el-icon-search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
    </div>
    <div class="operate">
      <el-button
        v-if="btnList.add_btn && showCreateBtn"
        @click="createSwiper"
        :disabled="disabled"
        type="primary"
        icon="el-icon-plus"
        >{{ type == "second" ? "创建广告位" : "创建轮播图" }}</el-button
      >
    </div>
    <div class="warn">
      {{
        type == "second"
          ? "首页显示的广告位。最多1个，请上传jpg, png格式的图片。建议图片尺寸为 1180x300px，建议图片大小不超过2MB。"
          : "首页显示的轮播图。最多8张，请上传jpg, png格式的图片。建议图片尺寸为1180x300px，建议图片大小不超过2MB。"
      }}
    </div>
    <div class="table">
      <el-table
        border
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
      >
        <el-table-column
          sortable
          prop="adId"
          label="序号"
          width="120"
          align="center"
        >
        </el-table-column>
        <el-table-column prop="adName" label="轮播图名称" align="center">
        </el-table-column>
        <el-table-column
          prop="executorName"
          label="创建人"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="startAt"
          label="开始时间"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="endAt"
          label="结束时间"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="clickHits"
          label="点击量"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="scope">
            <span
              :class="{ start: scope.row.status == '已上线' }"
              class="default"
            ></span>
            <span>{{ scope.row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          min-width="300"
        >
          <template #default="scope">
            <el-button
              v-if="btnList.edit_btn"
              size="mini"
              type="primary"
              @click="handleEdit(scope.$index, scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="btnList.start_btn"
              size="mini"
              @click="handleDown(scope.$index, scope.row)"
              :disabled="scope.row.disabled"
              >{{ scope.row.status == "已上线" ? "下线" : "上线" }}</el-button
            >
            <el-button
              v-if="btnList.remove_btn"
              size="mini"
              plain
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[2, 4, 6, 8]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
      <span
        >是否{{ lineText == "上线提示" ? "上线" : "下线" }}
        <el-tag>{{ currentChose.adName }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="lineSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
      <span
        >是否删除 <el-tag>{{ currentChose.adName }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="deleteSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import {
  getAdvertisementPage,
  dealAdvertisementByBatchId,
  getCountByYxd,
} from "@/request/service.js";
export default {
  name: "swiper-index",
  mixins: [initModule],
  props: {
    type: {
      default: "",
      type: String,
    },
  },
  data() {
    return {
      title: "",
      time: "",
      deleteVisible: false,
      dialogVisible: false,
      currentChose: {},
      lineText: "上线提示",
      tableData: [],
      identification: "",
      currentPage: 1,
      pageSize: 8,
      total: 0,
      showCreateBtn: true,
      disabled: false,
      spaceId: "",
      lineNum: 0,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      switch (this.type) {
        case "first":
          this.identification = "xyd_index_top";
          this.spaceId = 145;
          break;
        case "second":
          this.identification = "xyd_ad_top";
          this.spaceId = 149;
          break;
        case "third":
          this.identification = "xyd_article_top";
          this.spaceId = 146;
          break;
        case "fourth":
          this.identification = "xyd_live_top";
          this.spaceId = 147;
          break;
        case "five":
          this.identification = "xyd_course_top";
          this.spaceId = 148;
          break;
      }
      let params = {
        identification: this.identification,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
        searchValue: this.title,
      };
      getAdvertisementPage(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.total = res.totalSize;
          this.initStatus();
        }
      });
    },
    initStatus() {
      getCountByYxd(this.spaceId, this.$store.state.projectInfo.id).then(
        (res) => {
          if (res.status == 200) {
            this.lineNum = res.data;
            let num;
            if (this.type == "second") {
              num = 1;
            } else {
              num = 8;
            }
            if (this.lineNum >= num) {
              this.disabled = true;
            } else {
              this.disabled = false;
            }
            this.tableData.forEach((val) => {
              if (val.status == 2) {
                val.status = "已上线";
              } else {
                val.status = "已下线";
              }
              if (this.disabled) {
                if (val.status == "已下线") {
                  val.disabled = true;
                } else {
                  val.disabled = false;
                }
              } else {
                val.disabled = false;
              }
            });
          }
        }
      );
    },
    createSwiper() {
      this.$emit("create", {});
      this.$emit("operate", "add");
    },
    goTab(row) {
      this.$emit("create", row);
      this.$emit("operate", "edit");
    },
    search() {},
    reset() {
      this.title = "";
      this.time = "";
    },
    handleEdit(index, row) {
      this.goTab(row);
    },
    handleDown(index, row) {
      if (row.status == "已上线") {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.adId);
      let params = {
        dealType: this.lineText == "上线提示" ? 1 : 2,
        ids: ids,
      };
      dealAdvertisementByBatchId(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.adId);
      let params = {
        dealType: 3,
        ids: ids,
      };
      dealAdvertisementByBatchId(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_swiper {
  .header {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }
  
  .warn {
    display: block;
    margin-top: 30px;
    color: #7f7f7f;
    font-size: 14px;
  }
  .table {
    margin-bottom: 30px;
    .default {
      border-radius: 50%;
      width: 10px;
      height: 10px;
      background: #7f7f7f;
      margin-right: 5px;
    }
    .start {
      border-radius: 50%;
      width: 10px;
      height: 10px;
      background: rgb(62, 201, 119);
      margin-right: 5px;
    }
  }
}
</style>
