<template>
  <div class="tool-bar" :style="styleObj">
    <slot name="tool-content"></slot>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
export default {
  name: 'footer-tool-bar',
  computed: {
    ...mapGetters(['collapse']),
    styleObj() {
      return {
        'width': `calc(100% - ${this.collapse ? 60 : 180}px)`,
        'transition': 'width 0.3s'
      }
    }
  }
}
</script>

<style scope lang="scss">
.tool-bar{
  position: fixed;
  width: 100%;
  bottom: 0;
  right: 0;
  // height: 50px;
  // line-height: 50px;
  -webkit-box-shadow: 0 -1px 2px rgba(0,0,0,.03);
  box-shadow: 0 -1px 2px rgba(0,0,0,.03);
  background: #fff;
  border-top: 1px solid #e8e8e8;
  padding: 11px 24px 10px;
  z-index: 9;
  text-align: right;
}
</style>
