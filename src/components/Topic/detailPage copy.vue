<template>
  <el-select
    v-model="selectedOption"
    filterable
    remote
    multiple
    allow-create
    :remote-method="handleRemoteSearch"
    @change="handleSelectChange"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  data() {
    return {
      options: [], // 所有选项数据
      selectedOption: '' // 当前选中的选项值
    };
  },
  methods: {
    handleRemoteSearch(query) {
      // 根据query发送请求到服务器端进行远程搜索
      // 这里仅作示例，您需要根据实际情况进行相应的请求处理

      // 模拟异步请求
      setTimeout(() => {
        // 假设从服务器端获取到的搜索结果为responseOptions
        const responseOptions = [
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' },
          { value: 'option3', label: 'Option 3' },
          // 其他选项...
        ];

        // 将获取到的搜索结果赋值给options
        this.options = responseOptions;
      }, 500);
    },
    handleSelectChange(value) {
      if (!this.options.find(option => option.value === value)) {
        // 如果选择的选项不在选项列表中，将其添加为新选项
        const newOption = {
          value: value,
          label: value
        };
        this.options.push(newOption);
      }
    }
  }
};
</script>