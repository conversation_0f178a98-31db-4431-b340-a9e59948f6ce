<template>
  <div>
    <div class="header">
      <div class="title">
        <span class="tl">题库信息</span>
        <i @click="editQuestionBank" class="el-icon-edit-outline"></i>
      </div>
      <div class="content">
        <div class="col">
          <span class="tl">题库ID</span>
          <span>{{ TopicBankId }}</span>
        </div>
        <div class="col">
          <span class="tl">题库名称</span>
          <el-input disabled v-model="QuestionBankName"></el-input>
        </div>
      </div>
    </div>
    <div class="operate">
      <div class="left">
        <el-button type="primary" @click="addTopic" v-if="btnList.add_question"
          >添加题目</el-button
        >
        <el-button
          type="primary"
          @click="goTextRecognition"
          v-if="btnList.text_question_import && mode !== 1"
          >文本批量导入</el-button
        >
        <el-button v-if="btnList.excel_question_import" @click="addExcelDialog"
          >Excel批量导入</el-button
        >
        <el-button v-if="btnList.batch_export_edit" @click="deriveData"
          >批量导出/修改</el-button
        >
        <el-button @click="delBatch()" v-if="btnList.batch_remove"
          >批量删除</el-button
        >
      </div>
      <div class="right">
        <el-input
          v-model="topic"
          placeholder="请输入题干关键字搜索"
          @keydown.enter="init()"
        >
          <template v-slot:suffix>
            <i class="el-icon-search" @click="init()"></i>
          </template>
        </el-input>
      </div>
    </div>
    <div class="table">
      <el-table
        ref="multipleTable"
        border
        :data="tableData"
        @selection-change="handleSelectionChange"
        tooltip-effect="dark"
      >
        <el-table-column
          type="selection"
          width="70"
          align="center"
        ></el-table-column>
        <el-table-column prop="title" label="题目" align="center">
        </el-table-column>
        <el-table-column
          prop="title"
          label="标签"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.labelList&&scope.row.labelList.map((obj) => obj.name).join(" | ") }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="题型" align="center" width="150">
          <template v-slot:header #header="scope">
            <el-dropdown trigger="click">
              <div>
                <span>题型</span>
                <el-icon><Filter /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="init(1)">单选题</el-dropdown-item>
                  <el-dropdown-item @click="init(2)">多选题</el-dropdown-item>
                  <el-dropdown-item @click="init()">全部</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template #default="scope">
            <div>{{ scope.row.type === 1 ? "单选" : "多选" }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              size="mini"
              type="primary"
              @click="editQuestion(scope.row.id, scope.row.labelList)"
              v-if="btnList.question_edit_btn"
              >编辑</el-button
            >
            <el-button
              size="mini"
              plain
              type="danger"
              @click="delQuestion(scope.row.id)"
              v-if="btnList.question_remove_btn"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50, 100, 500, 1000]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 更改题库名称弹框 -->
    <el-dialog title="修改题库信息" v-model="QuestionBankVisible" width="50%">
      <div class="dialog">
        <span class="tl"><span style="color: red">*</span> 题库名称</span>
        <el-input
          style="width: 75%"
          v-model.trim="BankName"
          placeholder="请输入64字以内的题库名称"
        ></el-input>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <span>
            <el-button @click="QuestionBankVisible = false">取 消</el-button>
            <el-button type="primary" @click="editQuestionBankName()"
              >确 定</el-button
            >
          </span>
        </div>
      </template>
    </el-dialog>
    <!-- 添加/编辑题目弹框 -->
    <el-dialog
      :title="dialogType"
      v-model="TopicVisible"
      width="50%"
      :before-close="outTopicVisible"
    >
      <el-tabs v-model="topicType" @tab-click="change">
        <el-tab-pane
          label="单选题"
          name="1"
          :disabled="dialogType === '编辑标题'"
        >
          <div class="topicType1" :type="topicType">
            <div class="col1">
              <span class="tl"><span style="color: red">*</span> 题目</span>
              <el-input
                type="textarea"
                style="width: 75%"
                v-model.trim="TopicName"
                placeholder="请输入题干"
              ></el-input>
            </div>
            <div class="col2">
              <span class="tl"><span style="color: red">*</span> 选项</span>
              <div class="right">
                <div
                  class="option"
                  v-for="item in topicOption"
                  :key="item.lable"
                >
                  <span class="option-left">{{ item.lable }}</span
                  ><el-input
                    style="width: 75%"
                    v-model.trim="item.value"
                    placeholder="请输入选项内容"
                  ></el-input
                  ><i
                    @click="delTopic(item.lable)"
                    class="el-icon-delete"
                    v-if="editOption"
                  ></i>
                </div>
                <div class="addOption"  v-if="editOption">
                  <span @click="addOption"> 添加选项 </span>
                </div>
              </div>
            </div>
            <div class="col1" v-if="mode != 1">
              <span class="tl"><span style="color: red">*</span> 答案</span>
              <div>
                <el-radio
                  v-for="item in topicOption"
                  :key="item.lable"
                  v-model="radio"
                  :label="item.lable"
                  >{{ item.lable }}</el-radio
                >
              </div>
            </div>
            <div class="col1" v-if="mode != 1">
              <span class="tl">答案解析</span>
              <el-input
                type="textarea"
                style="width: 75%"
                v-model.trim="TopicParse"
                placeholder="请输入解析"
              ></el-input>
            </div>
            <div class="col1" >
              <span class="tl">标签</span>
              <el-select
                v-model="value"
                multiple
                allow-create
                filterable
                remote
                reserve-keyword
                placeholder="请输入并选择标签"
                :remote-method="remoteMethod"
                :loading="loading"
                @change="change1"
                :disabled="!editOption"
                style="width: 75%"
              >
                <el-option
                  v-for="item in options"
                  :key="item"
                  :label="item.label"
                  :value="item"
                />
              </el-select>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="多选题"
          name="2"
          :disabled="dialogType === '编辑标题'"
        >
          <div class="topicType2" :type="topicType">
            <div class="col1">
              <span class="tl"><span style="color: red">*</span> 题目</span>
              <el-input
                type="textarea"
                style="width: 75%"
                v-model.trim="TopicName"
                placeholder="请输入题干"
              ></el-input>
            </div>
            <div class="col2">
              <span class="tl"><span style="color: red">*</span> 选项</span>
              <div class="right">
                <div
                  class="option"
                  v-for="item in topicOption"
                  :key="item.lable"
                >
                  <span class="option-left">{{ item.lable }}</span
                  ><el-input
                    style="width: 75%"
                    v-model.trim="item.value"
                    placeholder="请输入选项内容"
                  ></el-input
                  ><i
                    @click="delTopic(item.lable)"
                    class="el-icon-delete"
                    v-if="editOption"
                  ></i>
                </div>
                <div class="addOption" v-if="editOption">
                  <span @click="addOption"> 添加选项 </span>
                </div>
              </div>
            </div>
            <div class="col1" v-if="mode !== 1">
              <span class="tl"><span style="color: red">*</span> 答案</span>
              <div>
                <el-checkbox-group v-model="checkList">
                  <el-checkbox
                    v-for="item in topicOption"
                    :key="item.lable"
                    :label="item.lable"
                  ></el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <div class="col1" v-if="mode !== 1">
              <span class="tl">答案解析</span>
              <el-input
                type="textarea"
                style="width: 75%"
                v-model.trim="TopicParse"
                placeholder="请输入解析"
              ></el-input>
            </div>
            <div class="col1" >
              <span class="tl">标签</span>
              <el-select
                v-model="value"
                multiple
                filterable
                allow-create
                remote
                reserve-keyword
                :disabled="!editOption"
                placeholder="请输入并选择标签"
                :remote-method="remoteMethod"
                :loading="loading"
                 @change="change1"
                style="width: 75%"
              >
                <el-option
                  v-for="item in options"
                  :key="item"
                  :label="item.label"
                  :value="item"
                />
              </el-select>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <span>
            <el-button @click="outTopicVisible">取消</el-button>
            <el-button
              @click="addTopicVisible()"
              :type="dialogType === '编辑标题' ? 'primary' : null"
              >{{ dialogType === "添加标题" ? "添加" : "确定" }}</el-button
            >
            <el-button
              v-show="dialogType === '添加标题'"
              type="primary"
              @click="addTopicVisible('go')"
              >添加并继续</el-button
            >
          </span>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
      <h3>确定删除这些内容吗？</h3>
      <el-tag
        class="delete-box"
        v-for="(item, index) in choseData"
        :key="index"
      >
        {{ item.title }}
      </el-tag>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="saveDelete" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog title="删除确认" v-model="deleteNoVisible" width="50%">
      <h3 style="margin-bottom: 20px; font-weight: 400; font-size: 14px">
        已绑定试卷的题目无法删除。若需删除题目，请先从试卷中解除题目绑定关系
      </h3>

      <el-table :data="gridData">
        <el-table-column property="questionTitle" label="题目" />
        <el-table-column property="paperTitle" label="绑定的试卷" />
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteNoVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="deleteNoVisible = false" size="mini"
            >好 的</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="Excel导入题目"
      v-model="ExcelVisible"
      width="50%"
      :before-close="outExcelDialog"
    >
      <div class="ExcelVisible">
        <div>
          1.下载模板，并按照模板中的使用说明填写题目。<span style="color: red"
            >请勿更改表头（添加选项除外），否则将识别失败。</span
          >
        </div>
        <a
          :href="
            mode == 1
              ? 'https://static.medsci.cn/doc/%E9%87%8F%E8%A1%A8%E9%A2%98%E5%BA%93%E9%A2%98%E7%9B%AE%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
              : 'https://static.medsci.cn/product/apo/xls/%E9%A2%98%E5%BA%93-%E9%A2%98%E7%9B%AE%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
          "
          >下载《题目导入模板》</a
        >
        <div class="li">
          <div>2.上传填写完成的Excel文件</div>
          <el-upload
            class="upload-demo"
            action="#"
            :show-file-list="false"
            :http-request="customRequest"
            :before-upload="beforeExcelUpload"
          >
            <el-button>选取并上传文件</el-button>
          </el-upload>
          <el-tag v-if="fileList">{{ fileList.file.name }} </el-tag>
          <div style="color: #cccccc">文件格式：xls ； xlsx</div>
        </div>
        <div class="btn">
          <el-button type="primary" @click="saveExcel">确定</el-button>
          <el-button @click="outExcelDialog">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MsFormSearch from "@/components/MsCommon/ms-form-search";
import {
  Question,
  editQuestionBank,
  addQuestion,
  deleteQuestion,
  detailQuestion,
  editQuestion,
  batchAddQuestionsByExcel,
  exportQuestions,
  questionBatchDelete,
  detailBankQuestion,
  medsciLabelList,
  importScaleQuestionsByExcel,
} from "@/request/service.js";
import { exportScaleQuestions } from "@/request/download.js";
// import { ElMessage } from "element-plus";
// import dayjs from "dayjs";
import { numToChar } from "@/utils/toQueryString.js";
import { Filter } from "@element-plus/icons";
import topicModule from "@/mixins/topicModule.js";
import { isRepeat } from "@/utils/validate.js";
import { ElMessage, ElNotification } from "element-plus";
export default {
  name: "detailPage",
  mixins: [topicModule],
  emits: ["goRecognition"],
  props: {
    edit: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    Filter,
    MsFormSearch,
  },
  data() {
    return {
      gridData: [],
      topic: "",
      currentPage: 1,
      pageSize: 100,
      total: 0,
      QuestionBankVisible: false,
      TopicVisible: false,
      // checkVisible: false,
      deleteVisible: false,
      deleteNoVisible: false, //判断返回的字段有没有值 有值变成true
      ExcelVisible: false,
      choseData: [],
      tableData: [],
      // currentChose: {},
      // obVisible: false,
      QuestionBankName: "",
      BankName: "",
      TopicBankId: "",
      TopicName: "",
      TopicId: "",
      topicType: "1",
      topicOption: [
        {
          lable: "A",
          value: "",
        },
        {
          lable: "B",
          value: "",
        },
        {
          lable: "C",
          value: "",
        },
        {
          lable: "D",
          value: "",
        },
      ],
      radio: "",
      checkList: [],
      TopicParse: "",
      dialogType: "添加标题",
      fileList: "",
      options: [],
      loading: false,
      editOption: "", //是否绑定试卷
      mode: "", //题目类型
      list: [],
      value: "",
      arr: [],
      oldList: [],
      oldLists: [],
    };
  },
  created() {
    this.QuestionBankName = this.edit.title;
    this.TopicBankId = this.edit.id;
  },
  async mounted() {
    await this.detail();
    setTimeout(async () => {
      await this.init();
    }, 100);
  },

  methods: {
    // 查询标签
    async tagList(query) {
      let params = {
        pageIndex: 1,
        pageSize: 500,
        name: query,
      };
      let res = await medsciLabelList(params);
      this.list = res.data.map((element) => {
        return {
          value: element.id,
          label: element.name,
        };
      });
    },
    change1(value) {
      const modifiedArray = value.map((element, index) => {
        if (element instanceof Object) {
          return element; // 如果元素是对象，则保持不变
        } else {
          return { label: value[index], value: "-1" }; // 如果元素不是对象，则返回修改后的对象
        }
      });
      this.oldList = modifiedArray;
      console.log(this.oldList)
    },
    async remoteMethod(query) {
      if (query) {
        await this.tagList(query);
        this.list.concat(this.oldList);
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.options = this.list.filter((item) => {
            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
          });
        }, 200);
      } else {
        this.options = [];
      }
    },
    a() {
    },
    async detail() {
      let params = {
        questionBankId: this.TopicBankId,
      };
      let res = await detailBankQuestion(params.questionBankId);
      this.mode = res.data.mode;
      this.editOption = res.data.editOption;
    },
    async init(type) {
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.topic,
        questionBankId: this.TopicBankId,
        mode: this.mode,
      };
      if (type) {
        params.type = type;
      }
      Question(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data;
      });
    },
    // reset() {
    //   this.title = "";
    //   this.name = "";
    //   this.lineTime = "";
    //   this.$refs.multipleTable.clearSelection();
    // },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 修改题库名称弹框
    editQuestionBank() {
      this.QuestionBankVisible = true;
      this.BankName = this.QuestionBankName;
    },
    // 添加题目弹框
    addTopic() {
      this.TopicVisible = true;
      this.value = [];
      this.editOption=true
      this.oldList=[]
      this.oldLists=[]
    },
    // 关闭添加题目弹框
    outTopicVisible() {
      this.dialogType = "添加标题";
      (this.TopicName = ""),
        (this.topicOption = [
          {
            lable: "A",
            value: "",
          },
          {
            lable: "B",
            value: "",
          },
          {
            lable: "C",
            value: "",
          },
          {
            lable: "D",
            value: "",
          },
        ]),
        (this.topicType = "1");
      (this.radio = ""),
        (this.checkList = []),
        (this.TopicParse = ""),
        (this.TopicVisible = false);
    },
    // 删除题目
    delTopic(type) {
      this.topicOption = this.topicOption.filter((item) => item.lable !== type);
      this.topicOption = this.topicOption.map((item, value) => {
        return {
          lable: numToChar(value + 1),
          value: item.value,
        };
      });
    },
    // 添加选项
    addOption() {
      if (this.topicOption.length >= 26)
        return this.$message.warning("选项最多只能有26项");
      this.topicOption.push({
        lable: numToChar(this.topicOption.length + 1),
        value: "",
      });
    },
    // 修改题库名称
    async editQuestionBankName() {
      let params = {
        id: this.TopicBankId,
        title: this.BankName,
      };
      let res = await editQuestionBank(params);
      if (res.status === 200) {
        this.$message.success("修改题库名称成功");
        this.QuestionBankName = this.BankName;
        this.QuestionBankVisible = false;
        this.BankName = "";
      }
    },
    // 编辑/添加题目确认按钮
    async addTopicVisible(type) {
      if (this.TopicName === "") {
        return this.$message.warning("题干不能为空");
      } else if (this.TopicName.length >= 10000) {
        return this.$message.warning("请输入1~9999个字符");
      }
      if (this.topicOption.some((item) => item.value === "")) {
        return this.$message.warning("选项不能为空");
      } else if (this.topicOption.some((item) => item.value.length >= 10000)) {
        return this.$message.warning("请输入1~9999个字符");
      }
      if (this.topicType === "1") {
        if (!this.radio && this.mode == 0) {
          return this.$message.warning("答案不能为空");
        }
        if (this.dialogType === "添加标题") {
          let value = this.topicOption.map((item) => {
            if (item.lable === this.radio) {
              return {
                content: item.value,
                isSelected: true,
                option: item.lable,
              };
            } else {
              return {
                content: item.value,
                isSelected: false,
                option: item.lable,
              };
            }
          });
          if (isRepeat(value)) {
            return this.$message.warning("选项内容不能重复");
          }
          let params;
          if (this.mode == 1) {
            params = {
              type: 1,
              title: this.TopicName,
              options: JSON.stringify(value),
              questionBankId: this.TopicBankId,
              answer: [this.radio],
              analysis: this.TopicParse,
              mode: this.mode,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          } else {

            params = {
              type: 1,
              title: this.TopicName,
              options: JSON.stringify(value),
              questionBankId: this.TopicBankId,
              answer: [this.radio],
              analysis: this.TopicParse,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          }

          let res = await addQuestion(params);
          if (res.status === 200) {
            this.$message.success("添加题目成功");
            this.value=[]
            this.oldList=[]
            this.init();
          } else {
            return;
          }
        } else {
          // 编辑单选
          let value = this.topicOption.map((item) => {
            if (item.lable === this.radio) {
              return {
                content: item.value,
                isSelected: true,
                option: item.lable,
              };
            } else {
              return {
                content: item.value,
                isSelected: false,
                option: item.lable,
              };
            }
          });
          if (isRepeat(value)) {
            return this.$message.warning("选项内容不能重复");
          }
          let params = {
            title: this.TopicName,
            options: JSON.stringify(value),
            id: this.TopicId,
            questionBankId: this.TopicBankId,
            answer: [this.radio],
            analysis: this.TopicParse,
            mode: this.mode,
            labelList: this.oldList.map((element) => {
              return {
                id: element.value,
                name: element.label,
              };
            }),
          };
          let res = await editQuestion(params);
          if (res.status === 200) {
            this.$message.success("编辑题目成功");
            this.init();
          } else {
            return;
          }
        }
      } else {
        if (this.checkList.length === 0 && this.mode == 0) {
          return this.$message.warning("答案不能为空");
        }
        if (this.dialogType === "添加标题") {
          let value = this.topicOption.map((item) => {
            if (this.checkList.some((index) => index === item.lable)) {
              return {
                content: item.value,
                isSelected: true,
                option: item.lable,
              };
            } else {
              return {
                content: item.value,
                isSelected: false,
                option: item.lable,
              };
            }
          });
          if (isRepeat(value)) {
            return this.$message.warning("选项内容不能重复");
          }

          let params;
          if (this.mode == 1) {
            params = {
              type: 2,
              title: this.TopicName,
              options: JSON.stringify(value),
              questionBankId: this.TopicBankId,
              answer: this.checkList,
              analysis: this.TopicParse,
              mode: this.mode,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          } else {
            params = {
              type: 2,
              title: this.TopicName,
              options: JSON.stringify(value),
              questionBankId: this.TopicBankId,
              answer: this.checkList,
              analysis: this.TopicParse,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          }
          let res = await addQuestion(params);
          if (res.status === 200) {
            this.$message.success("添加题目成功");
            this.init();
          } else {
            return;
          }
        } else {
          // 编辑多选
          let value = this.topicOption.map((item) => {
            if (this.checkList.some((index) => index === item.lable)) {
              return {
                content: item.value,
                isSelected: true,
                option: item.lable,
              };
            } else {
              return {
                content: item.value,
                isSelected: false,
                option: item.lable,
              };
            }
          });
          if (isRepeat(value)) {
            return this.$message.warning("选项内容不能重复");
          }
          let params 
          if (this.mode == 1) {
             params = {
              title: this.TopicName,
              options: JSON.stringify(value),
              id: this.TopicId,
              questionBankId: this.TopicBankId,
              answer: this.checkList,
              analysis: this.TopicParse,
              mode: this.mode,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          } else {
             params = {
              title: this.TopicName,
              options: JSON.stringify(value),
              id: this.TopicId,
              questionBankId: this.TopicBankId,
              answer: this.checkList,
              analysis: this.TopicParse,
              labelList: this.oldList.map((element) => {
                return {
                  id: element.value,
                  name: element.label,
                };
              }),
            };
          }
          console.log(this.value)

          let res = await editQuestion(params);
          if (res.status === 200) {
            this.$message.success("编辑题目成功");
            this.init();
          } else {
            return;
          }
        }
      }
      (this.TopicName = ""),
        (this.topicOption = [
          {
            lable: "A",
            value: "",
          },
          {
            lable: "B",
            value: "",
          },
          {
            lable: "C",
            value: "",
          },
          {
            lable: "D",
            value: "",
          },
        ]),
        (this.radio = ""),
        (this.checkList = []),
        (this.TopicParse = ""),
        (this.TopicId = "");
      this.value = [];
      if (!type) {
        this.TopicVisible = false;
        this.dialogType = "添加标题";
      }
    },
    // 编辑
    async editQuestion(id, labelList) {
      this.value = [];
      let res = await detailQuestion(id);
      if (res.status === 200) {
        this.dialogType = "编辑标题";

        this.TopicId = id;
        this.TopicName = res.data.title;
        if(res.data.labelList){
          this.oldLists = res.data.labelList.map((ele) => {
          return { value: ele.id, label: ele.name };
          });
          this.value = this.oldLists;
        }
        this.editOption = res.data.editOption
        this.topicOption = res.data.options.map((item) => {
          return {
            lable: item.option,
            value: item.content,
          };
        });
        this.options = this.oldLists;
        if (res.data.type === 1) {
          (this.topicType = "1"), (this.radio = res.data.answer[0]);
        } else {
          (this.topicType = "2"), (this.checkList = res.data.answer);
        }
        this.TopicParse = res.data.analysis;
        this.TopicVisible = true;
      }
    },
    // 删除题目
    async delQuestion(ids) {
      let params = {
        ids: [ids],
      };
      let res = await deleteQuestion(params);
      if (res.status === 200) {
        if (res.data.length > 0) {
          this.gridData = res.data;
          this.deleteNoVisible = true;
        } else {
          this.$message.success("删除题目成功");
          this.init();
        }
      }
    },
    // 批量删除题目
    async delBatch() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids,
      };
      let res = await questionBatchDelete(params);
      if (this.choseData.length == 0) {
        this.$message.warning("请至少选择一条数据！");
        return;
      }
      if (res.data.length > 0) {
        this.gridData = res.data;
        this.deleteNoVisible = true;
      } else {
        this.deleteVisible = true;
      }
    },
    async saveDelete() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids,
      };
      let res = await deleteQuestion(params);
      if (res.status == 200) {
        this.$message.success("批量删除题目成功");
        this.init();
        this.deleteVisible = false;
      }
    },
    change() {
      (this.TopicName = ""),
        (this.topicOption = [
          {
            lable: "A",
            value: "",
          },
          {
            lable: "B",
            value: "",
          },
          {
            lable: "C",
            value: "",
          },
          {
            lable: "D",
            value: "",
          },
        ]),
        (this.radio = ""),
        (this.TopicId = ""),
        (this.checkList = []),
        (this.TopicParse = "");
      this.value = [];
    },
    goTextRecognition() {
      this.$emit("goRecognition", {
        id: this.TopicBankId,
        title: this.QuestionBankName,
      });
    },
    // 导出
    async deriveData() {
      if (this.choseData.length == 0) {
        this.$message.warning("请至少选择一条数据！");
        return;
      }
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
        questionBankId: this.TopicBankId,
      };
      let res;
      if (this.mode == 1) {
        res = await exportScaleQuestions(params);
        let name = this.QuestionBankName + `.xlsx`;
        let link = document.createElement("a");
        // link.href = window.URL.createObjectURL(res.data);
        link.href = window.URL.createObjectURL(res);
        link.download = name;
        link.click();
      } else {
        res = await exportQuestions(params);
        if (res.status === 200) {
          let name = this.QuestionBankName + `.xlsx`;
          let link = document.createElement("a");
          // link.href = window.URL.createObjectURL(res.data);
          link.href = res.data;
          link.download = name;
          link.click();
        } else {
          this.$message.warning("暂无数据导出");
        }
      }
    },
    // Excel批量导入
    addExcelDialog() {
      this.ExcelVisible = true;
    },
    // Excel导入文件格式校验
    beforeExcelUpload(file) {
      const isJPG =
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      if (!isJPG) {
        this.$message.error("上传的文件只能是xls ； xlsx!");
        return false;
      } else {
        return true;
      }
    },
    customRequest(item) {
      this.fileList = item;
      // let _this = this
      // this.filetoDataUrl(item.file, function (img) {
      //   let params = {
      //     base64: img
      //   }
      //   uploadAvatar(params).then((res) => {
      //     if (res.status == 200) {
      //       _this.tableData.avatar = res.data.url
      //     }
      //   })
      // })
    },
    // 退出Excel导入题目弹框
    outExcelDialog() {
      this.fileList = "";
      this.ExcelVisible = false;
    },
    // Excel导入确定按钮
    async saveExcel() {
      
      let a = new FormData();

      let res;
      if (this.mode == 1) {
        if(!this.fileList.file){
           ElMessage.error(
                `上传文件不能为空`
              );
        return 
      }
        a.append("file", this.fileList.file);
        a.append("bankId", this.TopicBankId);
        res = await importScaleQuestionsByExcel(a);
      } else {
        a.append("file", this.fileList.file);
        a.append("gatherId", this.TopicBankId);
        res = await batchAddQuestionsByExcel(a);
      }
      if (this.mode == 1) {
        if (res.status == 200) {
          if (res.data) {
            let result = res.data.join(" | ");
            ElNotification({
              title: "错误提示(修改全部错误重新导入)",
              message: `${result}`,
              type: "error",
              position: "bottom-right",
              duration: 0,
            });
            this.fileList = "";
          } else {
            ElNotification({
              title: "提示",
              message: `导入成功`,
              type: "success",
              position: "bottom-right",
            });
            this.fileList = "";
            this.ExcelVisible=false
            this.init()
          }
        }
      } else {
        let queImportFailList = [];
        if (res.status == 200) {
          if (res.data.verfiyFail) {
            queImportFailList = res.data.queImportFailList;
            let temp = "";
            queImportFailList.forEach((val, index) => {
              if (queImportFailList.length - 1 == index) {
                temp += val.errorMsg;
              } else {
                temp += val.errorMsg + "|";
              }
            });
            if(res.data.repeatedCount){
              ElNotification({
                title: "错误提示",
                message: `成功${res.data.successCount}条，重复题目${res.data.repeatedCount}条，失败${res.data.failCount}条，原因为${temp}`,
                type: "error",
                position: "bottom-right",
              });
            }else{
              ElNotification({
                title: "错误提示",
                message: `成功${res.data.successCount}条，失败${res.data.failCount}条，原因为${temp}`,
                type: "error",
                position: "bottom-right",
              });
            }
            
          } else {
             if(res.data.repeatedCount){
              ElNotification({
                title: "提示",
                message: `成功${res.data.successCount}条，重复题目${res.data.repeatedCount}条`,
                type: "success",
                position: "bottom-right",
              });
            }else{
              ElNotification({
              title: "提示",
              message: `成功${res.data.successCount}条`,
              type: "success",
              position: "bottom-right",
            });
            }
          }
        }

        this.$message.success("导入文件成功");
        this.fileList = "";
        this.init();
        this.ExcelVisible = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
// ::v-deep{
//   .el-input{
//     height: 200px;
//     overflow: hidden;
//   }
// }
.header {
  background-color: #f0f2f5;
  margin-top: 20px;
  padding: 15px 5px 20px 10px;
  .title {
    display: flex;
    justify-content: space-between;
    .tl {
      font-size: 18px;
    }
    i {
      margin-right: 10px;
      font-size: 20px;
      cursor: pointer;
    }
  }
  .content {
    background-color: #fff;
    margin-top: 15px;
    padding: 5px;
    .col {
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      white-space: nowrap;
      display: flex;
      align-items: center;
      margin-top: 15px;
      .tl {
        margin-right: 20px;
        width: 100px;
        text-align-last: right;
      }
      div {
        width: 30%;
      }
    }
  }
}
.operate {
  .left{
    margin-right: 5px;
  }
  .right {
    width: 30%;
    ::v-deep {
      .el-input__suffix {
        margin-right: 5px;
        cursor: pointer;
      }
    }
  }
}

.dialog {
  // .col {
  //   display: flex;
  //   align-items: center;
  //   margin-right: 20px;
  // }
  .tl {
    width: 25%;
    opacity: 1;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    margin-left: 50px;
    white-space: nowrap;
  }
}
.dialog-footer {
  text-align: center;
}
.topicType1 {
  ::v-deep {
    .col1 {
      display: flex;
      align-items: center;
      margin-top: 30px;
      .tl {
        width: 70px;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        margin-left: 20px;
        white-space: nowrap;
        text-align: right;
      }
      textarea {
        height: 80px;
        //  &::placeholder{
        //    transform: translateY(50%);
        //  }
      }
    }
    .col2 {
      display: flex;
      margin-top: 15px;
      .tl {
        margin-top: 8px;
        width: 5%;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        margin-left: 50px;
        white-space: nowrap;
      }
      // textarea{
      //      &::placeholder{
      //        transform: translateY(50%);
      //      }
      //    }
      .right {
        width: 85%;
        .option {
          margin-bottom: 10px;
          span {
            display: inline-block;
            width: 11px;
            margin-right: 10px;
          }
          .el-input__inner::placeholder {
            font-size: 12px;
          }
          i {
            margin-left: 10px;
            font-size: 18px;
            cursor: pointer;
          }
        }
        div:nth-child(-n + 2) {
          i {
            display: none;
          }
        }
        .addOption {
          margin-top: 20px;
          span {
            color: #4db8e4;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.topicType2 {
  ::v-deep {
    .col1 {
      display: flex;
      align-items: center;
      margin-top: 30px;
      .tl {
        width: 70px;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        margin-left: 20px;
        white-space: nowrap;
        text-align: right;
      }
      textarea {
        height: 80px;
        //  &::placeholder{
        //    transform: translateY(50%);
        //  }
      }
    }
    .col2 {
      display: flex;
      margin-top: 15px;
      .tl {
        margin-top: 8px;
        width: 5%;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        margin-left: 50px;
        white-space: nowrap;
      }
      // textarea{
      //      &::placeholder{
      //        transform: translateY(50%);
      //      }
      //    }
      .right {
        width: 85%;
        .option {
          margin-bottom: 10px;
          span {
            display: inline-block;
            width: 11px;
            margin-right: 10px;
          }
          .el-input__inner::placeholder {
            font-size: 12px;
          }
          i {
            margin-left: 10px;
            font-size: 18px;
            cursor: pointer;
          }
        }
        div:nth-child(-n + 2) {
          i {
            display: none;
          }
        }
        .addOption {
          margin-top: 20px;
          span {
            color: #4db8e4;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.delete-box {
  margin-top: 10px;
  margin-right: 10px;
}
::v-deep {
  .el-table__header-wrapper {
    .el-checkbox__input::after {
      content: "全选";
      position: absolute;
      margin-left: 5px;
    }
    .el-table-column--selection .cell {
      margin-left: -20px;
      padding-left: 0px !important;
    }
    .el-dropdown {
      cursor: pointer;
    }
  }
  .el-table__body-wrapper .el-table__row {
    td:nth-child(2) {
      text-align: left;
    }
  }
}
.ExcelVisible {
  a {
    display: inline-block;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
  }
  .li {
    button {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
  .btn {
    text-align: right;
    margin-top: 20px;
  }
}
</style>
