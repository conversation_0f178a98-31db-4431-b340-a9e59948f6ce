<template>
  <div class="header">
    <div class="left">
      <div class="col">
        <el-input
          v-model="title"
          placeholder="请输入题库名称"
          @keydown.enter="init"
        ></el-input>
      </div>
      <div class="col">
        <el-date-picker
          v-model="lineTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="创建开始时间"
          end-placeholder="创建结束时间"
          :default-time="defaultTime"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="right">
      <el-button @click="init" icon="el-icon-search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
  </div>
  <div class="operate">
    <el-button
      type="primary"
      @click="bankVisible = true"
      v-if="btnList.add_question_bank"
      >新建题库</el-button
    >
  </div>

  <div class="table">
    <el-table
      ref="multipleTable"
      border
      :data="tableData"
      tooltip-effect="dark"
    >
      <el-table-column prop="id" label="题库ID" width="80" align="center">
      </el-table-column>
      <el-table-column prop="title" label="题库名称 " align="center">
      </el-table-column>
      <el-table-column prop="mode" label="题库属性" align="center">
        <template #default="scope">
          {{scope.row.mode?'量表类题库':'考试类题库'}}
        </template>
      </el-table-column>
      <el-table-column
        prop="questionCount"
        label="题目数量"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="singleChoiceQuestionCount"
        label="单选题目数量"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="multipleChoiceQuestionCount"
        label="多选题目数量"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column prop="createdName" label="创建人" align="center">
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" align="center">
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="editQuestionBank(scope.row)"
            v-if="btnList.bank_edit_btn"
            >编辑</el-button
          >
          <el-button
            size="mini"
            plain
            type="danger"
            :disabled="!scope.row.canBeDeleted"
            v-if="btnList.bank_remove_btn"
            @click="delQuestionBank(scope.row.id)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
  <!-- 新建题库弹框 -->
  <el-dialog
    title="新建题库"
    v-model="bankVisible"
    width="50%"
    :before-close="outBank"
  >
    <div class="dialog">
      <span class="tl"><span style="color: red">*</span> 题库名称</span>
      <el-input
        style="width: 75%"
        v-model.trim="bankName"
        placeholder="请输入64字以内的题库名称"
      ></el-input>
    </div>
    <div class="dialog">
      <span class="tl"><span style="color: red">*</span> 题库属性</span>
      <el-radio-group v-model="radio1" class="ml-4">
        <el-radio label="1" size="large">量表类题库</el-radio>
        <el-radio label="0" size="large">考试类题库</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <span>
          <el-button @click="outBank">取 消</el-button>
          <el-button type="primary" @click="goDetailPage()">确 定</el-button>
        </span>
      </div>
    </template>
  </el-dialog>

  <!-- <el-dialog title="批量删除" v-model="deleteVisible" width="70%">
    <h3>确定删除这些内容吗？</h3>
    <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
      {{ item.title }}
    </el-tag>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="saveDelete" size="mini"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog> -->
</template>

<script>
import {
  QuestionBank,
  addQuestionBank,
  deleteQuestionBank,
} from "@/request/service.js";
import topicModule from "@/mixins/topicModule.js";

// import { ElMessage } from "element-plus";
// import dayjs from "dayjs";
export default {
  name: "Topic",
  emits: ["goDetail"],
  mixins: [topicModule],
  data() {
    return {
      title: "",
      lineTime: "",
      bankName: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      bankVisible: false,
      // choseData: [],
      // currentChose: {},
      tableData: [],
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
      ],
      radio1:'1'
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        startTime: publishedStartTime,
        endTime: publishedEndTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
      };
      QuestionBank(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data;
      });
    },
    reset() {
      this.title = "";
      this.name = "";
      this.lineTime = "";
      // this.$refs.multipleTable.clearSelection();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 添加题库并跳转
    async goDetailPage() {
      let params = {
        title: this.bankName,
        mode:this.radio1
      };
      let res = await addQuestionBank(params);
      if (res.status === 200) {
        this.$message.success("添加题库成功");
        this.$emit("goDetail", res.data);
      }
    },
    // 删除题库
    async delQuestionBank(id) {
      let params = {
        id,
      };
      let res = await deleteQuestionBank(params);
      if (res.status === 200) {
        this.$message.success("删除题库成功");
        this.init();
      }
    },
    // 题库详情页
    editQuestionBank(data) {
      this.$emit("goDetail", data);
    },
    outBank() {
      this.bankName = "";
      this.bankVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .left {
    display: flex;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .tl {
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
      }
    }
  }
}

.dialog {
  margin-bottom: 20px;
  // .col {
  //   display: flex;
  //   align-items: center;
  //   margin-right: 20px;
  // }
  .tl {
    width: 25%;
    opacity: 1;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    margin-left: 50px;
    white-space: nowrap;
  }
}
.dialog-footer {
  text-align: center;
}
::v-deep {
  .el-table__body-wrapper .el-table__row {
    td:nth-child(2) {
      text-align: left;
      .cell {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
</style>
