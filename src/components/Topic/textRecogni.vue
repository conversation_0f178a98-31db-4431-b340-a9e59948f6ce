<template>
  <div>
    <el-input
      style="width:100%; height: 100%;"
      v-model="writeTxt"
      :rows="40"
      type="textarea"
      placeholder="将题目文本粘贴至此区域"
      @blur="ocr"
      @focus="resetDisable"
    />
    <!-- <div style="width:49%;display:inline-block;height:500px">
      <div style="margin-bottom: 20px" v-for="item in ocrTxt" :key="item">{{item}}</div>
    </div>  -->
    <!-- <el-button @click="ocr">识别</el-button> -->
  </div>
</template>
<script>

export default {
  data() {
    return {
      writeTxt: '',
      importArr: [],
      ocrTxt: [],
      sortRule: [],
      typeRule: ['【单选题】', '[单选题]','【单选】', '[单选]', '【多选题】', '[多选题]','【多选】', '[多选]'],
      answerRule: ['答案:','答案：', '【答案】', '[答案]'],
      analysisRule: ['解析:','解析：', '【解析】', '[解析]'],
      labelListRule: ['标签:','标签：', '【标签】', '[标签]'],
      optionsRule: ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.','K.','L.','M.','N.','O.','P.','Q.','R.','S.','T.','U.','V.','W.','X.','Y.','Z.','A、', 'B、', 'C、', 'D、', 'E、', 'F、', 'G、', 'H、', 'I、', 'J、', 'K、', 'L、', 'M、', 'N、', 'O、', 'P、', 'Q、', 'R、', 'S、', 'T、', 'U、', 'V、', 'W、', 'X、', 'Y、', 'Z、'],
      // eslint-disable-next-line no-control-regex
      regN: new RegExp( '\n' , "g" ), //去除空格
      // eslint-disable-next-line no-control-regex
      regR: new RegExp( '\r' , "g" ), //去除换行符
    };
  },
  mounted() {
    this.initSort()
// [{
// 	id: 1,
// 	sort: 1, //排序
// 	type: 'radio', //类型radio单选，checkbox多选
// 	title: '我是题目', //题目
// 	options: [{
// 		option: 'A',
// 		content: '缺氧'
// 	},{
// 		option: 'B',
// 		content: '毒害'
// 	}], //选项
// 	answer: ['A'], //单选也用数组，取第一位
// 	analysis: '我是解析' //非必填
// }]
  },
  methods: {
    initSort() {
      for(let i = 0; i < 100; i++) {
        // 解决数字点问题
        this.sortRule.push(`${i+1}、[`)
        this.sortRule.push(`${i+1}.[`)
        this.sortRule.push(`${i+1}、【`)
        this.sortRule.push(`${i+1}.【`)
      }
    },
    initArr() {
      this.importArr = []
      // 过滤换行符、回车符
      let data = this.writeTxt.replace( this.regN , '' ).replace( this.regR , '' )
      // 序号在字符串出现的下标顺序数组
      let acitveIndex = []
      this.sortRule.forEach(row=>{
        if(data.indexOf(row)>-1){
          acitveIndex.push(data.indexOf(row))
           // 处理重复下标2次的情况
           if(data.indexOf(row) != data.lastIndexOf(row)) {
            acitveIndex.push(data.lastIndexOf(row))
          }
        }
      })
      // acitveIndex需要数组排序从小到大，处理混乱排序情况
      let sortIndex = this.selectionSort(acitveIndex)
      sortIndex.forEach((row,i)=>{
        // 根据每个序号下标出现位置，去slice字符串，push到数组完成题目分割
        this.importArr.push(data.slice(sortIndex[i],(sortIndex[i+1])))
      })
    },
    selectionSort(arr) {
      var len = arr.length;
      var minIndex, temp;
      for (var i = 0; i < len - 1; i++) {
          minIndex = i;
          for (var j = i + 1; j < len; j++) {
              if (arr[j] < arr[minIndex]) {     // 寻找最小的数
                  minIndex = j;                 // 将最小数的索引保存
              }
          }
          temp = arr[i];
          arr[i] = arr[minIndex];
          arr[minIndex] = temp;
      }
      return arr;
    },
    ocr() {
      this.initArr()
      this.ocrTxt = []
      this.importArr.forEach((item,index)=>{
        // 序号取数组顺序
        let sort = index+1
        // 类型
        let type = null
        // 类型数组下标，后面字符串分割取题目用
        let typeIndex = null
        this.typeRule.forEach((row,i)=>{
          if(item.indexOf(row)>-1) {
            typeIndex = i
            if(i==0||i==1||i==2||i==3) {
              type = 1 //单选
            }else if(i==4||i==5||i==6||i==7){
              type = 2 //多选
            }
          }
        })
        // 上面循环优化前
        // if(item.indexOf(this.typeRule[0])>-1) {
        //   type = 'radio'
        //   typeIndex = 0
        // }else if (item.indexOf(this.typeRule[1])>-1) {
        //   type = 'radio'
        //   typeIndex = 1
        // }else if (item.indexOf(this.typeRule[2])>-1) {
        //   type = 'checkbox'
        //   typeIndex = 2
        // }else if (item.indexOf(this.typeRule[3])>-1) {
        //   type = 'checkbox'
        //   typeIndex = 3
        // }
        // 标题
        let title = null
        // 按数组字符串题型分割
        let titleArr = item.split(this.typeRule[typeIndex])
        // 按第一个选项A.再次分割，取出标题
        let titleIndex = 0
        
        if(titleArr[1].indexOf('A.')>-1){
          titleIndex = titleArr[1].indexOf('A.')
        }else if(titleArr[1].indexOf('A、')>-1){
          titleIndex = titleArr[1].indexOf('A、')
        }
        title = titleArr[1].slice(0, titleIndex)
        // 选项数组
        let options = []
        // 答案数组下标，后面字符串分割取题目用
        let answerIndex = null
        this.answerRule.forEach((row,i)=>{
          if(item.indexOf(row)>-1) {
            answerIndex = i
          }
        })
        let optionsArr = item.split(title)
        let optionsAll = optionsArr[1]?.split(this.answerRule[answerIndex])[0]
        // 和分割整题逻辑一样
        let acitveIndex = []
        this.optionsRule.forEach(row=>{
          if(optionsAll?.indexOf(row)>-1){
            acitveIndex.push(optionsAll.indexOf(row))
          }
        })
        // acitveIndex需要数组排序从小到大，处理、.混写情况
        let sortIndex = this.selectionSort(acitveIndex)
        sortIndex.forEach((row,i)=>{
          // 根据每个序号下标出现位置，去slice字符串，push到数组完成题目分割
          let item = optionsAll.slice(sortIndex[i],(sortIndex[i+1]))
          let json = {}
          if(item.indexOf('.')>-1) {
            json = {
              option: item.split('.')[0],
              content: item.split('.')[1],
              // content: item.slice(item.indexOf('.')+1,item.length),
              isSelected: false
            }
          }else if(item.indexOf('、')>-1) {
            json = {
              option: item.split('、')[0],
              content: item.split('、')[1],
              // content: item.slice(item.indexOf('、')+1,item.length),
              isSelected: false
            }
          }
          options.push(json)
        })
        // 答案
        let answer = null
        // 解析
        let analysis = null
        // 标签
        let labelList = null
        let labelLisIndex = null
        let answerArr = item.split(this.answerRule[answerIndex])[1]

        this.labelListRule.forEach((row,i)=>{
          if(item.indexOf(row)>-1) {
            labelLisIndex = i
          }
        })
        let answerTxt = null

        // 解析数组下标，后面字符串分割取题目用
        let analysisIndex = null
        this.analysisRule.forEach((row,i)=>{
          if(item.indexOf(row)>-1) {
            analysisIndex = i
          }
        })
        if(labelLisIndex!=null&&analysisIndex!=null){
            answerTxt = answerArr.split(this.labelListRule[labelLisIndex])[0].split(this.analysisRule[analysisIndex])[0]
            if(item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0]){
              if(item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0].indexOf(";")>-1){
              labelList=item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0].split(";").map((res)=>{
              return res })
              }
              else if(item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0].indexOf("；")>-1){
                labelList=item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0].split("；").map((res)=>{
                return res })
              }else{
                labelList=[item.split(this.labelListRule[labelLisIndex])[1].split(this.analysisRule[analysisIndex])[0]]
              }
            }
            
            analysis = answerArr.split(this.analysisRule[analysisIndex])[1]

        }
        if(labelLisIndex!=null&&analysisIndex==null) {
            answerTxt = answerArr.split(this.labelListRule[labelLisIndex])[0]
            if(item.split(this.labelListRule[labelLisIndex])[1]){
              if(item.split(this.labelListRule[labelLisIndex])[1].indexOf(';')>-1){
              labelList=item.split(this.labelListRule[labelLisIndex])[1].split(";").map((res)=>{
                return res
              })
              }else if(item.split(this.labelListRule[labelLisIndex])[1].indexOf('；')>-1){
                labelList=item.split(this.labelListRule[labelLisIndex])[1].split("；").map((res)=>{
                  return res
                })
              }else{
                 labelList=[item.split(this.labelListRule[labelLisIndex])[1]]
              }
            }
        } 
        if(labelLisIndex==null&&analysisIndex!=null) {
          answerTxt = answerArr.split(this.analysisRule[analysisIndex])[0]
          analysis = answerArr.split(this.analysisRule[analysisIndex])[1]
        }
        if(analysisIndex==null&&labelLisIndex==null) {
          answerTxt = answerArr
        } 
        answer = answerTxt.split('')
        // 处理isSelected
        options.forEach(row=>{
          answer.forEach(j=>{
            if(row.option == j) {
              row.isSelected = true
            }
          })
        })
        this.ocrTxt.push({
          sort: sort,
          type: type,
          title: title,
          options: options,
          answer: answer,
          analysis: analysis,
          labelList:labelList
        })
      })
      this.$parent.setOcrTxt(this.ocrTxt)
    },
    resetDisable(){
      this.$parent.resetDisable()
    },
    reset(){
      this.writeTxt =''
    }
  }
}
</script>
<style lang="scss" scoped>
.content-box {
  .content {
    position: relative;
    .content-footer {
      margin-top: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      span {
        color: #7f7f7f;
        cursor: pointer;
      }
    }
  }
}
#qiankun_container {
  height: 0%;
}
::v-deep{
  .el-textarea__inner{
    height: 100%;
  }
}
</style>
