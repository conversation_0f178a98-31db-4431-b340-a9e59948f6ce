<template>
  <div>
   <div class="header">
    <div class="title">
        <span class="tl">题库信息</span>
    </div>
    <div class="content">
      <div class="col">
        <span class="tl">题库ID</span>
        <span>{{TopicBankId}}</span>
      </div>
      <div class="col">
        <span class="tl">题库名称</span>
        <el-input disabled   v-model="QuestionBankName" ></el-input>
      </div>
    </div>
  </div>
  <div class="operate">
    <div class="left">
     <div class="left-header">
        将题目文本粘贴到下方
        <div>
          <span @click="openRule">
             填写规则与示例
          </span>
        </div>
     </div>
     <text-recogni 
     class="left-content"
     ref="textRecogni"
     ></text-recogni>
     <!-- <div class="left-content">
       将题目文本粘贴到下方
     </div> -->
    </div>
    <div class="right">
     <div class="right-header">
      题目检查确认
      <div>
        已识别 : {{ ocrTxt.length}}题
      </div>
      <!-- <el-button @click="goWrong">查询</el-button> -->
     </div>
     <div class="right-content">
      <div class="topic" v-for="item in ocrTxt" :key="item.sort">
        <div class="topic-title" :class="item.topicTitle?'danger':''">
        {{item.sort}}.<span v-if="item.type">{{item.type===1?'单选题':'多选题'}}</span> {{item.title}}
        </div>
        <div class="topic-option" :class="item.topicOption?'danger':''">
          <div v-for="select in item.options" :key="select.sort">
             {{select.option}}.{{select.content}}
          </div>
        </div>
        <div class="topic-answer" :class="item.topicAnswer?'danger':''">
          <div>答案</div> {{item.answer.join('、')}}
        </div>
        <div class="topic-analysis" v-if="item.labelList&&item.labelList.length>0">
          <div>标签</div> {{item.labelList.join(";")}}
        </div>
         <div class="topic-analysis" v-if="item.analysis">
          <div>解析</div> {{item.analysis}}
        </div>
      </div>
     </div>
    </div>
  </div>
  <div class="footer">
    <el-button style="width: 100px;" @click="goDetail">取消</el-button>
    <el-button style="width: 100px;" type="primary" :disabled="ocrTxt.length===0" @click="addOcrTxt">添加题目</el-button>
  </div>
  
  <el-dialog  title="填写说明" v-model="ruleVisible" width="50%">
      <el-tabs class="ruleContent" v-model="topicType">
        <el-tab-pane  label="基本规则" name="0">
           <div class="topicType0" :type="topicType">
              <div class="col">
                  <div class="title">
                    题干识别：按照编号识别
                  </div>
                  <div class="example">
                    <div>
                      支持的编号样式：<span style="color: #409eff;">1、1. </span>
                    </div>
                    <div class="example-bottom">
                        举例：1.【单选题】中华民族有多少个民族
                    </div>
                  </div>
              </div>
              <div class="col">
                  <div class="title">
                    题型识别：按照题干标注识别
                  </div>
                  <div class="example">
                    <div>
                      支持的标注样式：<span style="color: #409eff;">【单选题】[单选题]【多选题】[多选题]【单选】[单选]【多选】[多选]</span>
                    </div>
                    <div class="example-bottom">
                        举例：1. [多选题]计算机有哪些输出设备
                    </div>
                  </div>
              </div>
              <div class="col">
                  <div class="title">
                    答案识别：必填，按照标注识别
                  </div>
                  <div class="example">
                    <div>
                      支持的标注样式：<span style="color: #409eff;">答案： 【答案】[答案]</span>
                    </div>
                    <div class="example-bottom">
                        举例：答案：A
                    </div>
                  </div>
              </div>
              <div class="col">
                  <div class="title">
                    标签识别：选填，按照标注识别
                  </div>
                  <div class="example">
                    <div>
                      支持的标注样式：<span style="color: #409eff;">标签： 【标签】[标签]</span>
                    </div>
                    <div class="example-bottom">
                        举例：标签：疲劳症状
                    </div>
                  </div>
              </div>
              <div class="col">
                  <div class="title">
                    解析识别：选填，按照标注识别
                  </div>
                  <div class="example">
                    <div>
                      支持的标注样式：<span style="color: #409eff;">解析：[解析]【解析】</span>
                    </div>
                    <div class="example-bottom">
                        举例：【解析】中华民族有56个民族，详情见教材X页
                    </div>
                  </div>
                  <div class="example-footer"> 
                     若识别有异常，则可在左侧输入区域修改，修改后会重新识别
                  </div>
              </div>
           </div>
        </el-tab-pane>
        <el-tab-pane  label="单选题" name="1">
           <div class="topicType1" :type="topicType">
             <div class="example">
               <div class="example-left">
                <div class="example-title">
                   填写示例
                </div>
                <div class="example-content">
                   <div>1、【单选题】火灾中,烟气对人的危害特性有</div>
                   <div>A.缺氧</div>
                   <div>B.毒害</div>
                   <div>C.尘害</div>
                   <div>D.高温</div>
                   <div>【答案】A</div>
                   <div>【标签】标签1;标签2</div>
                   <div>【解析】详情见教材第X页</div>
                </div>
                </div>
               <div class="example-center">
               </div>
               <div class="example-right">
                <div class="example-title">
                   题目识别
                </div>
                <div class="example-content">
                   <div class="topic">
                      <div class="topic-title">
                      1.<span>单选题</span> 火灾中,烟气对人的危害特性有
                      </div>
                      <div class="topic-option">
                        <div>A.缺氧</div>
                        <div>B.毒害</div>
                        <div>C.尘害</div>
                        <div>D.高温</div>
                      </div>
                      <div class="topic-answer">
                        <div>答案</div> A
                      </div>
                      <div class="topic-analysis">
                        <div>标签</div> 标签1;标签2
                      </div>
                      <div class="topic-analysis">
                        <div>解析</div> 详情见教材第X页
                      </div>
                   </div>
                </div>
               </div>
             </div>
             <div class="col">
               <div>题型识别：按照题干标注识别</div>
               <div> 选项识别：必须以字母A~Z为编号，支持<span style="color: #409eff;">A. A、 </span></div>
               <div> 答案识别：必填，支持识别<span style="color: #409eff;">答案： 【答案】[答案]</span></div>
               <div> 解析识别：选填，支持识别<span style="color: #409eff;">解析：[解析]【解析】</span></div>
             </div>
           </div>
        </el-tab-pane>
        <el-tab-pane  label="多选题" name="2">
           <div class="topicType1" :type="topicType">
             <div class="example">
               <div class="example-left">
                <div class="example-title">
                   填写示例
                </div>
                <div class="example-content">
                   <div>2、【多选题】下列( )灭火剂是扑救精密仪器火灾的最佳选择。</div>
                   <div>A.二氧化碳</div>
                   <div>B.干粉</div>
                   <div>C.泡沫</div>
                   <div>【答案】ABC</div>
                </div>
                </div>
               <div class="example-center">
               </div>
               <div class="example-right">
                <div class="example-title">
                   题目识别
                </div>
                <div class="example-content">
                   <div class="topic">
                      <div class="topic-title">
                      2.<span>多选题</span> 下列( )灭火剂是扑救精密仪器火灾的最佳选择。
                      </div>
                      <div class="topic-option">
                        <div>A.二氧化碳</div>
                        <div>B.干粉</div>
                        <div>C.泡沫</div>
                      </div>
                      <div class="topic-answer">
                        <div>答案</div> A、B、C
                      </div>
                   </div>
                </div>
               </div>
             </div>
             <div class="col">
               <div>题型识别：按照题干标注识别</div>
               <div> 选项识别：必须以字母A~Z为编号，支持<span style="color: #409eff;">A. A、 </span></div>
               <div> 答案识别：必填，支持识别<span style="color: #409eff;">答案： 【答案】[答案]</span></div>
               <div> 解析识别：选填，支持识别<span style="color: #409eff;">解析：[解析]【解析】</span></div>
             </div>
           </div>
        </el-tab-pane>
     </el-tabs>

    <template #footer>
      <div class="ruleVisible">
        <el-button @click="ruleVisible = false" type="primary" >关 闭</el-button>
      </div>
    </template>
  </el-dialog>

 </div>
</template>

<script>
import { batchAddQuestionsByText } from "@/request/service.js";
// import { exportUserCase } from "@/request/download.js";
// import { ElMessage } from "element-plus";
// import initModule from "@/mixins/initModule.js";
// import initScroll from "@/mixins/initScroll.js";
// import  controlDialog  from '../controlDialog.vue'
// import  classifyDialog  from '../classifyDialog.vue'
import textRecogni from "@/components/Topic/textRecogni";
import { validOptions , isRepeat } from "@/utils/validate.js"
import { charToNum } from "@/utils/toQueryString.js"
export default {
  name: "testPaper",
  emits:['goDetailPage'],
  // mixins: [initModule, initScroll],
  components:{
    textRecogni
  },
  props: {
    edit: {
      type: Object,
      default: ()=>{},
    },
  },
  data() {
    return {
      QuestionBankName:'',
      TopicBankId:'',
      ocrTxt:[],
      disable:true,
      ruleVisible:false,
      topicType:'0',
    };
  },
  created(){
    this.QuestionBankName = this.edit.title
    this.TopicBankId = this.edit.id
  },
  mounted() {
  
  },
  methods: {
   
   setOcrTxt(data){
     this.ocrTxt = data
     if(this.ocrTxt.some((item,index)=>{  
       if(item.type!==1&&item.type!==2){
        this.ocrTxt[index].topicTitle = true
        return true
       }else{
        return false 
       }
      })){
      return this.$message.warning("题干中的题型标注无法智能识别");
     }

     if(this.ocrTxt.some((item,index)=>{
      if(item.options.length<2||!validOptions(item.options)||isRepeat(item.options)||item.options.some(ele=>ele.content.trim()==='')){
        this.ocrTxt[index].topicOption = true
        return true
      }else{
        return false
      }
     })){
      return this.$message.warning("至少2个选项；选项编号需从A开始且连续不重复；选项内容需不为空且不重复");
     }

     if(this.ocrTxt.some((item,index)=>{
      if(item.answer.length===0||
      (item.type===1&&item.answer.length>1)||
      (item.type===2&&item.answer.length<=1||
      item.options.length<item.answer.length)||
      item.answer.some(element =>charToNum(element)>item.options.length)){
        this.ocrTxt[index].topicAnswer = true
        return true
      }else{
        return false
      }
     })){
      return this.$message.warning("答案不能为空, 且答案必须在选项编号范围内 , 并符合题型要求");
     }

     this.disable=false
   },
  //  重置添加按钮状态
   resetDisable(){
    this.disable=true
   },
  //  添加按钮
   async addOcrTxt(){
      if(this.disable){
        return this.$message.warning("当前录入识别出有错误，请先解决错误再保存");
      }else{
        const newArray = this.ocrTxt.map(item => {
          return {
            ...item,
            options: JSON.stringify(item.options)
          };
        });
        let params = {
          gatherId:this.TopicBankId,
          questionsList:newArray
        }
        let res = await batchAddQuestionsByText(params)
         if(res.status===200){
            if(res.data.verfiyFail){
              this.ocrTxt = []
              return this.$message.warning(res.data.queImportFailList[0].errorMsg);
            }else{
              this.$message.success("导入成功");
              this.ocrTxt = []
              this.$refs.textRecogni.reset()
              this.$emit('goDetailPage',{id:this.TopicBankId,title:this.QuestionBankName})
            }
         }else{
           this.ocrTxt = []
         }
      }
   },
   goDetail(){
      this.$emit('goDetailPage',{id:this.TopicBankId,title:this.QuestionBankName})
      
    },
   openRule(){
     this.topicType='0',
     this.ruleVisible = true
   },
  //  goWrong(){
  //   this.$nextTick(()=>{
  //     this.$refs.tk.scrollIntoView(true)
  //   })
  //  }
  },
};
</script>

<style scoped lang="scss">
.header {
  background-color: #f0f2f5;
  margin-top: 20px;
  padding: 15px 5px 20px 10px;
  .title {
    display: flex;
    justify-content: space-between;
    .tl{
      font-size: 18px;
    }
    i{
      margin-right: 10px;
      font-size: 20px;
    }
  }
  .content{
    background-color: #fff;
    margin-top: 15px;
    padding: 5px;
    .col{
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      white-space: nowrap;
      display: flex;
      align-items: center;
      margin-top: 15px;
      .tl{
        margin-right: 20px;
        width: 100px;
        text-align-last: right;
      }
      div{
        width: 30%;
      }
    }
  }
}
.operate{
  box-sizing: border-box;
  display: flex;
  margin-top: 10px;
  width: 100%;
  height: 600px;
  border: 1px solid #f6f6f6;
  .left{
    width: 50%;
    height: 600px;
    border-right: 2px dashed #d7d7d7;
    display: flex;
    flex: 1;
    flex-direction: column;

    .left-header{
      padding: 20px;
      flex: 1;
      background-color: #f2f2f2;
      div{
        margin-top: 25px;
        span{
          cursor:pointer;
          color: #6cbde0;
          font-size: 15px;
        }
      }
    }
    .left-content{
      overflow: hidden;
      // overflow-y:scroll;
      flex: 6;
    }
  }
  .right{
    width: 50%;
    height: 600px;
    display: flex;
    flex: 1;
    flex-direction: column;
    .right-header{
      padding: 20px;
      flex: 1;
      background-color: #f2f2f2;
      div{
        margin-top: 20px;
        font-size: 15px;
      }
    }
    .right-content{
      overflow-y:scroll;
      flex: 6;
      .topic{
        margin-top: 10px;
        .danger{
         border: 1px solid #f00;
        }
        .topic-title{
          span{
           display: inline-block;
           width: 55px;
           line-height: 22px;
           text-align: center;
           color: #1b91ff;
           background-color: #e6f7ff;
           border: 1px solid #77bfff;
           border-radius: 3px;
           font-size: 15px;
          }
        }
        .topic-option{
           margin-left: 20px;
           padding-top: 5px;
           display: flex;
           flex-direction: column;
           div{
            margin-top: 5px;
            margin-bottom: 5px;
           }
        }
        .topic-answer{
          margin-left: 20px;
          div{
            width: 50px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            display:inline-block ;
            border-radius: 3px;
            color: #8d8d8d;
            background-color: #e7e7e7;
          }
        }
        .topic-analysis{
          margin-left: 20px;
          margin-top: 10px;
          div{
            width: 50px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            display:inline-block ;
            border-radius: 3px;
            color: #8d8d8d;
            background-color: #e7e7e7;
          }
        }

      }
    }
  }
}
.footer{
  margin-top: 10px;
  text-align: right;
}
.ruleVisible{
  text-align: center;
}
.ruleContent{
  .topicType0{
     padding: 20px;
     .col{
      margin-bottom: 15px;
       .title{
        margin-bottom: 15px;
       }
       .example{
        margin-left: 70px;
        .example-bottom{
          margin-top: 10px;
          color: #999;
        }
       }
       .example-footer{
         margin-top: 15px;
       }
     }
  }
  .topicType1{
    padding: 20px  40px;
    .example{
      display: flex;
      .example-left{
        height: 260px;
        flex: 30;
        display: flex;
        flex-direction: column;
        border: 2px solid #f2f2f2;
        .example-title{
          padding-left: 20px;
          height: 40px;
          line-height: 40px;
          background-color: #f2f2f2;
        }
        .example-content{
          margin-top: 20px;
          div{
            margin-bottom: 5px;
          }
        }
      }
      .example-center{
        margin-left: 15px;
        height: 0px;
        flex: 1;
        border-radius: 4px;
        border: 25px solid #d7d7d7;
        border-right: 0px solid #fff;
        border-top: 20px solid #fff;
        border-bottom: 20px solid #fff;
      }
      .example-right{
        height: 260px;
        flex: 30;
        display: flex;
        flex-direction: column;
        border: 2px solid #f2f2f2;
        .example-title{
          padding-left: 20px;
          height: 40px;
          line-height: 40px;
          background-color: #f2f2f2;
        }
        .example-content{
          .topic{
              margin-top: 10px;
              .danger{
              border: 1px solid #f00;
              }
              .topic-title{
                span{
                display: inline-block;
                width: 45px;
                line-height: 18px;
                text-align: center;
                color: #1b91ff;
                background-color: #e6f7ff;
                border: 1px solid #77bfff;
                border-radius: 3px;
                font-size: 14px;
                }
              }
              .topic-option{
                margin-left: 20px;
                padding-top: 5px;
                display: flex;
                flex-direction: column;
                div{
                  margin-top: 0px;
                  margin-bottom: 5px;
                }
              }
              .topic-answer{
                margin-left: 20px;
                div{
                  width: 50px;
                  height: 22px;
                  text-align: center;
                  line-height: 22px;
                  display:inline-block ;
                  border-radius: 3px;
                  color: #8d8d8d;
                  background-color: #e7e7e7;
                }
              }
              .topic-analysis{
                margin-left: 20px;
                margin-top: 5px;
                div{
                  width: 50px;
                  height: 22px;
                  text-align: center;
                  line-height: 22px;
                  display:inline-block ;
                  border-radius: 3px;
                  color: #8d8d8d;
                  background-color: #e7e7e7;
                }
              }
            }
          }
      }
    }
    .col{
      div{
         margin-top: 25px;
      }
    }

  }
}
</style>
