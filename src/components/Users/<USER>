<template>
  <div class="yxd_users-create">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="col">
        <span class="tl"> <span style="color: red">*</span> 真实姓名</span>
        <el-input
          v-model="tableData.realName"
          placeholder="请输入内容"
        ></el-input>
      </div>
      <div class="col">
        <span class="tl"> <span style="color: red">*</span> 用户名</span>
        <el-input
          v-model="tableData.userName"
          placeholder="请输入内容"
        ></el-input>
      </div>
      <div class="col">
        <span class="tl"><span style="color: red">*</span> 手机号码</span>
        <el-input
          v-model="tableData.mobile"
          placeholder="请输入11位手机号码"
          :disabled="type !== 'add'"
        ></el-input>
      </div>
      <div v-for="(item, index) in custom" :key="index">
        <!-- 字符串 -->
        <div v-show="item.fieldType===1" class="col custom">
           <span class="tl">{{item.fieldDisplayName}}</span>
            <el-input
              v-model.trim="item.fieldValue"
              :placeholder="'请输入'+item.fieldName"
            ></el-input>
        </div>
        <!-- 数字 -->
        <div v-show="item.fieldType===2" class="col custom">
           <span class="tl">{{item.fieldDisplayName}}</span>
            <el-input
              v-model.trim="item.fieldValue"
              onkeydown="this.value=this.value.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3')"
              onkeyup="this.value=this.value.replace(/[^\d.]/g, '')"
              :placeholder="'请输入'+item.fieldName"
            ></el-input>
        </div>
        <!-- 整数 -->
        <div v-show="item.fieldType===5" class="col custom">
           <span class="tl">{{item.fieldDisplayName}}</span>
            <el-input
              v-model.trim="item.fieldValue"
              onkeyup="this.value=this.value.replace(/\D+/, '')"
              :placeholder="'请输入'+item.fieldName"
            ></el-input>
        </div>
        <!-- 日期 -->
        <div v-show="item.fieldType===3" class="col custom">
           <span class="tl">{{item.fieldDisplayName}}</span>
            <el-date-picker
              v-model="item.fieldValue"
              type="date"
              value-format="YYYY-MM-DD"
              :placeholder=item.fieldName>
            </el-date-picker>
        </div>
        <!-- 时间 -->
        <div v-show="item.fieldType===4" class="col custom">
           <span class="tl">{{item.fieldDisplayName}}</span>
            <el-time-select
              v-model="item.fieldValue"
              :picker-options="{
                start: '00:00',
                step: '01:00',
                end: '24:00'
              }"
              :placeholder="'选择'+item.fieldName">
            </el-time-select>
        </div>
        <!-- 单选 -->
        <!-- <div v-show="item.fieldType===6 || item.fieldType===7" class="custom">
          <span class="tl">{{item.fieldDisplayName}}</span>
            <el-input
              v-model.trim="item.fieldValue"
              :placeholder="'请输入'+item.fieldName"
            ></el-input>
        </div> -->
        <!-- 附件上传 -->
        <div v-show="item.fieldType===8" class="col custom">
          <span class="tl">{{item.fieldDisplayName}}</span>
          <el-upload
            class="file-uploader"
            action="#"
            :show-file-list="true"
            :http-request="(option) => handleFileUpload(option, item)"
            :before-upload="(file) => beforeFileUpload(file, item)"
            :on-remove="() => handleFileRemove(item)"
          >
            <el-button size="small" type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">支持图片、文档、视频等常见格式，文件大小不超过 20MB</div>
            </template>
          </el-upload>
        </div>
      </div>
      <div class="col">
        <span class="tl">上传头像</span>
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="customRequest"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="tableData.avatar" :src="tableData.avatar" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </div>
      <div class="col sp" v-show="listData.length>0">
        <span class="tl">用户组权限</span>
        <!-- 树状图 -->
        <ul class="checkbox">
          <el-tree
            ref="tree"
            :data="listData"
            show-checkbox
            node-key="id"
            :default-expand-all="true"
            :check-strictly="true"
            :default-checked-keys="addCheckList"
            @check-change="handleCheckChange"
            :props="{
            value: 'id',
            label:'title',
            children:'childRoles' 
            }">
          </el-tree>
        </ul>
      </div>
      <div class="btn">
        <el-button @click="save" type="primary">{{ btnText }}</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import {
  query,
  addUser,
  updateUser,
  uploadAvatar,
  // listUserDefinedFieldByFieldName,
  getDefinedField,
  editDefinedField
} from '@/request/service.js'
import initScroll from '@/mixins/initScroll.js'
import { ElMessage } from 'element-plus'
import { validPhone } from '@/utils/validate.js'
export default {
  name: 'users-create',
  mixins: [initScroll],
  props: {
    tableList: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      listData: [],
      addCheckList: [],
      custom:{},
      tableData:{}
    }
  },
   created(){
     if(this.type === 'add'){
       this.tableData = {
         realName:'',
         userName:'',
         mobile:''
       }
     }else{
     let params = {
        accountType:0,
        id:this.tableList.id
      }
      editDefinedField(params).then((res)=>{
        if(res.status===200){
        this.tableData=res.data
        this.custom=res.data.extField
        this.addCheckList = res.data.userGroupList
        }else{
          // 拦截
          this.goBack()
          ElMessage.error('用户信息加载失败')
        }
      })
     }
   },
  mounted() {
    this.init()
    this.getCustom()
  },
  computed: {
    btnText() {
      if (this.type == 'add') {
        return '添加'
      } else {
        return '修改'
      }
    }
  },
  methods: {
    init() {
      let params = {
        accountType:0,
        title: ''
      }
      query(params).then((res) => {
        if (res.status == 200) {
          if (res.data.length > 0) {
            this.initScroll()
            this.listData=res.data
            if (this.type == 'edit') {
              // this.initGroup()
            }
          }
        }else{
           // 拦截
        }
      })
    },
    // initGroup() {
    //   let params = {
    //     // mobile: '',
    //     projectId: this.$store.state.projectInfo.id,
    //     userId: this.tableData.userId
    //   }
    //   getDetail(params).then((res) => {
    //     if (res.status == 200) {
    //       this.addCheckList = res.data.userGroupList
    //       // this.listData.forEach((val) => {
    //       //   this.addCheckList.forEach((d) => {
    //       //     if (val.id == d) {
    //       //       val.checked = true
    //       //     }
    //       //   })
    //       // })
    //     }else{
    //        // 拦截
    //         this.goBack()
    //         ElMessage.error('用户信息加载失败')
    //     }
    //   })
    // },
     getCustom() {
       if(this.type === 'add'){
         let params = {
           accountType:0,
           fieldName: '',
           projectId: this.$store.state.projectInfo.id
         }
         getDefinedField(params).then((res)=>{
           this.custom=res.data
         })
       }
      // listUserDefinedFieldByFieldName(params).then((res) => {
      //   this.custom = res.data.map((d) => {
      //     return {
      //       ...d,
      //       disabled: [
      //         '序号',
      //         '真实姓名',
      //         '昵称',
      //         '手机号码',
      //         '用户组'
      //       ].includes(d.fieldDisplayName)
      //     }
      //   })
      //   this.custom = this.custom.filter((item,index)=>index>4)
      //   if(this.type == 'add'){
      //       this.custom.forEach(item=> {  
      //       this.tableData[item.fieldName] = ''
      //       // this.$set(this.tableData,item.fieldName,'')
      //   })
      //   }
      // })
    },
    save() {
      if (!this.tableData.realName) {
        ElMessage.warning('真实姓名不可为空！')
        return
      } else if (!this.tableData.userName) {
        ElMessage.warning('用户名不可为空！')
        return
      } else if (!this.tableData.mobile) {
        ElMessage.warning('手机号码不可为空！')
        return
      } else if (!validPhone(this.tableData.mobile)) {
        ElMessage.warning('请输入正确的11位手机号码！')
        return
      }
       for(let key in this.custom){
         let value = this.custom[key].fieldValue||'';
         let type = this.custom[key].fieldType;
         let name = this.custom[key].fieldDisplayName;
         if(type===1){
            //  if(!value){
            //  ElMessage.warning(name+'不可为空！')
            //  return
            //  }else 
             if(value?.length>32){
             ElMessage.warning(name+'长度不能超过32个字符')
             return
             }
         }else if(type===2){
          //  数组
          //  if(!value){
          //    ElMessage.warning(name+'不可为空！')
          //    return
          //    }else
            if(value>10000||Number(value)+"" === "NaN"){
             ElMessage.warning(name+'最大不能超过10000')
             return
             }
         }else if(type===5){
          //  整数
          // if(!value){
          //    ElMessage.warning(name+'不可为空！')
          //    return
          //    }else
           if(value?.length>14||Number(value)+"" === "NaN"){
             ElMessage.warning(name+'应为长度不能超过14位数的纯数字')
             return
             }
         }
        //  else if(type===4){
        //   // 日期
        //   if(!value){
        //      ElMessage.warning(name+'不可为空！')
        //      return
        //      }
        //  }else if(type===5){
        //   // 时间
        //   if(!value){
        //      ElMessage.warning(name+'不可为空！')
        //      return
        //      }
        //  }
       }
      let value=this.custom.map(item=>{
        return   {
              fieldName:item.fieldName,
              fieldValue:item.fieldValue,
              fieldId:''+item.id+'',
              fieldType:item.fieldType
           }  
      })
      let value2=this.custom.map(item=>{
        return   {
              fieldName:item.fieldName,
              fieldValue:item.fieldValue,
              fieldId:item.fieldId,
              fieldType:item.fieldType
           }  
      })
      let params = {
        accountType: 0,
        avatar: this.tableData.avatar,
        mobile: this.tableData.mobile,
        userName: this.tableData.userName,
        projectId: this.$store.state.projectInfo.id,
        realName: this.tableData.realName,
        userGroupList: this.addCheckList ,
      }  
      if (this.type == 'add') {
         params.extField = JSON.stringify(value)
        addUser(params).then((res) => {
          if (res.status == 200) {
            // this.goBack()
            window.location.reload();
          }
        })
      } else {
        params.userGroupList = this.addCheckList 
        params.userId = this.tableData.userId
        params.extField =JSON.stringify(value2)
        updateUser(params).then((res) => {
          if (res.status == 200) {
            // this.goBack()
            window.location.reload();
          }
        })
      }
    },
    cancel() {
      this.goBack()
    },
    goBack() {
      this.$emit('back')
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'||file.type === 'image/png'||file.type === 'image/jpg'||file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传头像只能是图片格式!');
        return false
        }
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 5MB!')
        return false
      } else {
        return true
      }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader()
      reader.onload = function () {
        callback(reader.result)
      }
      reader.readAsDataURL(file) //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    customRequest(item) {
      let _this = this
      this.filetoDataUrl(item.file, function (img) {
        let params = {
          base64: img
        }
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.tableData.avatar = res.data.url
          }
        })
      })
    },
    handleCheckChange(){
        this.addCheckList=this.$refs.tree.getCheckedKeys()
    },
    // 处理上传文件变化
    handleUploadChange(item, value) {
      item.fieldValue = value;
    },
    // 处理文件上传
    handleFileUpload(option, item) {
      let _this = this;
      this.filetoDataUrl(option.file, function (fileData) {
        let params = {
          base64: fileData
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            item.fieldValue = res.data.url;
            _this.$message.success('文件上传成功');
          }
        });
      });
    },
    // 文件上传前验证
    beforeFileUpload(file) {
      const allowedTypes = [
        // 图片格式
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        // 文档格式
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/plain',
        // 视频格式
        'video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv'
      ];
      const isAllowedType = allowedTypes.includes(file.type);
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isAllowedType) {
        this.$message.error('支持上传图片、文档、视频等常见格式的文件!');
        return false;
      }
      if (!isLt20M) {
        this.$message.error('文件大小不能超过 20MB!');
        return false;
      }
      return true;
    },
    // 处理文件移除
    handleFileRemove(item) {
      item.fieldValue = '';
    }
  }
}
</script>
<style scoped lang="scss">
.yxd_users-create {
  .col {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    .tl {
      width: 80px;
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 20px;
      // white-space: nowrap;
      text-align: right;
    }
    .custom {
      display: flex;
      align-items: center;
      width: 100%;
    }
    ::v-deep .avatar-uploader {
      .el-upload--text {
        width: 120px;
        height: 120px;
        line-height: 120px;
        background-color: #fff;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        img {
          width: 120px;
          height: 120px;
          object-fit: contain;
        }
      }
    }
    .el-input {
      width: 30%;
    }
    .checkbox {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        margin-bottom: 10px;
      }
    }
  }
  .sp {
    align-items: flex-start;
  }
}
</style>