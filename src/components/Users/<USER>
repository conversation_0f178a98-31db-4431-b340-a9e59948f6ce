<template>
  <div class="yxd_users">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="name" placeholder="请输入姓名"></el-input>
          </div>
          <div class="col">
            <el-input v-model="mobile" placeholder="请输入手机号码"></el-input>
          </div>
          <div class="col">
            <span class="tl"></span>
            <el-cascader
            :options="listData"
            placeholder="请选择用户组"
            v-model="roleName"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles'
            }"
            clearable>
          </el-cascader>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button v-if="btnList.add_doctor_user_btn" @click="addUser" 
          >添加用户</el-button
        >
        <el-button
          v-if="btnList.batch_Import_doctor_user_btn"
          @click="uploadVisible = true"
          >批量导入</el-button
        >
        <el-button v-if="btnList.export_doctor_user" @click="handleExportUserInfo" 
          >导出用户</el-button
        >
      </div>
      <div class="table">
        <ms-table
          :columns="data.columns"
          :data="data.list"
          ref="tableRef"
        ></ms-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <el-dialog title="批量导入用户" v-model="uploadVisible" width="50%" :before-close="outVisible">
        <div class="edit-main">
          <div class="col">
            <span>分类名称</span>
            <div class="upload">
              <el-upload
                ref="treeRef"
                class="upload-demo"
                action="#"
                :http-request="customRequest"
                :limit="3"
              >
                <el-button icon="el-icon-upload">上传文件</el-button>
              </el-upload>
              <span @click="downTemplate">下载导入模版</span>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="saveEdit" size="mini"
              >校验数据</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <h3>确定删除用户</h3>
        <el-tag class="delete-box">
          {{ currentChose.realName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveDeleteEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>


      <!-- 自定义字段预览弹窗 -->
      <el-dialog :title="previewTitle" v-model="previewVisible" width="60%">
        <div v-if="previewFile">
          <img v-if="isImage(previewFile)" :src="previewFile" style="width: 100%; height: auto;" />
          <div v-else style="text-align: center; padding: 20px;">
            <p>无法预览此文件类型</p>
            <p style="margin-bottom: 10px">{{previewFile}}</p>
            <el-button type="primary" @click="openFile">打开文件</el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 审核完善信息弹窗 -->
      <el-dialog :title="`${currentChose.realName || ''} 完善信息审核`" v-model="approvePerfectVisible" width="40%">
        <el-radio-group v-model="approvePerfectStatus">
          <el-radio :label="2">审核通过</el-radio>
          <el-radio :label="3">审核不通过</el-radio>
        </el-radio-group>
        <div v-if="approvePerfectStatus === 3" style="margin-top: 15px;">
          <el-input
            v-model="perfectInfoApprovalReason"
            type="textarea"
            placeholder="请输入审核不通过理由"
            :rows="3"
          ></el-input>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="approvePerfectVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveApprovePerfectEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getUserList,
  downLoad,
  importUser,
  deleteUser,
  yxdExportUserInfo,
  query,
  approvePerfectInfo
} from '@/request/service.js'
import initModule from '@/mixins/initModule.js'
import initScroll from '@/mixins/initScroll.js'
import { onMounted, ref, reactive, resolveComponent } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'

export default {
  name: 'users',
  mixins: [initModule, initScroll],
  emits: ['add', 'edit'],
  components: {},
  setup(props, ctx) {
    const uploadVisible = ref(false)
    const approvePerfectVisible = ref(false)
    const approvePerfectStatus = ref(2)
    const perfectInfoApprovalReason = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    const name = ref('')
    const mobile = ref('')
    const roleName = ref('')
    const listData = ref([])
    const previewVisible = ref(false)
    const previewFile = ref('')
    const previewTitle = ref('附件预览')

    // 预览自定义字段附件
    const handlePreview = (fileUrl, fieldDisplayName) => {
      previewFile.value = fileUrl
      previewTitle.value = fieldDisplayName ? `${fieldDisplayName}预览` : '附件预览'
      previewVisible.value = true
    }

    // 判断是否为图片
    const isImage = (url) => {
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url)
    }

    // 打开文件
    const openFile = () => {
      window.open(previewFile.value, '_blank')
    }
    // 基础列（不包含操作列）
    const baseColumns = [
      {
        label: '序号',
        prop: 'accountId',
        width: 60
      },
      {
        label: '用户名',
        prop: 'userName'
      },
      {
        label: '真实姓名',
        prop: 'realName'
      },
      {
        label: '手机号码',
        prop: 'mobile',
        width: 100
      },
      {
        label: '用户组',
        prop: 'roleName'
      },
      {
        label: '城市',
        prop: 'cityName'
      },
      {
        label: '单位',
        prop: 'companyName',
        width: 150
      },
      {
        label: '科室',
        prop: 'departmentName'
      },
      {
        label: '身份',
        prop: 'professionalCatName'
      },
      {
        label: '职称',
        prop: 'professionalName'
      },
      {
        label: '注册时间',
        prop: 'createdTime',
        width: 95
      }
    ]

    // 审核状态列（单独定义，根据数据动态添加，固定在右侧）
    const createApprovalStatusColumn = () => ({
      label: '审核状态',
      prop: 'perfectInfoStatus',
      width: 90,
      fixed: 'right',
      render: (h, scope) => {
        // 1-待审核  2-审核通过  3-审核不通过
        const statusMap = {
          1: { text: '待审核', color: '#E6A23C' },
          2: { text: '审核通过', color: '#67C23A' },
          3: { text: '审核不通过', color: '#F56C6C' }
        }
        const status = statusMap[scope.row.perfectInfoStatus]
        return h('span', {
          style: {
            color: status ? status.color : '#909399'
          }
        }, status ? status.text : '')
      }
    })

    // 操作列（单独定义，在函数内部创建以确保作用域正确，固定在右侧）
    const createOperationColumn = () => ({
      label: '操作',
      width:"210",
      fixed: 'right',
      render: (h, scope) => {
        const msHandle = resolveComponent('ms-handle')
        const items = [
          { label: '编辑', func: { func: 'add', uuid: scope }, show: true,type:"default" },
          { label: '审核', func: { func: 'approvePerfect', uuid: scope }, show: scope.row.perfectInfoStatus==1||scope.row.perfectInfoStatus==3?true:false,type:"success" },
          { label: '去审', func: { func: 'approvePerfect', uuid: scope }, show: scope.row.perfectInfoStatus==2?true:false,type:"warning" },
          {
            label: '删除',
            func: { func: 'remove', uuid: scope },
            show: true,
            type:"danger"
          }
        ]
        return h(msHandle, {
          items,
          onAdd: () => {
            handleEdit(scope.row)
          },
          onApprovePerfect: () => {
            handleApprovePerfect(scope.row)
          },
          onRemove: () => {
            handleDelete(scope.row)
          }
        })
      }
    })
    const data = reactive({
      columns: [],
      list: []
    })
    const deleteVisible = ref(false)
    const currentChose = reactive({})
    const currentFile = reactive({
      file: null
    })
    const treeRef = ref(null)
    const userImportFailList = ref([])
    const init = async () => {
      data.list=[]

      let params = {
        accountType: 0,
        mobile: mobile.value,
        pageIndex: currentPage.value,
        pageSize: pageSize.value,
        realName: name.value,
        // 用户组筛选数据
        // roleName.value
        userName: '',
      }
      if(!roleName.value){
        params.roleId=0
      }else if(roleName.value.length===1){
        params.roleId=roleName.value[0]
      }else if(roleName.value.length===2){
        params.roleId=roleName.value[1]
      }else{
        params.roleId=roleName.value[2]
      }
      await getUserList(params).then((res) => {
        if (res.status == 200) {
          // 重新构建列数组：基础列 + 自定义字段列 + 审核状态列（如果需要）+ 操作列
          data.columns.length = 0
          data.columns.push(...baseColumns)

          // 添加自定义字段列
          if (res.data && res.data.length > 0 && res.data[0].extField) {
            res.data[0].extField.forEach((val) => {
              // 如果是附件上传类型字段（fieldType为8），添加预览功能
              if (val.fieldType === 8) {
                data.columns.push({
                  label: val.fieldDisplayName,
                  width: "90",
                  render: (h, scope) => {
                    const msHandle = resolveComponent('ms-handle')
                    const fieldValue = scope.row[val.fieldName]
                    const items = [
                      {
                        label: '预览',
                        func: { func: 'preview', uuid: scope },
                        show: fieldValue ? true : false,
                        type: "primary"
                      },
                    ]
                    return h(msHandle, {
                      items,
                      onPreview: () => {
                        handlePreview(fieldValue, val.fieldDisplayName)
                      }
                    })
                  }
                })
              } else {
                data.columns.push({
                  label: val.fieldDisplayName,
                  prop: val.fieldName ? val.fieldName : '',
                  "width": "100"
                })
              }
            })
          }

          // 检查是否需要显示审核状态列（如果有任何用户的perfectInfoStatus不为0）
          const shouldShowApprovalStatus = res.data && res.data.some(user => user.perfectInfoStatus && user.perfectInfoStatus !== 0)
          if (shouldShowApprovalStatus) {
            // 在操作列之前添加审核状态列
            data.columns.push(createApprovalStatusColumn())
          }

          // 最后添加操作列
          data.columns.push(createOperationColumn())
          data.list = res.data
          if (data.list && data.list.length > 0 && data.list[0].extField) {
            data.list.forEach((d, index) => {
              d.extField.forEach((t) => {
                data.list[index][t.fieldName] = t.fieldValue
              })
            })
          }
          // initScroll();

          total.value = res.totalSize
        }
      })
    }
     const gitListData = async () => {
         let params = {
            accountType:0,
            title: ''
          }
        await query(params).then((res) => {
            if (res.status == 200) {
            listData.value =getList(res.data)
            }
          })
     }
     const  getList =(data)=>{
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  getList(item.childRoles)
        }
     })
    }
    const downTemplate = async () => {
      await downLoad().then((res) => {
        if (res.status == 200) {
          var a = document.createElement('a')
          a.target = '_self'
          a.href = res.data
          a.click()
        }
      })
    }
    const customRequest = (item) => {
      currentFile.file = item.file
    }
    const handleSizeChange = (val) => {
      pageSize.value = val
      init()
    }
    const handleCurrentChange = (val) => {
      currentPage.value = val
      init()
    }
    const addUser = () => {
      // !!!
      ctx.emit('add', {})
    }
    const handleApprovePerfect = (row) => {
      approvePerfectVisible.value = true
      approvePerfectStatus.value = 2
      perfectInfoApprovalReason.value = ''
      Object.assign(currentChose, row)
    }
    const handleEdit = (row) => {
      ctx.emit('edit', row)
    }
    const handleDelete = (row) => {
      deleteVisible.value = true
      Object.assign(currentChose, row)
    }

    const saveApprovePerfectEdit = async () => {
      // 完善信息审核状态：0-未完善 1-待审核  2-审核通过  3-审核不通过
      let data = {
        id: currentChose.id,
        perfectInfoStatus: approvePerfectStatus.value
      }
      // 如果是审核不通过，需要传递理由
      if (approvePerfectStatus.value === 3) {
        if (!perfectInfoApprovalReason.value.trim()) {
          ElMessage.warning('请输入审核不通过理由')
          return
        }
        data.perfectInfoApprovalReason = perfectInfoApprovalReason.value
      }
      await approvePerfectInfo(data).then((res) => {
        if (res.status == 200) {
          ElNotification({
            title: '提示',
            message: `操作成功`,
            type: 'success',
            position: 'bottom-right'
          })
          init()
          approvePerfectVisible.value = false
        }else {
          ElNotification({
              title: '错误提示',
              message: `完善信息审核失败`,
              type: 'error',
              position: 'bottom-right'
            })
          approvePerfectVisible.value = false
        }
      })
    }
    const saveEdit = async () => {
      let formData = new FormData()
      formData.append('file', currentFile.file)
      await importUser(formData).then((res) => {
        if (res.status == 200) {
          if (res.data.verfiyFail) {
            userImportFailList.value = res.data.userImportFailList
            let temp = ''
            userImportFailList.value.forEach((val) => {
              temp += val.errorMsg + '|'
            })
            ElNotification({
              title: '错误提示',
              message: `成功${res.data.successCount}条，失败${res.data.failCount}条，原因为${temp}`,
              type: 'error',
              position: 'bottom-right'
            })
          } else {
            ElNotification({
              title: '提示',
              message: `成功${res.data.successCount}条`,
              type: 'success',
              position: 'bottom-right'
            })
          }
          init()
        }
      })
      treeRef.value.clearFiles()
      uploadVisible.value = false
    }
    const search = () => {
      init()
    }
    const outVisible = () => {
      treeRef.value.clearFiles()
      uploadVisible.value = false
    }

    const reset = () => {
      name.value = ''
      mobile.value = ''
      roleName.value = ''
    }
    const saveDeleteEdit = async () => {
      await deleteUser(currentChose.userId, 0).then((res) => {
        if (res.status == 200) {
          init()
          deleteVisible.value = false
        }
      })
    }
    const handleExportUserInfo = async () => {
      let params = {
        mobile: mobile.value,
        realName: name.value,
      }
      if(!roleName.value){
        params.roleId=0
      }else if(roleName.value.length===1){
        params.roleId=roleName.value[0]
      }else if(roleName.value.length===2){
        params.roleId=roleName.value[1]
      }else{
        params.roleId=roleName.value[2]
      }
      yxdExportUserInfo(params).then((res) => {
        if (res.status == 200) {
          let name = `导出数据.csv`
          let link = document.createElement('a')
          link.href = res.data
          link.download = name
          link.click()
        } else {
          ElMessage.warning('暂无数据导出')
        }
      })
    }

    onMounted(() => {
      init()
      gitListData()
    })

    return {
      uploadVisible,
      approvePerfectVisible,
      approvePerfectStatus,
      perfectInfoApprovalReason,
      currentPage,
      pageSize,
      total,
      name,
      mobile,
      data,
      deleteVisible,
      previewVisible,
      previewFile,
      previewTitle,
      currentChose,
      currentFile,
      userImportFailList,
      treeRef,
      init,
      gitListData,
      downTemplate,
      customRequest,
      handleSizeChange,
      handleCurrentChange,
      addUser,
      handleEdit,
      handleApprovePerfect,
      handleDelete,
      saveEdit,
      search,
      reset,
      saveDeleteEdit,
      saveApprovePerfectEdit,
      handleExportUserInfo,
      handlePreview,
      isImage,
      openFile,
      roleName,
      listData,
      getList,
      outVisible
    }
  }
}
</script>

<style scoped lang="scss">
.yxd_users {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .edit-main {
    .col {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30px;
      .el-input {
        width: 30%;
      }
      span {
        margin-top: 10px;
      }
      .upload {
        display: flex;
        flex-direction: column;
        margin-left: 20px;
        span {
          width: 50px;
          white-space: nowrap;
          text-align: right;
          margin-top: 20px;
          cursor: pointer;
          color: #169bd5;
        }
      }
    }
  }
  ::v-deep .el-dialog__footer {
    text-align: center;
  }
}
</style>
