<template>
  <div class="yxd_group_detail">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="back" @click="back">
        <i class="el-icon-back"></i><span>返回</span>
      </div>
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">添加成员</span>
            <div class="textBox">
              <el-input v-model="member" placeholder="请输入真实姓名"
                ><template #append>
                  <el-button
                    icon="el-icon-search"
                    @click.stop="memberChange"
                  ></el-button> </template
              ></el-input>
              <ul v-if="showSearch">
                <li
                  v-for="item in searchList"
                  :key="item.userId"
                  :value="item.userId"
                  @click.stop="chose(item)"
                >
                  {{ item.realName }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="right">
          <el-button @click="addUser" icon="el-icon-plus" type="primary"
            >添加成员</el-button
          >
        </div>
      </div>
      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column
            prop="roleId"
            label="组ID"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column prop="userName" label="成员用户名" align="center">
          </el-table-column>
          <el-table-column prop="realName" label="成员真实姓名" align="center">
          </el-table-column>
          <el-table-column prop="mobile" label="手机号码" align="center">
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                size="mini"
                type="danger"
                plain
                @click.stop="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
      <span
        >是否删除 <el-tag>{{ currentChose.userName }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="deleteSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  adminUsers,
  removeAdminUser,
  bundleAdminUser,
  getUserAdminList,
} from "@/request/service.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "group-detail",
  mixins: [initScroll],
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      showSearch: false,
      deleteVisible: false,
      member: "",
      currentChose: {},
      tableData: [],
      searchList: [],
      checkChose: {},
      currentPage: 1,
      pageSize: 20,
      total: 0,
    };
  },
  mounted() {
    document.body.addEventListener(
      "click",
      () => {
        this.showSearch = false;
      },
      false
    );
    this.init();
  },
  methods: {
    init() {
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        roleId: this.info.id,
      };
      adminUsers(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll()
        }
      });
    },
    memberChange() {
      let params = {
        mobile: "",
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        realName: this.member,
        userName: '',
      };
      getUserAdminList(params).then((res) => {
        if (res.status == 200) {
          this.searchList = res.data;
        }
      });
      this.showSearch = true;
    },
    chose(item) {
      this.searchList = [];
      this.checkChose = item;
      this.member = item.realName;
      this.showSearch = false;
    },
    addUser() {
      let params = {
        roleId: this.info.id,
        userId: this.checkChose.userId,
      };
      bundleAdminUser(params).then((res) => {
        if (res.status == 200) {
          this.init();
        }
      });
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    back() {
      this.$emit("back");
    },
    deleteSave() {
      let paramId = [];
      paramId.push(this.currentChose.userId.toString());
      let params = {
        roleId: this.info.id,
        users: paramId,
      };
      removeAdminUser(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.init();
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_group_detail {
  .back {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    cursor: pointer;
    i {
      font-size: 24px;
      font-weight: 500;
    }
    span {
      font-size: 20px;
      font-weight: 500;
      margin-left: 10px;
    }
  }
  .header {
    display: flex;
    justify-content: flex-start;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
        .textBox {
          position: relative;
          ul {
            width: 100%;
            position: absolute;
            top: 50px;
            left: 0;
            z-index: 999;
            overflow: auto;
            border-radius: 4px;
            box-sizing: border-box;
            background: #fff;
            border: 1px solid #e4e7ed;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.2;
            min-width: 10px;
            padding: 6px 0;
            li {
              font-size: 14px;
              padding: 0 32px 0 20px;
              position: relative;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #606266;
              height: 34px;
              line-height: 34px;
              box-sizing: border-box;
              cursor: pointer;
            }
            li:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }
    }
    .right {
    }
  }

}
</style>
