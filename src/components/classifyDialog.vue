<template>
   <!-- 编辑弹出框 -->
       <!-- controlDialog -->
      <div>
      <el-dialog  v-if="editVisible" title="批量设置分类"  v-model="editVisible" width="70%" @close="closeDialog()">
        <div class="power-head">
           <table>
            <tr class="line">
              <td class="title">分类属性</td>
              <td>
                <el-radio-group @change="CancelRoleList" v-model="radio" fill="#797979" text-color="#797979">
                  <el-radio :label="1">设置分类</el-radio>
                  <el-radio :label="0">取消分类</el-radio>
                </el-radio-group>
              </td>
            </tr>
          </table>
          <div class="UserTree" v-show="radio">
            <div class="user">请选择分类:</div>
                <div class="down-tree">
                  <el-tree
                    ref="tree"
                    :data="tableData"
                    show-checkbox
                    node-key="id"
                    :default-expand-all="true"
                    :check-strictly="true"
                    @check="getChecked"
                    @check-change="handleCheckChange"
                    :props="{
                    children: 'childTags',
                    label: 'tagName'
                  }">
                  </el-tree>
                </div>
          </div>
          <div class="rightControl" v-show="!radio">
            <el-table class="tableControl" ref="multipleTable" border :data="classifyData" :default-sort="{ prop: 'date', order: 'descending' }"
              tooltip-effect="dark" style="width: 100%">
              <el-table-column  prop="moduleId" label="内容ID" width="150" align="center">
              </el-table-column>
              <el-table-column prop="title" label="内容标题" align="center" >
              </el-table-column>
              <el-table-column prop="tagName" label="已设置分类"   align="center">
              </el-table-column>
              <el-table-column prop="approvalStatus" label="操作" width="200" align="center">
                <template #default="scope">
                  <el-button
                  size="mini"
                  type="danger"
                  plain
                  @click="handleDelete(scope.$index, scope.row)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeDialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="savePower" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      </div>
</template>

<script>
import { ElMessage } from "element-plus";
// import {  getAriticlePage, batchEdit, tagTypeList, getRoleList, setDelePower , getRoleQuery, query } from "@/request/service.js";
import { setCancelTag , getModuleTags , delCancelTag , listTag } from "@/request/service.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "classifyDialog",
  mixins: [initModule, initScroll],
  props:{
    choseData:{
      type : Array,
      required: true
    },
  },
  data() {
    return {
      editVisible :false,
      checkList:[],
      type:'',
      radio:1,
      tableData:[],
      title:'',
      classifyData:[]
    };
  },
  methods: {
    // 打开弹框
     openDialog(type){
       this.editVisible = true
       this.type=type
     },
    //  获取分类数据
    gitClassList(){
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType: this.type,
      };
      listTag(params).then((res) => {
          this.tableData=res.data
      })
    },
    // 选项框
    handleCheckChange(){
        this.checkList = this.$refs.tree.getCheckedKeys()
    },
    // 确定按钮
    savePower() {
      if(this.radio === 1){
        let ids = [];
        let moduleList = []
        if(this.type==='course'){
          this.choseData.forEach((val) => {
            ids.push(val.courseId);
            moduleList.push({
              moduleId: val.id,
              title: val.title
            })
          });
        }else{
          this.choseData.forEach((val) => {
            ids.push(val.id);
          });
        }
        if(this.checkList.length>0){
          // 批量设置分类接口
        let params = {
          "moduleIds": [...ids],
          // "moduleType": "article",
          "moduleType": this.type,
          "operateType": this.radio,
          "tagIds": this.checkList,
          "moduleList": [...moduleList]
        }
        setCancelTag(params).then((res) => {
          if (res.status == 200) {
              ElMessage({
                message: '设置分类成功',
                type: 'success',
              })
          }
        });
        }
      }
      this.closeDialog()
    },
    // 关闭弹框
    closeDialog(){
      this.type=''
      this.radio = 1;
      this.editVisible = false;
      this.classifyData=[],
      this.checkList = [];
      this.$emit("init")
    },

    // 自定义按钮
    getChecked(data){
      const node = this.$refs.tree.getNode(data.id);
      this.setNode(node);
    },
    setNode(node){
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node,node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node,node.checked);
      }
    },
    setChildrenNode(node,state){
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i],state);
      }
    },
    setParentNode(node){
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    },
    // 取消分类数据回显
    CancelRoleList(index){
        if(index===0){
        let ids = [];
          if(this.type==='course'){
            this.choseData.forEach((val) => {
            ids.push({
               "moduleId":val.courseId,
               "title":val.title
            });
          });
          } else if(this.type==='live_info'||this.type==='eda') {
            this.choseData.forEach((val) => {
            ids.push({
               "moduleId":JSON.stringify(val.id),
               "title":val.name||val.title
            });
          });
          }else if(this.type==='survey') {
            this.choseData.forEach((val) => {
            ids.push({
               "moduleId":val.id,
               "title":val.templateName
            });
          });
          }else if(this.type==='tool_impact_factor') {
            this.choseData.forEach((val) => {
            ids.push({
               "moduleId":val.id,
               "title":val.fullname
            });
          });
          }else{
             this.choseData.forEach((val) => {
            ids.push({
               "moduleId":val.id,
               "title":val.title
            });
          });
          }
          let params = {
            // "moduleIds": [...ids],
            "moduleType": this.type,
            "modules":[...ids]
          }
          getModuleTags(params).then((res)=>{
          this.classifyData=res.data
          })  
      }
    },
    // 删除单条权限
    handleDelete(index,row){ 
     this.$confirm('是否删除分类?', '提示', {
     confirmButtonText: '确定',
     cancelButtonText: '取消',
     type: 'warning'
        }).then(() => {
          let params = {
          "moduleId": row.moduleId,
          "moduleType": this.type,
          "tagId": row.tagId
        }
        delCancelTag(params).then((res) => {
          if(res.status===200){
            this.CancelRoleList(0)
          }
          });
        this.$message({
            type: 'success',
            message: '删除成功!'
          });
        })
    },
  },
};
</script>

<style scoped lang="scss">
  .power-head {
    table {
      .line {
        height: 45px;
      }

      tr {
        padding: 30px 0;
        .title {
          width: 70px;
          // font-size: 20px;
        }

      }
    }
    .UserTree{
      margin-top: 10px;
      display: flex;
      .user {
        width: 70px;
      }
      .down-tree {
          flex: 1;
          height: 300px;
          display: block;
          overflow-y: scroll;
        }
    }
     ::v-deep {
       div.el-table::before {
          height: 0px;
       }
     }
  }
    .rightControl {
      .tableControl{
        height: 300px;
        display: block;
        overflow-y: scroll;
      }
    }
</style>
<style lang="scss">
</style>
