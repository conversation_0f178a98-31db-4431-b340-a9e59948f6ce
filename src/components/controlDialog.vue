<template>
  <!-- 编辑弹出框 -->
  <!-- controlDialog -->
  <div>
    <el-dialog
      v-if="powerVisible"
      title="批量设置权限"
      v-model="powerVisible"
      width="70%"
      @close="closeDialog()"
    >
      <el-tabs type="card" v-model="activeName" @tab-click="handleChangeTab">
        <el-tab-pane label="医生类" name="first" v-if="btnList.doctorRole">
          <!-- 医生 -->
          <div class="power-head">
            <table>
              <tr class="line">
                <td class="title">权限属性</td>
                <td>
                  <el-radio-group
                    @change="CancelRoleList"
                    v-model="radio"
                    fill="#797979"
                    text-color="#797979"
                  >
                    <el-radio :label="1">设置权限</el-radio>
                    <el-radio :label="0">取消权限 </el-radio>
                  </el-radio-group>
                </td>
              </tr>
            </table>
            <div class="UserTree" v-show="radio">
              <div class="user">用户组</div>
              <div class="down-tree">
                <el-tree
                  ref="tree"
                  :data="RoleList"
                  show-checkbox
                  node-key="id"
                  :default-expand-all="true"
                  :check-strictly="true"
                  @check="getChecked"
                  @check-change="handleCheckChange"
                  :props="{
                    children: 'childRoles',
                    label: 'title',
                  }"
                >
                </el-tree>
              </div>
            </div>
            <div class="rightControl" v-show="!radio">
              <el-table
                class="tableControl"
                ref="multipleTable"
                border
                :data="tableData"
                :default-sort="{ prop: 'date', order: 'descending' }"
                tooltip-effect="dark"
                style="width: 100%"
              >
                <el-table-column
                  prop="moduleId"
                  label="内容ID"
                  width="150"
                  align="center"
                >
                </el-table-column>
                <el-table-column prop="title" label="内容标题" align="center">
                </el-table-column>
                <el-table-column
                  prop="roleName"
                  label="已设置用户组"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="approvalStatus"
                  label="操作"
                  width="200"
                  align="center"
                >
                  <template #default="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      plain
                      @click="handleDelete(scope.$index, scope.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div></el-tab-pane
        >
        <el-tab-pane label="患者类" name="second" v-if="btnList.patientRole">
          <!-- 患者 -->
          <div class="power-head">
            <table>
              <tr class="line">
                <td class="title">权限属性</td>
                <td>
                  <el-radio-group
                    @change="CancelRoleList"
                    v-model="patientRadio"
                    fill="#797979"
                    text-color="#797979"
                  >
                    <el-radio :label="1">设置权限</el-radio>
                    <el-radio :label="0">取消权限 </el-radio>
                  </el-radio-group>
                </td>
              </tr>
            </table>
            <div class="UserTree" v-show="patientRadio">
              <div class="user">用户组</div>
              <div class="down-tree">
                <el-tree
                  ref="patientTree"
                  :data="RoleList"
                  show-checkbox
                  node-key="id"
                  :default-expand-all="true"
                  :check-strictly="true"
                  @check="getChecked"
                  @check-change="handleCheckChange"
                  :props="{
                    children: 'childRoles',
                    label: 'title',
                  }"
                >
                </el-tree>
              </div>
            </div>
            <div class="rightControl" v-show="!patientRadio">
              <el-table
                class="tableControl"
                ref="multipleTable"
                border
                :data="tableData"
                :default-sort="{ prop: 'date', order: 'descending' }"
                tooltip-effect="dark"
                style="width: 100%"
              >
                <el-table-column
                  prop="moduleId"
                  label="内容ID"
                  width="150"
                  align="center"
                >
                </el-table-column>
                <el-table-column prop="title" label="内容标题" align="center">
                </el-table-column>
                <el-table-column
                  prop="roleName"
                  label="已设置用户组"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="approvalStatus"
                  label="操作"
                  width="200"
                  align="center"
                >
                  <template #default="scope">
                    <el-button
                      size="mini"
                      type="danger"
                      plain
                      @click="handleDelete(scope.$index, scope.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog()" size="mini">取 消</el-button>
          <el-button type="primary" @click="savePower" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
// import {  getAriticlePage, batchEdit, tagTypeList, getRoleList, setDelePower , getRoleQuery, query } from "@/request/service.js";
import {
  query,
  setDelePower,
  getModuleRoles,
  delCancelRole,
} from "@/request/service.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "controlDialog",
  mixins: [initModule, initScroll],
  props: {
    choseData: {
      type: Array,
      required: true,
    }
  },
  data() {
    return {
      powerVisible: false,
      checkList: [],
      radio: 1,
      patientRadio: 1,
      type: "",
      tableData: [],
      activeName:"first",
      RoleList: []
    };
  },
  created() {
  },
  methods: {
    handleChangeTab(val) {
      this.radio = 1
      this.patientRadio = 1
      this.getPowerData(val.paneName)
    },
     // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val == "first" ? 0 : 1,
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    openDialog(type) {
      if(this.btnList.doctorRole&&this.btnList.patientRole){
        this.activeName="first"
      }
      if(this.btnList.doctorRole&&!this.btnList.patientRole){
        this.activeName="first"
      }
      if(!this.btnList.doctorRole&&this.btnList.patientRole){
        this.activeName="second"
      }
      this.getPowerData(this.activeName)
      this.powerVisible = true;
      this.type = type;
    },
    // 权限下拉列表
    // getPowerData() {
    //   let params = {
    //     title: '',
    //   };
    //     query(params).then((res) => {
    //     if (res.status == 200) {
    //       this.RoleList = res.data;
    //     }
    //   });
    // },
    // 选项框
    handleCheckChange() {
      if(this.activeName == 'first'){
        this.checkList = this.$refs.tree.getCheckedKeys();
      }else{
        this.checkList = this.$refs.patientTree.getCheckedKeys();
      }
    },
    savePower() {
      // let paramsList = [];
      // // checkList为勾选的id,数组
      // this.checkList.forEach(item => {
      //   const value = item.split(':')[0] - 0;
      //   // const label = item.split(':')[1];
      //   paramsList.push(value)
      // });
      if (this.radio === 1 || this.patientRadio === 1) {
        let ids = [];
        if (this.type === "course") {
          this.choseData.forEach((val) => {
            ids.push(val.courseId);
          });
        } else {
          this.choseData.forEach((val) => {
            ids.push(val.id);
          });
        }
        let params = {
          accountType: this.activeName == "first" ? 0 : 1,
          moduleIds: [...ids],
          // "moduleType": "article",
          moduleType: this.type,
          operateType: this.activeName == "first" ? this.radio : this.patientRadio,
          roleIds: this.checkList,
        };
        setDelePower(params).then((res) => {
          if (res.status == 200) {
            ElMessage({
              message: "设置权限成功",
              type: "success",
            });
          }
        });
      }
      this.closeDialog();
    },
    changeCheck() {
    },
    // 关闭弹框
    closeDialog() {
      this.type = "";
      this.radio = 1;
      this.patientRadio = 1
      this.powerVisible = false;
      (this.tableData = []), (this.checkList = []);
    },
    // 取消权限数据回显
    CancelRoleList(index) {
      if (index === 0) {
        let ids = [];
        if (this.type === "course") {
          this.choseData.forEach((val) => {
            ids.push(val.courseId);
          });
        } else {
          this.choseData.forEach((val) => {
            ids.push(val.id);
          });
        }
        let params = {
          accountType: this.activeName == "first" ? 0 : 1,
          moduleIds: [...ids],
          moduleType: this.type,
        };
        getModuleRoles(params).then((res) => {
          this.tableData = res.data;
        });
      }
    },
    // 删除单条权限
    handleDelete(index, row) {
      this.$confirm("是否删除权限?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          accountType: this.activeName == "first" ? 0 : 1,
          moduleId: row.moduleId,
          moduleType: this.type,
          roleId: row.roleId,
        };
        delCancelRole(params).then((res) => {
          if (res.status === 200) {
            this.CancelRoleList(0);
          }
        });
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    // 自定义按钮
    getChecked(data) {
      const node = this.$refs.tree.getNode(data.id);
      this.setNode(node);
    },
    setNode(node) {
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node, node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node, node.checked);
      }
    },
    setChildrenNode(node, state) {
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i], state);
      }
    },
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.power-head {
  table {
    .line {
      height: 45px;
    }

    tr {
      padding: 30px 0;
      .title {
        width: 70px;
        // font-size: 20px;
      }
    }
  }
  .UserTree {
    display: flex;
    .user {
      width: 70px;
    }
    .down-tree {
      flex: 1;
      height: 300px;
      display: block;
      overflow-y: scroll;
    }
  }
  ::v-deep {
    div.el-table::before {
      height: 0px;
      background: url("https://img.medsci.cn/web/prod/img/journal_back.png"),
        100 100;
    }
  }
}
.rightControl {
  .tableControl {
    height: 300px;
    display: block;
    overflow-y: scroll;
  }
}
</style>
<style lang="scss">
</style>
