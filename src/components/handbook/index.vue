<template>
  <div class="yxd_information">
    <div id="container" class="container" :class="{ isScroll: isScroll }" ref="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="输入指南标题"></el-input>
          </div>
          <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择分类"
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
            </el-cascader> 
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div> 
          <!-- 筛选权限 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择权限"
            :options="RoleList"
            ref="cascader"
            collapse-tags	
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles',
            multiple: true
            }"
            clearable
            >
          </el-cascader> 
          </div>
        </div>
        <div class="right">
          <!-- icon="el-icon-search" -->
          <el-button icon="el-icon-search" @click="init()">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <!-- <el-button v-if="btnList.add_aticle_btn" @click="goTab" type="primary" icon="el-icon-plus">创建资讯</el-button> -->
        <el-button v-if="btnList.batchSetRole" @click="setPower">批量设置权限</el-button>
        <el-button v-if="btnList.batchSetTag" @click="setClass">批量设置分类</el-button>
        <el-button v-if="btnList.batch_start_btn" @click="lineBatch">批量上线</el-button>
        <el-button v-if="btnList.batch_end_btn" @click="offLineBatch">批量下线</el-button>
        <el-button v-if="btnList.batch_remove_btn" @click="deleteClass" icon="el-icon-close">批量删除</el-button>
        <el-button
          @click="filterList()"
          :type="btnStatus?'primary':''"
          >未设置分类</el-button
        >
      </div>
      <div class="table">
        <el-table ref="multipleTable" border :data="tableData" :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column sortable prop="id" label="ID" width="120" align="center">
          </el-table-column>
          <el-table-column prop="title" label="标题" min-width="120" align="left">
            <template #default="scope">
              <a :href="'/guider/'+scope.row.encodeId" target="_blank">{{
                  scope.row.title
              }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="publishedTime" label="发布时间" sortable width="200" align="center">
          </el-table-column>
          <el-table-column prop="approvalStatus" label="状态" width="120" align="center">
            <template #default="scope">
              <span :class="{ start: scope.row.status == 1 }" class="default"></span>
              <span>{{ scope.row.status===1?'已上线':'已下线' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pv" label="PV/UV/下载PV/下载UV" width="180" align="center">
             <template #default="scope">
               {{scope.row.pv}}/{{scope.row.uv}}/{{scope.row.downloadPv}}/{{scope.row.downloadUv}}
             </template>
          </el-table-column>
          <el-table-column prop="userCollects" label="收藏/点赞" width="120" align="center">
          <template #default="scope">
               {{scope.row.userCollects}}/{{scope.row.userLikes}}
             </template>
          </el-table-column>
          <!-- 新增字段 -->
          <el-table-column label="操作" align="center" min-width="220" fixed="right">
            <template #default="scope">
              <!-- <el-button v-if="btnList.edit_btn" size="mini" type="primary" :disabled="scope.row.projectId != projectId"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
              <el-button v-if="btnList.end_btn" size="mini" @click="handleDown(scope.$index, scope.row)">{{
                  scope.row.status == 1 ? "下线" : "上线"
              }}</el-button>
              <el-button v-if="btnList.remove_btn" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)" :disabled="scope.row.status == 1">删除</el-button>
              <el-button v-if="btnList.export_guider" size="mini" @click="deriveData(scope.$index, scope.row)">
                导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
       <!-- 批量设置权限弹框 -->
      <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog>
      <!-- 批量设置分类弹框 -->
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
      <el-dialog title="批量上线" v-model="onlineVisible" width="70%">
        <h3>确定上线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量下线" v-model="offLineVisible" width="70%">
        <h3>确定下线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span>是否{{ lineText == "上线提示" ? "上线" : "下线" }}
          <el-tag>{{ currentChose.title }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="lineSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog  title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.title }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { ElMessage } from "element-plus";
import {  getGuiderPage, deleteGuiderTag, tagTypeList, getRoleList, setDelePower,query,listTag,batchLineModule } from "@/request/service.js";
import { exportUserGuider } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import  controlDialog  from '../controlDialog.vue'
import  classifyDialog  from '../classifyDialog.vue'
import filterList from "@/mixins/filterList.js";
export default {
  name: "guider",
  mixins: [initModule, initScroll, initAccountTypeList,filterList],
  components:{
    controlDialog,
    classifyDialog
  },
  data() {
    return {
      title: "",
      lineTime: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      classGroup: [],
      // powerVisible: true,
      editVisible: false,
      settingVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      deleteVisible: false,
      lineText: "上线提示",
      currentChose: {},
      RoleList: [],
      choseData: [],
      ClassifyData:[],
      radio: 1,
      selectPower: '',
      // checkList: [],
      selectvalue: [],
      ClassifyValue: [],
      offLineData:{},
      accountTypeValue: '',
      refesh:false
    };
  },
  mounted() {
    this.init();
    this.getClassifyData()
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
    projectId() {
      return this.$store.state.projectInfo.id;
    },
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val){
      if(val===0||val===1){
        this.getPowerData(val)
      }else if(val===""){
        this.RoleList = []
      }
    },
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    }, 
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    init() {
      if(this.choseData.length>0){
            this.refesh=true
          }
      this.tableData=[]
      // let publishedStartTime;
      // let publishedEndTime;
      // if (this.lineTime && this.lineTime.length > 0) {
      //   publishedStartTime = this.lineTime[0];
      //   publishedEndTime = this.lineTime[1];
      // }
      let params = {
        title: this.title,
        // publishedStartTime: publishedStartTime,
        // publishedEndTime: publishedEndTime,
        guiderIds:[],
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue,
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      getGuiderPage(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.approvalStatus == 1) {
              val.approvalStatus = "已上线";
            } else {
              val.approvalStatus = "已下线";
            }
            val.gotoUrl =
              `https://` +
              window.location.host +
              `/news/detail/${val.encodeId}`;
          });
          this.filterData = this.tableData;
          this.btnStatus=false
        }
      });
    },
    // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'guider',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
  getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    // 批量设置权限
    setPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }else{
       this.$refs.controlRef.openDialog('guider')
      }
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog('guider')
      this.$refs.classifyRef.gitClassList()
      // this.initTag();
      // this.editVisible = true;
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData=this.choseData.filter(item=>item.status === 0)
      this.settingVisible = true;
    },
    // saveEdit() {
    //   let ids = [];
    //   let tagDtoList = [];
    //   this.tags.forEach((d) => {
    //     tagDtoList.push({
    //       tagId: d.tagId,
    //       tagName: d.tagName,
    //     });
    //   });
    //   this.choseData.forEach((val) => {
    //     ids.push(val.id);
    //   });
    //   let params = {
    //     type: 4,
    //     ids: ids,
    //     tagDtoList: tagDtoList,
    //   };
    //   batchEdit(params).then((res) => {
    //     if (res.status == 200) {
    //       this.init();
    //       this.editVisible = false;
    //     }
    //   });
    // },
     // 批量删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        guiderIds: ids,
      };
      deleteGuiderTag(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
    // 批量上线按钮
    saveOnlineEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"guider"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量上线成功");
          this.init();
          this.onlineVisible = false;
        }
      });
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"guider"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量下线成功");
          this.init();
          this.offLineVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.title = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    handleEdit(index, row) {
      this.$emit("go", row);
    },
    handleDown(index, row) {
      if (row.status == 1) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    // 指南导出
    deriveData(index, row) {
      exportUserGuider(row.id).then((res) => {
        if (res.size > 57) {
          let name = row.title+`指南详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
        // if (res.status == 200) {
        //   const blob = res.data;
        //   const reader = new FileReader();
        //   reader.readAsDataURL(blob);
        //   reader.onload = (e) => {
        //     const a = document.createElement('a');
        //     a.download = `文件名称.zip`;
        //     // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; 		   filename=\"20181211191944.zip\"",
        //     a.href = e.target.result;
        //     document.body.appendChild(a);
        //     a.click();
        //     document.body.removeChild(a);
        //   };
        // }
      });
    },
    exportNoLike(index, row) {
      // this.deleteVisible = true;
      // this.currentChose = row.userHits;
    },
    handleSelectionChange(selection) {
          this.choseData = selection
    },
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        operationType: this.lineText == "上线提示" ? 1 : 0,
        moduleType:'guider',
        moduleIds: ids,
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "上线提示"? ElMessage.success("上线成功"):ElMessage.success("下线成功")
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    // 单条删除
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        projectId: this.$store.state.projectInfo.id,
        guiderIds: ids,
      };
      deleteGuiderTag(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;  
          this.init();
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_information {
//  ::v-deep {
//     .el-cascader-panel {
//       background-color: #ff0;
//     }
//   }
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
        ::v-deep {
            div.el-input.el-input--suffix{
               width: 150px !important;
            }
        }
      }
    }
  }
  .power-head {
    table {
      .line {
        height: 45px;
      }
      tr {
        padding: 30px 0;
        .title {
          width: 70px;
          // font-size: 20px;
        }
      }
    }
  }
  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 58.333%;
      display: flex;
      align-items: center;
      span {
        white-space: nowrap;
        margin-right: 10px;
      }
      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;
        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }
  .edit-main {
    display: flex;
    margin-top: 30px;
    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;
      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }
      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }
        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
</style>
<style lang="scss">
.el-input--suffix {
  width: 135px !important;
}
.el-date-editor--datetimerange {
  width: 260px !important;
}
.el-checkbox-group {
  display: flex;
  flex-direction: column;
}
</style>