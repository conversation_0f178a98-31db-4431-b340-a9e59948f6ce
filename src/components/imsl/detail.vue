<template>
  <div class="container" style="padding-bottom:20px" v-loading="loading">
    <table border="1">
      <th>{{title}}</th>
      <template v-for="(item, index) in detail" :key="index">
        <tr>
          <td>
            <div>
              <img
                src="https://static.medsci.cn/public-image/ms-image/75b5af80-40ba-11ee-8e90-b931ae271702_user.png"
                alt=""
              />
              <span v-html='formatContent(item.dialogues[0].message)'> </span>
            </div>
          </td>
        </tr>
        <tr style="background-color: #eaeaea;">
          <td>
            <div>
              <img
                src="https://static.medsci.cn/public-image/ms-image/f5931a90-40b9-11ee-8e90-b931ae271702_jiqiren.png"
                alt=""
              />
              <span  v-html='formatContent(item.dialogues[1].message)'> </span>
            </div>
          </td>
        </tr>
      </template>
    </table>
  </div>
</template>

<script>
import { chatLogDetail } from "@/request/service.js";
export default {
  data() {
    return {
      detail: [],
      pageIndex: 1,
      pageSize: 500,
      loading:false,
      title:""
    };
  },
  mounted() {
    this.init();
  },
  methods: {
     
    //   格式化
    formatContent(text) {
      return text.replace(/\\n/g, "<br>");
    },
    // 初始查询
    init() {
        this.loading=true
      let params = {
        conversationId: this.$route.query.conversationId,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        userId: this.$route.query.userId,
      };
      chatLogDetail(params).then((res) => {
        this.detail = res.data;
        this.title= res.title
        this.loading=false
      });
    },
  },
};
</script>

<style lang="scss" scoped>
table {
  border-collapse: collapse;
  width: 100%;
  font-family: Arial, sans-serif;
  color: #333;
  text-align: center;
}

table th,
table td {
  padding: 15px;
  border: 1px solid #ccc;
  font-size: 16px;
  width: 40%;
  word-break: break-word;
  div {
    width: 40%;
    margin: 0 auto;
    text-align: left;
    display: flex;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
    }
    span {
      margin-top: 10px;
      display: inline-block;
    }
  }
}

table th {
  background-color: #f2f2f2;
  font-weight: bold;
  text-align: center;
  font-size: 20px;
  width: 40%;
}

table tr:nth-child(even) {
  background-color: #f9f9f9;
}

</style>