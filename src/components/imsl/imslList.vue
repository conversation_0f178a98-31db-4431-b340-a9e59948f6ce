<template>
  <div class="container_imslList">
    <div class="search">
      <div class="s_content" id="no1">
        <div style="display: flex">
          <div class="col">
            <el-input
              v-model="chatTopic"
              placeholder="请输入对话关键字"
            ></el-input>
          </div>
          <div class="col">
            <el-input
              v-model="chatName"
              placeholder="请输入用户名"
            ></el-input>
          </div>
        </div>
        <div class="search_btn">
          <el-button @click="search" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      style="width: calc(100% - 20px); margin: 30px 20px"
    >
      <el-table-column prop="id" label="序号" width="100" align="center"/>
      <el-table-column prop="userName" label="用户名" width="180" align="center"/>
      <el-table-column prop="chatTopic" label="问题">
        <template #default="scope">
          <el-link
            :href="`#/imsl/imslDetail?userId=${scope.row.userId}&conversationId=${scope.row.conversationId}`"
            :underline="false"
            type="primary"
            >{{ scope.row.chatTopic }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" width="180" align="center" />
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { chatLogPage } from "@/request/service.js";
export default {
  data() {
    return {
      tableData: [],
      chatName: "",
      chatTopic: "",
      pageSize: 20,
      currentPage: 1,
      total:0,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    // 重置
    reset() {
      this.chatName = "";
      this.chatTopic = "";
    },
    // 查询
    search() {
      this.init();
    },
    // 查询列表
    init() {
      let params = {
        chatName: this.chatName,
        chatTopic: this.chatTopic,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
      };
      chatLogPage(params).then((res) => {
        this.tableData = res.data;
        this.total = res.totalSize;
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
    background: #f8f8f8;
    display: flex;
    align-items: center;
    white-space: nowrap;
    .s_content {
      display: flex;
      align-items: flex-end;
      background: #fff;
      padding: 10px 20px;
      width: 100%;
      justify-content: space-between;
      flex-wrap: nowrap;
      .s_item {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }
      .search_btn {
        width: 130px;
      }
      ::v-deep .el-button {
        width: 60px;
        height: 20px !important;
        font-size: 12px;
        text-align: center;
        padding: 0;
      }
      ._mb {
        margin-right: 10px;
      }
    }
  }
  .container_imslList{
      .el-link.el-link--primary{
      color:#66b1ff ;
  }
  }
  
  .col{
      margin-right: 20px;
  }
</style>