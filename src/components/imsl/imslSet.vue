<template>
  <div class="container">
    <table border="1">
      <tr>
        <td>账户类型</td>
        <td><h3>企业版</h3></td>
      </tr>
      <tr>
        <td>有效期</td>
        <td>{{ ruleForm.endTime }} 到期</td>
      </tr>
      <tr>
        <td>iMSL数量（人）</td>
        <td>
          <div class="t_progress">
            <el-progress :percentage="progressPercentage" style="width: 50%;background:'red'" class="custom-progress">
              {{ ruleForm.imslSetNum ? ruleForm.imslSetNum : 0 }}人/{{
                ruleForm.imslAccountNum ? ruleForm.imslAccountNum : 0
              }}人
            </el-progress>
          </div>
        </td>
      </tr>
      <tr>
        <td><span style="color: red">*</span>可用范围</td>
        <td>
          <div>
            <div></div>
            <div class="t_edit" @click="edit">编辑</div>
          </div>
        </td>
      </tr>
      <tr>
        <td><span style="color: red">*</span>产品名称</td>
        <td>
          <div>
            <div class="t_text">{{ title }}</div>
            <div class="t_edit" @click="edit">编辑</div>
          </div>
        </td>
      </tr>
      <tr>
        <td><span style="color: red">*</span>产品介绍</td>
        <td>
          <div>
            <div class="t_text">{{ description }}</div>
            <div class="t_edit" @click="edit">编辑</div>
          </div>
        </td>
      </tr>
    </table>
    <el-dialog v-model="dialogVisible" v-if="dialogVisible" title="编辑">
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="产品名称" prop="title">
          <el-input
            v-model="ruleForm.title"
            placeholder="请输入≤14字的机器人的名字"
            class="no-icon-input"
            style="width:500px"
            suffix-icon=""
          />
        </el-form-item>
        <el-form-item label="产品介绍" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            style="width:300px"
            placeholder="请输入≤24字的欢迎语，或者机器人的介绍"
          />
        </el-form-item>
        <el-form-item label="可用范围" prop="scope">
          <el-radio-group v-model="ruleForm.scope" @change="changeP">
            <el-radio label="0" size="large">全部人员</el-radio>
            <el-radio label="1" size="large">部分人员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.scope == '1'">
          <el-button
            :disabled="search"
            v-if="
              initActive &&
              initActive.length == 0 &&
              ruleForm.userIds &&
              ruleForm.userIds.length == 0
            "
            type="primary"
            size="small"
            style="margin-left: 130px"
            @click="choice"
            >选择用户组</el-button
          >
          <div class="alreadyChoice" v-else>
            <span @click="choice" v-if="!search"> 已选择</span>
            <span v-else :disabled="true">已选{{ ruleForm.userCount }}人</span>
          </div>
        </el-form-item>
        <div class="btn_set">
          <el-button type="primary" @click="submitForm('ruleFormRef')"
            >保存</el-button
          >
        </div>
      </el-form>
    </el-dialog>
    <!-- 用户 -->
    <el-dialog v-model="dialogTableVisible" title="选择范围">
      <div style="display: flex">
        <el-tabs v-model="activeName" class="demo-tabs" style="width: 70%">
          <el-tab-pane label="用户组" name="first">
            <el-tree
              ref="treeRef"
              :data="data"
              show-checkbox
              default-expand-all
              node-key="id"
              :default-checked-keys="initActive"
              check-strictly="true"
              highlight-current
              :props="defaultProps"
              @check="checkTree"
              @check-change="handleNodeChange"
            />
          </el-tab-pane>
          <el-tab-pane label="用户" name="second">
            <el-input
              v-model="userName"
              class="w-50 m-2"
              placeholder="请输入用户名搜索，两个字及以上"
              :suffix-icon="Search"
            />
            <el-checkbox-group
              v-model="ruleForm.userIds"
              @change="changeChoice"
            >
              <el-checkbox
                style="display: block; margin-top: 20px"
                :label="item.userId"
                v-for="item in searchList"
                :key="item.userId"
                >{{ item.realName }}</el-checkbox
              >
            </el-checkbox-group>
          </el-tab-pane>
        </el-tabs>
        <div class="checkActive">
          <div class="checkActive_title">
            <div>已选择：</div>
            <div @click="deletes('all', ruleForm.roleIds, ruleForm.userIds)">
              清空
            </div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in ruleForm.roleIds"
            :key="index"
          >
            <div>{{ item.title }}</div>
            <div @click="deletes('one', item.id)">x</div>
          </div>
          <div
            class="checkActive_title"
            v-for="(item, index) in checkActive1"
            :key="index"
          >
            <div>{{ item.realName }}</div>
            <div @click="deletes('two', item.userId)">x</div>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button size="small" @click="handleDialogClose(true)"
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="addsub">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { roleList, userList, addImsl, imslDetail } from "@/request/service.js";
import { ElMessage } from 'element-plus';
export default {
  data() {
    return {
      ruleForm: {
        userIds: [],
        roleIds: [],
        description: "",
        scope: "0",
        title: "",
      },
      search: false,
      initActive: [],
      dialogTableVisible: false,
      dialogVisible: false,
      data: [],
      defaultProps: {
        children: "childRoles",
        label: "title",
      },
      userName: "",
      searchList: [],
      activeName: "first",
      checkActive1: [],
      rules: {
        title: [
          { required: true, message: "请输入产品名称", trigger: "blur" },
          { max: 14, message: "请输入≤14字的机器人的名字", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入产品介绍", trigger: "blur" },
          {
            max: 24,
            message: "请输入≤24字的欢迎语，或者机器人的介绍",
            trigger: "blur",
          },
        ],
        scope: [{ required: true, message: "请选择可用范围", trigger: "blur" }],
      },
      detail: {},
    };
  },
  computed: {
    progressPercentage() {
      if (this.ruleForm.imslAccountNum === 0) {
        return 0;
      }
      return (
        (this.ruleForm.imslSetNum / this.ruleForm.imslAccountNum) * 100
      );
    },
  },
  async mounted() {
    await this.init();
  },
  watch: {
    userName(val) {
      this.searchLiist();
    },
  },
  methods: {
    // 切换范围
    changeP(i) {
      if (i == 0) {
        this.initActive=[]
        this.$refs.treeRef.setCheckedKeys(this.initActive);
        this.ruleForm.roleIds = [];
        this.ruleForm.userIds = [];
        this.initActive = [];
        this.checkActive1 = [];
      }
    },

    // 初始回显
    async init() {
      let res = await roleList();
      let data = res.data.filter((res) => {
        return res.userNum > 0;
      });
      this.data = data;
      await this.detailSet();
    },
    // 有详情
    async detailSet() {
      this.initActive=[]
      let res1 = await imslDetail();
      this.ruleForm = res1.data;
      if (res1.data.title) {
        this.title = JSON.parse(JSON.stringify(this.ruleForm.title));
        this.description = JSON.parse(
          JSON.stringify(this.ruleForm.description)
        );
        this.ruleForm.scope = JSON.stringify(this.ruleForm.scope);
        if (this.ruleForm.userList && this.ruleForm.userList.length > 0) {
          this.ruleForm.userIds = this.ruleForm.userList.map((item) => {
            return item.userId;
          });
        } else {
          this.ruleForm.userIds = [];
        }
        if(this.$refs.treeRef){
          this.initActive=[]
        this.$refs.treeRef.setCheckedKeys(this.initActive);
        }
        
        this.initActive.splice(
          0,
          this.initActive.length,
          ...(this.ruleForm.roleIds || [])
        );
        this.checkActive1.splice(
          0,
          this.checkActive1.length,
          ...(this.ruleForm.userList || [])
        );
        if (this.ruleForm && this.ruleForm.roleIds) {
          if(this.$refs.treeRef){
          this.$refs.treeRef.setCheckedKeys(this.initActive);
          }
        } else {
          this.$refs.treeRef.setCheckedKeys([]);
        }
      } else {
        this.ruleForm = Object.assign({}, res1.data, {
          scope: "0",
          userIds: [],
        });
        this.$refs.treeRef.setCheckedKeys([]);
        this.initActive.splice(0, this.initActive.length);
        this.checkActive1.splice(0, this.checkActive1.length);
      }
    },
    // 保存
    submitForm(form) {
      this.$refs[form].validate(async (valid) => {
        if (valid) {
          if((this.ruleForm.scope==1&&this.ruleForm.roleIds&&!this.ruleForm.roleIds.length>0)&&(this.ruleForm.scope==1&&this.ruleForm.userIds&&!this.ruleForm.userIds.length>0)){
              ElMessage({
                message: '请选择用户组',
                type:"warning"
              })
              return
          }
          if (this.ruleForm.roleIds && this.ruleForm.roleIds.length > 0) {
            this.ruleForm.roleIds = this.ruleForm.roleIds.map((element) => {
              if (element.id) {
                return element.id;
              } else {
                return element;
              }
            });
          }
          let params = {
            description: this.ruleForm.description,
            roleIds:this.ruleForm.scope==1?this.ruleForm.roleIds:[] ,
            scope: this.ruleForm.scope,
            title: this.ruleForm.title,
            userIds: this.ruleForm.scope==1?this.ruleForm.userIds:[],
          };
          let res = await addImsl(params);
          if(res.status==200){
            this.dialogVisible = false;
             await this.init();
          }
          
        } else {
          return false;
        }
      });
    },
    setChildrenNode(node, state) {
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = state;
        this.setChildrenNode(node.childNodes[i], state);
      }
    },
    // 父级选中带着子集选中
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === "parent") {
            node[key].checked = true;
            this.setParentNode(node[key]);
          }
        }
      }
    },
    // 选择user
    changeChoice(e) {
      this.searchList.forEach((element) => {
        e.forEach((rows) => {
          if (element.userId == rows) {
            this.checkActive1.push(element);
          }
        });
      });
      this.checkActive1 = this.fn2(this.checkActive1);
      let a = this.checkActive1.map((element) => {
        return element.userId;
      });
      if (a.length != e.length) {
        a.forEach((element, index) => {
          if (e.indexOf(element) == -1) {
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    // 编辑
    async edit() {
      this.dialogVisible = true;
      await this.detailSet();
    },

    //   复选框选择
    checkTree(e, nodes) {
      const node = this.$refs.treeRef.getNode(e.id);
      this.setNode(node);
      this.setActive();
    },
    //   查询用户列表
    searchLiist() {
      if (this.userName.trim() && this.userName.length >= 2) {
        userList(this.userName).then((res) => {
          this.$nextTick(() => {
            this.searchList = res.data;
          });
        });
      }
    },
    fn2(arr) {
      const res = new Map();
      return arr.filter(
        (arr) => !res.has(arr.userId) && res.set(arr.userId, arr.userId)
      );
    },
    //   删除
    deletes(type, item, item1) {
      if (type == "all") {
        if (item && item.length > 0) {
          item.forEach((element) => {
            this.$refs.treeRef.setChecked(element.id, false, true);
          });
        }
        this.ruleForm.userIds = [];
        this.checkActive1 = [];
        this.setActive();
      } else if (type == "one") {
        this.$refs.treeRef.setChecked(item, false, true);
        this.setActive();
      } else {
        this.ruleForm.userIds.forEach((element, index) => {
          if (item == element) {
            this.ruleForm.userIds.splice(index, 1);
            this.checkActive1.splice(index, 1);
          }
        });
      }
    },
    setNode(node) {
      if (node.checked) {
        //当前是选中,将所有子节点都选中
        this.setChildrenNode(node, node.checked);
        this.setParentNode(node);
      } else {
        //当前是取消选中,将所有子节点都取消选中
        this.setChildrenNode(node, node.checked);
      }
    },
    handleNodeChange(data, checked, deep) {
      if (this.initActive.includes(data.id)) {
        if (!checked) {
          this.initActive = this.initActive.filter((id) => {
            return id != data.id;
          });
        }
      } else {
        if (checked) {
          this.initActive.push(data.id);
        }
      }
      if (data.childRoles.length > 0) {
        return false;
      }
    },
    //   设置右边显示的数据
    setActive() {
      this.initActive = this.$refs.treeRef.getCheckedKeys(false);
      this.ruleForm.roleIds = this.$refs.treeRef.getCheckedNodes(false, true);
    },
    addsub() {
      this.handleDialogClose(false);
      this.activeName = "first";
      this.userName = "";
      this.searchList = [];
    },

    handleDialogClose(done) {
      if (done) {
        setTimeout(async () => {
          this.dialogTableVisible = false;
          this.activeName = "first";
          this.userName = "";
          this.searchList = [];
          await this.init();
        }, 0);
      } else {
        this.dialogTableVisible = false;
      }
    },
    //选择用户组
    choice() {
      this.dialogTableVisible = true;
      this.$nextTick((vm) => {
        this.setActive();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  ::v-deep {
    .el-input__inner,
    .el-textarea__inner {
      width: 400px;
    }
  }
  .alreadyChoice {
    margin-left: 130px;
    color: rgb(2, 167, 241);
  }
  table {
    border-collapse: collapse;
    width: 60%;
    .t_progress {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .t_text {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .t_edit {
      color: rgb(64, 158, 255);
      font-size: 14px;
    }
    .t_edit:hover {
      cursor: pointer;
    }
    tr {
      > td:first-child {
        width: 35%;
      }
      td {
        border: 1px solid #e2e2e2;
        padding: 8px;
        text-align: center;
        > div {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
  .checkActive {
    width: 30%;
    height: 250px;
    overflow: auto;
    margin-top: 40px;
    margin-left: 10px;
    background: #f2f2f2;
    padding: 20px 20px;
    &_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      > div:last-child:hover {
        cursor: pointer;
      }
      > div:last-child {
        width: 40px;
        text-align: center;
      }
    }
  }
  .btn_set {
    width: 400px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .demo-ruleForm {
    margin: 50px 0 100px;
    .no-icon-input {
      ::v-deep .el-input__suffix {
        display: none;
      }
    }
  }
}
.custom-progress{
  ::v-deep .el-progress-bar__inner {  
    background-color: red; /* 设置进度条轨道的颜色 */  
  }  
}
</style>