<template>
  <div class="container">
    <div class="search">
      <div class="s_content" id="no1">
        <div style="display: flex">
          <div class="col">
            <el-input v-model="docsName" placeholder="请输入文件名"></el-input>
          </div>
        </div>
        <div class="search_btn">
          <el-button @click="search" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div></div>
    </div>
    <div class="up">
      <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        :show-file-list="false"
        :before-upload="handlePreview"
        accept=".md, .pdf"
      >
        <el-button type="primary">上传</el-button>
      </el-upload>
    </div>
    <div class="notes">
      <span>已上传:</span>
      <span>
        格式：Markdown，PDF<br />

        文档可以是书籍、论文，但请务必保证内容的准确性和合规性
      </span>
    </div>
    <el-table
      :data="tableData"
      border
      style="width: calc(100% - 20px); margin: 10px 20px"
    >
      <el-table-column prop="id" label="序号" align="center" width="100" />
      <el-table-column prop="docsName" label="文件名" align="center" />
      <el-table-column prop="createdBy" label="创建人" align="center" width="180"/>
      <el-table-column prop="createdTime" label="创建时间" align="center" width="180"/>
      <el-table-column label="操作" align="center">
        <template #default="scope">

          <el-button
            size="mini"
            type="primary"
            plain
            @click="down(scope.row.docsPath, scope.row.docsName,scope.row.docsType)"
            >下载</el-button
          >
          <el-button
            size="mini"
            type="primary"
            plain
            :disabled="scope.row.approvalStatus != 0 ? true : false"
            @click.stop="handleAudit(scope.$index, scope.row,scope.row.docsType)"
            >审核</el-button
          >
          <el-button
            size="mini"
            type="danger"
            plain
            @click.stop="handleDelete(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script >
// 知识库上传文件
// docsRecordPage:"/medsciImsl/docsRecordPage",
// // 知识库审核文件
// docsAudit:"/medsciImsl/docsAudit",
// // 知识库删除
// docsDelete:"/medsciImsl/docsDelete",
import {
  ossToken,
  docsRecordPage,
  docsDelete,
  docsUpload,
} from "@/request/service.js";
import {
  docsAudit,
} from "@/request/serviceLong.js";
import COS from "cos-js-sdk-v5";
export default {
  data() {
    return {
      tableData: [
        {
          id: 1,
          name: "测",
          question: "阿叔u爱仕达UI大海对啊卜",
          time: "2020-2121",
        },
        {
          id: 1,
          name: "测",
          question: "阿叔u爱仕达UI大海对啊卜",
          time: "2020-2121",
        },
        {
          id: 1,
          name: "测",
          question: "阿叔u爱仕达UI大海对啊卜",
          time: "2020-2121",
        },
      ],
      currentPage: 1,
      pageSize: 20,
      userId: "",
      total: 100,
      uploadYear: new Date().getFullYear(),
      uploadMouth:
        new Date().getMonth() < 10
          ? "0" + (new Date().getMonth() + 1)
          : new Date().getMonth() + 1,
      uploadDate:
        new Date().getDate() < 10
          ? "0" + new Date().getDate()
          : new Date().getDate(),
      arrImg: [],
      docsName: "",
    };
  },
  mounted() {
    this.init();
    this.userId = this.$store.state.backInfo.userInfo.userId;
  },
  methods: {
    // 重置
    reset() {
      this.docsName = "";
    },
    // 查询
    search() {
      this.init();
    },
    // 审核
    handleAudit(index, row) {
      let params = {
        doc_name: row.docsName,
        id: row.id,
      };
      docsAudit(params).then((res) => {
        if (res.status === 200) {
          this.init();
          this.$message({
            type: "success",
            message: "审核成功!",
          });
        }
      });
    },
    // 删除单条
    handleDelete(index, row) {
      this.$confirm(`是否删除${row.docsName}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          doc_name: row.docsName,
          id: row.id,
        };
        docsDelete(params).then((res) => {
          if (res.status === 200) {
            this.init();
            this.$message({
              type: "success",
              message: "删除成功!",
            });
          }
        });
      });
    },
    // 初始查询
    init() {
      let params = {
        docsName: this.docsName,
        pageSize: this.pageSize,
        pageIndex: this.currentPage,
      };
      docsRecordPage(params).then((res) => {
        this.tableData = res.data;
        this.total = res.totalSize;
      });
    },
    // 上传文件
    async handlePreview(files, a) {
      const response = await ossToken({ type: 1 });

      
      let aliOssFile = files;
      let fileType;
      let docsType;
      let docsName;
      docsName = files.name.substr(0, files.name.lastIndexOf("."));
        console.log(files.type)

      switch (files.type) {
        case "application/pdf":
          docsType = "pdf";
          fileType = aliOssFile.type.replace("application/", "");
          break;
        case "application/json":
          docsType = "json";
          fileType = aliOssFile.type.replace("application/", "");
          break;
        case "":
          docsType = "md";
          fileType = aliOssFile.type.replace("", "md");
          break;
        case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          fileType = aliOssFile.type.replace(
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "docx"
          );
          break;
      }
      let fileName = "";
      if (window.location.href.includes("Bioon")) {
        fileName = `bioon-com/${this.uploadYear}${this.uploadMouth}${
          this.uploadDate
        }/${new Date().getTime()}_${this.userId}.${fileType}`;
      } else {
        fileName = `/${window.location.host}/${new Date().getTime()}_${this.userId}.${fileType}`;
      }
       const cos = new COS({
        SecretId: response.data.accessKeyId,
        SecretKey: response.data.accessKeySecret,
        XCosSecurityToken:response.data.securityToken
      });
      cos.uploadFile(
        {
          Bucket: response.data.publicBucketName,
          Region: "ap-shanghai",
          Key: fileName,
          Body: files,
        },
        (err, data) => {
          if (err) {
            this.imgUploadFlag = false;
          } else {
             let imgUrl ='https://'+data.Location.replace(data.Location.split('/')[0], this.cosUrl);
          let params = {
            docsName: docsName,
            docsPath: imgUrl,
            docsType: docsType,
          };
          if(imgUrl){
            docsUpload(params).then((res) => {
            if(res.status=="200"){
              setTimeout(() => {
                 this.init();
              }, 1000);
            }
          });
          }
          }
        }
      );

    },
    handleRemove() {},
    handleExceed() {},
    beforeRemove() {},

    // 下载文件
    down(url, filename,docsType) {
      fetch(url)
        .then((response) => response.blob())
        .then((blob) => {
          const blobUrl = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = blobUrl;
          link.download = filename+'.'+docsType;
          link.click();
          URL.revokeObjectURL(blobUrl);
        })
        .catch((error) => {
          console.error("下载文件时发生错误:", error);
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  background: #f8f8f8;
  display: flex;
  align-items: center;
  white-space: nowrap;
  .s_content {
    display: flex;
    align-items: flex-end;
    background: #fff;
    padding: 10px 0 10px 20px;
    width: 100%;
    justify-content: space-between;
    flex-wrap: nowrap;
    .s_item {
      display: flex;
      align-items: center;
      margin-right: 20px;
    }
    .search_btn {
      width: 130px;
    }
    ::v-deep .el-button {
      width: 60px;
      height: 20px !important;
      font-size: 12px;
      text-align: center;
      padding: 0;
    }
    ._mb {
      margin-right: 10px;
    }
  }
}
.up {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.notes {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-left: 20px;
  margin-top: 10px;
  span {
    font-size: 14px;
  }
  span:last-child {
    color: #999999;
  }
}
.container{
  overflow: hidden;
}
</style>