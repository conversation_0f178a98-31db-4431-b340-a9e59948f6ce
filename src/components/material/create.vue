<template>
    <section>
        <el-tabs v-model="activeName">
            <el-tab-pane label="表单内容" name="design">
                <el-form
                    ref="formname"
                    style="text-align: left;"
                    :model="form"
                >
                    <el-form-item prop="formName"
                        :rules="[
                            { required: true, message: '请输入调研  / 问卷 / 表单标题', trigger: 'blur' },
                        ]"
                    >
                        <el-input v-model="form.formName" placeholder="调研 / 问卷 / 表单标题"></el-input>
                    </el-form-item>
                </el-form>
                <div class="form-design-style">
                  <div id="avueForm" ref="avue"></div>
                    <!-- <avue-form-design ref="avue" :options="options" :includeFields="includeFields"></avue-form-design> -->
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 提交按钮 -->
        <footer-tool-bar v-loading="buttonLoading">
            <template v-slot:tool-content>
                <el-button type="primary" @click="info_operation('save')"
                    >保存</el-button
                >
                <el-button type="info" @click="info_operation('back')"
                    >返回</el-button
                >
            </template>
        </footer-tool-bar>
    </section>
</template>
<script>
import FooterToolBar from "@/components/ToolBar/footer-tool-bar"
import { loadMicroApp, initGlobalState } from "qiankun";
import { saveForm , updateForm } from "@/request/service.js";

export default {
    name: "formMenu",
//     components: {
//       avueFormDesign
//       avueFormDesign  from 
//   },
     props: {
        edit: {
        type: Object,
        default: () => {},
        },
     },
    data() {
        return {
            // 所选tab
            activeName: "design",
            buttonLoading: false,
            // 表单属性
            form: {
                formName: "", // 名字
                content: {},
            },
            options: {},
            // includeFields: ['group', 'dynamic', 'title', 'input', 'password', 'textarea', 'number', 'url', 'array', 'img', 'radio', 'checkbox', 'select', 'cascader', 'upload', 'year', 'month', 'week', 'date', 'time', 'datetime', 'daterange', 'timerange', 'datetimerange', 'icon', 'switch', 'rate', 'slider', 'color']
            includeFields: ['dynamic', 'title', 'input', 'password', 'textarea', 'number', 'url', 'array', 'img', 'radio', 'checkbox', 'select', 'cascader', 'upload', 'signature', 'region', 'sort', 'proportion', 'year', 'month', 'week', 'date', 'time', 'datetime', 'daterange', 'timerange', 'datetimerange', 'icon', 'switch', 'rate', 'slider', 'color'],
            microApp: null,
            dataValues: {},
            globalState: null
        }
    },
    components: {
        FooterToolBar,
    },
    async mounted() {
        window.sessionStorage.removeItem("formDesignData")
        this.init()
        await this.initForm()
    },
    beforeUnmount() {
      if (this.microApp) {
        this.microApp.unmount();
      }
    },
    methods: {
        async initForm() {
          if (this.microApp) {
            this.microApp.unmount();
          }
          this.$nextTick(() => {
            let url =
              process.env.NODE_ENV == "production"
                ? `//${window.location.host}/msFormDesgin/`
                : "http://localhost:7101/";
            this.microApp = loadMicroApp({
              name: "avue",
              entry: url,
              container: "#avueForm",
              props: {
                options: this.options,
                includeFields: this.includeFields,
                dataValues: {}
              },
            });
            this.globalState = initGlobalState({
              setData: false
            })
          });
        },
        async init() {
            // const { formId } = this.edit;
            if (this.edit) {
                // const res = await this.api.formService_formDetails(this.edit)
                // if (res.code != 'SUCCESS') {
                //     return this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                // }
                this.form = {
                  formName: this.edit.formName,
                  content: this.edit.formContent ? this.edit.formContent : {}
                }
                String.prototype.toFunction = function() {
                    return eval('(' + this + ')')
                }
                let json = this.edit.formContent ? this.edit.formContent : {}
                if(json.column && json.column.length) {
                  json.column.forEach(item => {
                    if(item.change) {
                        item.change = item.change.toFunction()
                    }
                    if(item.click) {
                        item.click = item.click.toFunction()
                    }
                    if(item.focus) {
                        item.focus = item.focus.toFunction()
                    }
                    if(item.blur) {
                        item.blur = item.blur.toFunction()
                    }
                  })
                }
                if(json.group && json.group.length) {
                  json.group.forEach(row => {
                    if(row.column && row.column.length) {
                      row.column.forEach(item => {
                        if(item.change) {
                          item.change = item.change.toFunction()
                          }
                          if(item.click) {
                              item.click = item.click.toFunction()
                          }
                          if(item.focus) {
                              item.focus = item.focus.toFunction()
                          }
                          if(item.blur) {
                              item.blur = item.blur.toFunction()
                          }
                      })
                    }
                  })
                }
                this.options = json

            }
        },
        // 保存
        async handleSave(values) {
            if (!values && !values.column.length && !values.group) {
                this.$message.warning("请先设计模板！")
                return
            }
            this.buttonLoading = true;
            let json = values
            if(json.column && json.column.length) {
              json.column.forEach(item => {
                if(item.change) {
                    item.change = item.change.toString()
                }
                if(item.click) {
                    item.click = item.click.toString()
                }
                if(item.focus) {
                    item.focus = item.focus.toString()
                }
                if(item.blur) {
                    item.blur = item.blur.toString()
                }
              })
            }
             if(json.group && json.group.length) {
               json.group.forEach(row => {
                 if(row.column && row.column.length) {
                   row.column.forEach(item => {
                    if(item.change) {
                      item.change = item.change.toString()
                      }
                      if(item.click) {
                          item.click = item.click.toString()
                      }
                      if(item.focus) {
                          item.focus = item.focus.toString()
                      }
                      if(item.blur) {
                          item.blur = item.blur.toString()
                      }
                  })
                 }
              })
             }
            if (this.edit) {
                const params = {
                content: json,
                formId:this.edit.formId,  
                formName:this.form.formName,
                // createdId: this.$store.state.backInfo.userInfo.userId,
                // createdName: this.$store.state.backInfo.userInfo.userName,
                projectId: this.$store.state.projectInfo.id
            }
                const res = await updateForm(params)
                if (res.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                    return
                }
            } else {
                const params = {
                content: json,
                formName:this.form.formName,
                createdId: this.$store.state.backInfo.userInfo.userId,
                createdName: this.$store.state.backInfo.userInfo.userName,
                projectId: this.$store.state.projectInfo.id
            }
                const res = await saveForm(params)
                if (res.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                    return
                }
            }
            this.buttonLoading = false;
            window.location.reload();
            this.$emit("back", {});
            this.$router.push('/form')
            this.form.formName=''
        },
        // 按钮
         info_operation(val) {

            switch (val) {
              case "save":
                  this.$refs.formname.validate(async (valid) => {
                      if (valid) {
                        // const values = await this.$refs.avue.getData()
                        // this.microApp.mount({ props: { getData: true } });
                        // let data = await this.micr.oApp.mount({ props: { getData: true } });
                        this.globalState.setGlobalState({
                          setData: true
                        })
                          setTimeout(()=>{
                            let data = window.sessionStorage.getItem("formDesignData")
                             this.handleSave(JSON.parse(data))
                          }, 100)
                      }
                  })
                  break
              case "back":
                //   this.$router.back()
                 this.$emit("back", {});
                 break
              default:
                  break
            }
        },
    },
}
</script>

<style scoped lang="scss">
.form-design-style {
    // width: 100%;
    // height: calc(100vh);
    // border-radius: 4px;
    // border: 1px solid #DCDFE6;
    // text-align: left;
}
#avueForm {
  padding-bottom: 50px;
}
#avueForm ::v-deep .el-form-item__content, .el-drawer__wrapper ::v-deep .el-form-item__content {
  margin-left: 0 !important;
}
#avueForm ::v-deep .el-input--small, .el-drawer__wrapper ::v-deep .el-input--small{
  width: 100% !important;
}
#avueForm ::v-deep .widget-form-list {
    width: 100% !important;
}
</style>
