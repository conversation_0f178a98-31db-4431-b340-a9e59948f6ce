<template>
  <div class="yxd_information">
    <div id="container" class="container" :class="{ isScroll: isScroll }" ref="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">标题</span>
            <el-input v-model="title" placeholder="请输入"></el-input>
          </div>
          <!-- <div class="col">
          <span class="tl">创建人</span>
          <el-select v-model="createUserId" placeholder="请选择">
            <el-option
              v-for="item in classGroup"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div> -->
          <div class="col">
            <span class="tl">创建时间</span>
            <el-date-picker v-model="lineTime" type="datetimerange" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </div>
          <!-- 筛选权限 -->
          <!-- <div class="col" v-if="true">
            <span class="tl">权限</span>
            <el-cascader
            :options="RoleList"
            ref="cascader"
            collapse-tags	
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles',
            multiple: true
            }"
            clearable
            >
          </el-cascader> 
          </div> -->
          <!-- 筛选分类 -->
          <!-- <div class="col" v-if="true">
            <span class="tl">分类</span>
            <el-cascader
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div> -->
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="init()">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <div>
          <el-button v-if="btnList.form_add" @click="goTab" type="primary" icon="el-icon-plus">表单添加</el-button>
          <!-- <el-button v-if="true" @click="operation_change({operation: domConfig.soltButtons[0]})" type="primary" icon="el-icon-plus">表单添加</el-button> -->
          <!-- <el-button v-if="true" @click="setPower">批量设置权限</el-button>
          <el-button v-if="true" @click="setClass">批量设置分类</el-button> -->
          <el-button v-if="btnList.batch_form_audit_on" @click="lineBatch">批量审核</el-button>
          <el-button v-if="btnList.batch_form_audit_off" @click="offLineBatch">批量去审</el-button>
          <!-- <el-button v-if="true" @click="deleteClass" icon="el-icon-close">批量删除</el-button> -->
        </div>
        <div class="operate-right">
          <el-alert class="article-total" :title="`共搜索到${total}个表单`" type="info" show-icon></el-alert>
        </div>
      </div>
      <div class="table">
        <el-table ref="multipleTable" border :data="tableData" :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column sortable prop="id" label="ID" width="120" align="center">
          </el-table-column>
          <el-table-column prop="formName" label="标题" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="createUserName" label="创建人" width="120" align="center">
          </el-table-column>
          <el-table-column prop="createAt" label="创建时间" sortable width="200" align="center">
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="scope">
              <label class="status-label" :style="{background: scope.row.status == 'PASS' ?  'rgb(64, 162, 63)' : 'rgb(167, 173, 189)' }"></label>
              <span :class="{ start: scope.row.status == 'PASS' }" class="default"></span>
              <span>{{ scope.row.status==='PASS'?'审核通过':'待审核' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="300" fixed="right">
            <template #default="scope">
              <!-- <el-button v-if="btnList.edit_btn" size="mini" type="primary" :disabled="scope.row.projectId != projectId"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
              <el-button v-if="btnList.form_update" size="mini" @click="goTab(scope.$index, scope.row)">
                编辑
              </el-button>
              <el-button v-if="btnList.form_audit" size="mini" @click="handleDown(scope.$index, scope.row)">{{
                  scope.row.status === "PASS" ? "去审" : "审核"
              }}</el-button>
              <el-button v-if="btnList.remove_btn" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
      <!-- 编辑弹出框 -->
      <!-- <el-dialog title="批量设置权限" v-model="powerVisible" width="70%">
        <div class="power-head">
          <table>
            <tr class="line">
              <td class="title">权限属性</td>
              <td>
                <el-radio-group v-model="radio" fill="#797979" text-color="#797979">
                  <el-radio :label="1">设置权限</el-radio>
                  <el-radio :label="0">取消权限</el-radio>
                </el-radio-group>
              </td>
            </tr>
            <tr valign="top">
              <td class="title user">用户组</td>
              <td>
                <el-checkbox-group v-model="checkList" @change="changeCheck">
                  <el-checkbox v-for="item in RoleList" :key="item.id" :label="item.id + ':' + item.title">{{
                      item.title
                  }}
                  </el-checkbox>
                </el-checkbox-group>
              </td>
            </tr>
          </table>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="powerVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="savePower" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog> -->
       <!-- 批量设置权限弹框 -->
      <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog>
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
      <!-- <el-dialog title="批量设置分类" v-model="editVisible" width="70%">
        <div class="edit-head">
          <div class="left">
            <span>已选分类</span>
            <div class="edit-tag">
              <el-tag v-for="tag in tags" :key="tag.tagName" closable size="mini" :type="tag.type">
                {{ tag.tagName }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="edit-main">
          <div class="title">
            <span>可选分类</span>
            <div class="group"></div>
          </div>
          <div class="title" v-for="(item, index) in classGroup" :key="index">
            <div class="group">
              <div class="btn" @click="item.checked = !item.checked" :class="{ checked: item.checked }">
                {{ item.tagName }}
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog> -->
      <el-dialog title="批量审核" v-model="onlineVisible" width="70%">
        <h3>确定审核这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.formName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量去审" v-model="offLineVisible" width="70%">
        <h3>确定去审这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.formName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span>是否{{ lineText == "审核提示" ? "审核" : "去审" }}
          <el-tag>{{ currentChose.formName }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="lineSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog  title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.formName }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { ElMessage } from "element-plus";
import {  formMaterial, formBatchDelete, tagTypeList, getRoleList, setDelePower,query,listTag,formBatchAudit } from "@/request/service.js";
import { exportUserBehavior } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import formMixin from "../../mixins/form-mixin"
import tableMixins  from "../../mixins/table"
import  controlDialog  from '../controlDialog.vue'
import  classifyDialog  from '../classifyDialog.vue'
export default {
  name: "material",
  mixins: [initModule, initScroll,formMixin,tableMixins],
  components:{
    controlDialog,
    classifyDialog
  },
  data() {
    return {
      title: "",
      lineTime: "",
      currentPage: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      classGroup: [],
      // powerVisible: true,
      editVisible: false,
      settingVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      deleteVisible: false,
      lineText: "上线提示",
      currentChose: {},
      RoleList: [],
      choseData: [],
      ClassifyData:[],
      radio: 1,
      selectPower: '',
      // checkList: [],
      selectvalue: [],
      ClassifyValue: [],
      createUserId:''
    };
  },
  mounted() {
    this.init();
    // this.getPowerData()
    // this.getClassifyData()
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
    projectId() {
      return this.$store.state.projectInfo.id;
    },
  },
  methods: {
    set(){
      this.init()
    },
    //  点击权限多选框选项
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    }, 
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    init() {
      this.tableData=[]
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        formName: this.title,
        createUserId:this.createUserId,
        startTime: publishedStartTime,
        endTime: publishedEndTime,
        currentPageNo: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      formMaterial(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll();
          // this.tableData.forEach((val) => {
          //   if (val.approvalStatus == 1) {
          //     val.approvalStatus = "已上线";
          //   } else {
          //     val.approvalStatus = "已下线";
          //   }
          //   val.gotoUrl =
          //     `https://` +
          //     window.location.host +
          //     `/news/detail/${val.encodeId}`;
          // });
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'guider',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
  getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    changeCheck(val) {
    },
    // initTag() {
    //   let params = {
    //     tagIds: [],
    //     tagType: "article",
    //   };
    //   tagTypeList(params).then((res) => {
    //     if (res.status == 200) {
    //       this.classGroup = res.data;
    //       this.classGroup.forEach((val) => {
    //         val.checked = false;
    //       });
    //     }
    //   });
    // },
    goTab(index,item) {
      this.$emit("go", item);
    },
    // 批量设置权限
    setPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }else{
       this.$refs.controlRef.openDialog('guider')
      }
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog('guider')
      this.$refs.classifyRef.gitClassList()
      // this.initTag();
      // this.editVisible = true;
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData=this.choseData.filter(item=>item.status === 0)
      this.settingVisible = true;
    },
    // saveEdit() {
    //   let ids = [];
    //   let tagDtoList = [];
    //   this.tags.forEach((d) => {
    //     tagDtoList.push({
    //       tagId: d.tagId,
    //       tagName: d.tagName,
    //     });
    //   });
    //   this.choseData.forEach((val) => {
    //     ids.push(val.id);
    //   });
    //   let params = {
    //     type: 4,
    //     ids: ids,
    //     tagDtoList: tagDtoList,
    //   };
    //   batchEdit(params).then((res) => {
    //     if (res.status == 200) {
    //       this.init();
    //       this.editVisible = false;
    //     }
    //   });
    // },
     // 批量删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        guiderIds: ids,
      };
      formBatchDelete(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
    // 批量上线按钮
    saveOnlineEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:"PASS"
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量上线成功");
          this.init();
          this.onlineVisible = false;
        }
      });
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:"WAITING"
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量下线成功");
          this.init();
          this.offLineVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.title = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    handleEdit(index, row) {
      this.$emit("go", row);
    },
    handleDown(index, row) {
      if (row.status == "PASS") {
        this.lineText = "去审提示";
      } else {
        this.lineText = "审核提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    // 指南导出
    deriveData(index, row) {
      exportUserGuider(row.id).then((res) => {
        if (res.size > 57) {
          let name = row.title+`指南详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
        // if (res.status == 200) {
        //   const blob = res.data;
        //   const reader = new FileReader();
        //   reader.readAsDataURL(blob);
        //   reader.onload = (e) => {
        //     const a = document.createElement('a');
        //     a.download = `文件名称.zip`;
        //     // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; 		   filename=\"20181211191944.zip\"",
        //     a.href = e.target.result;
        //     document.body.appendChild(a);
        //     a.click();
        //     document.body.removeChild(a);
        //   };
        // }
      });
    },
    exportNoLike(index, row) {
      // this.deleteVisible = true;
      // this.currentChose = row.userHits;
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    // 上线/下线
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:this.lineText == "审核提示" ? 'PASS' : 'WAITING',
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "审核提示"? ElMessage.success("审核成功"):ElMessage.success("去审成功")
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    // 单条删除
    deleteSave() {
      let params = {
        projectId: this.$store.state.projectInfo.id,
        id: this.currentChose.id,
        formId:this.currentChose.formId
      };
      formBatchDelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;  
          this.init();
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_information {
//  ::v-deep {
//     .el-cascader-panel {
//       background-color: #ff0;
//     }
//   }
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }
  .operate {
    .operate-right{
       flex: 1;
      .article-total {
       width: 227px !important;
       float: right ;
       border-radius: 4px;
     }
    }
  }
  .table {
    .status-label{
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 6px;
      display: inline-block;
      position: relative;
      top: 1px;
    }
  }
  .power-head {
    table {
      .line {
        height: 45px;
      }
      tr {
        padding: 30px 0;
        .title {
          width: 70px;
          // font-size: 20px;
        }
      }
    }
  }
  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 58.333%;
      display: flex;
      align-items: center;
      span {
        white-space: nowrap;
        margin-right: 10px;
      }
      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;
        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }
  .edit-main {
    display: flex;
    margin-top: 30px;
    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;
      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }
      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }
        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
</style>
<style lang="scss">
.el-input--suffix {
  width: 135px !important;
}
.el-date-editor--datetimerange {
  width: 260px !important;
}
.el-checkbox-group {
  display: flex;
  flex-direction: column;
}
</style>