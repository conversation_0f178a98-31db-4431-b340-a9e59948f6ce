<template>
  <div>
    <el-tooltip effect="dark" content="选择图片" placement="bottom">
      <el-button
        icon="el-icon-picture"
        size="mini"
        circle
        @click="dialogVisible = true"
        :disabled="disabled"
      ></el-button>
    </el-tooltip>
    <el-dialog
      v-model="dialogVisible"
      width="50%"
      title="图片上传"
      @close="dialogVisible = false"
      :append-to-body="false"
    >
      <el-upload
        class="avatar-uploader"
        action="#"
        :show-file-list="false"
        :http-request="customRequest"
        :before-upload="beforeAvatarUpload"
      >
        <img v-if="cover" :src="cover" class="avatar" />
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>

      <div class="footer">
        <el-button size="mini" @click="save" type="primary">保存</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import { uploadAvatar } from "@/request/service.js";
import msUpload from "@/components/ms-upload/index";
export default {
  name: "EditorImg",
  components: {
    msUpload,
  },
  data() {
    return {
      dialogVisible: false,
      cover: "",
    };
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.cover = "";
      }
    },
  },
  methods: {
    save() {
      this.dialogVisible = false;
      this.$emit("successCBK", this.cover);
    },
    getImgUrl(data) {
      const arr = Object.keys(data).map((v) => data[v]);
      this.$emit("successCBK", arr);
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        ElMessage.error("上传头像图片大小不能超过 2MB!");
        return false;
      } else {
        return true;
      }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    customRequest(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.cover = res.data.url;
          }
        });
      });
    },
  },
};
</script>
<style scoped lang="scss">
.footer {
  display: flex;
  justify-content: flex-end;
}
::v-deep .avatar-uploader {
  .el-upload--text {
    width: 120px;
    height: 120px;
    line-height: 120px;
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
</style>
