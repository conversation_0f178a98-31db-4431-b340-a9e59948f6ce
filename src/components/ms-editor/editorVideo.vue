<template>
  <div>
    <el-tooltip effect="dark" content="选择视频" placement="bottom">
      <el-button
        icon="el-icon-video-camera"
        size="mini"
        circle
        @click="dialogVisible = true"
        :disabled="disabled"
      ></el-button>
    </el-tooltip>

    <el-dialog
      v-model="dialogVisible"
      width="50%"
      title="视频上传"
      @close="dialogVisible = false"
      :append-to-body="false"
    >
      <el-tabs v-model="videoAction" style="margin-bottom: 20px;">
        <el-tab-pane label="上传视频" name="first">
          <el-upload
            class="video-uploader"
            action="#"
            :show-file-list="false"
            :http-request="customRequest"
            :before-upload="beforeVideoUpload"
          >
            <video v-if="videoUrl" :src="videoUrl" class="video-preview" controls></video>
            <div v-else class="upload-placeholder">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将视频拖到此处，或<em>点击上传</em></div>
            </div>
          </el-upload>
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="progress-container">
            <el-progress :percentage="uploadProgress" :stroke-width="18" status="success" />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="视频地址" name="third">
          <el-input
            v-model="videoUrlInput"
            placeholder="请输入视频地址"
            style="margin-bottom: 20px;"
          ></el-input>
          <div v-if="videoUrlInput" class="preview-container">
            <p>视频预览：</p>
            <video :src="videoUrlInput" class="video-preview" controls></video>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div class="footer">
        <el-button size="mini" @click="save" type="primary" :disabled="uploading">保存</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import { ossToken } from "@/request/service.js";
import COS from "cos-js-sdk-v5";

export default {
  name: "EditorVideo",
  data() {
    return {
      dialogVisible: false,
      videoUrl: "",
      videoUrlInput: "",
      videoAction: "first",
      uploadProgress: 0,
      uploading: false,
      ossData: {}
    };
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.videoUrl = "";
        this.videoUrlInput = "";
        this.getOssToken();
      }
    },
  },
  methods: {
    save() {
      let videoList = [];
      
      if (this.videoAction === "first" && this.videoUrl) {
        videoList.push({
          url: this.videoUrl
        });
      } else if (this.videoAction === "third" && this.videoUrlInput) {
        videoList.push({
          url: this.videoUrlInput
        });
      } else {
        ElMessage.warning("请先上传或输入视频地址");
        return;
      }
      
      this.dialogVisible = false;
      this.$emit("successCBK", videoList);
    },
    
    beforeVideoUpload(file) {
      const isVideo = file.type === 'video/mp4' || file.type === 'video/quicktime';
      const isLt100M = file.size / 1024 / 1024 < 100;
      
      if (!isVideo) {
        ElMessage.error("请上传mp4或mov格式的视频!");
        return false;
      }
      if (!isLt100M) {
        ElMessage.error("视频大小不能超过100MB!");
        return false;
      }
      return true;
    },
    
    async getOssToken() {
      try {
        const response = await ossToken({ type: 1 });
        this.ossData = {
          SecretId: response.data.accessKeyId,
          SecretKey: response.data.accessKeySecret,
          XCosSecurityToken: response.data.securityToken,
          Bucket: response.data.publicBucketName,
        };
      } catch (error) {
        console.error("Failed to get OSS token:", error);
        ElMessage.error("获取上传凭证失败!");
      }
    },
    
    customRequest(option) {
      const file = option.file;
      this.uploading = true;
      this.uploadProgress = 0;
      
      if (!this.ossData.SecretId) {
        this.getOssToken().then(() => this.uploadVideo(file));
      } else {
        this.uploadVideo(file);
      }
    },
    
    uploadVideo(file) {
      const fileName = `/${window.location.host}/video/${new Date().getTime()}_${file.name}`;
      const cos = new COS({
        SecretId: this.ossData.SecretId,
        SecretKey: this.ossData.SecretKey,
        XCosSecurityToken: this.ossData.XCosSecurityToken,
      });

      cos.uploadFile(
        {
          Bucket: this.ossData.Bucket,
          Region: "ap-shanghai",
          Key: fileName,
          Body: file,
          onProgress: (progressData) => {
            this.uploadProgress = Math.floor(progressData.percent * 100);
          }
        },
        (err, data) => {
          if (err) {
            console.error(err);
            ElMessage.error('视频上传失败!');
            this.uploading = false;
          } else {
            this.videoUrl = "https://img.medsci.cn/" + data.Location.split('/').slice(1).join('/');
            ElMessage.success('视频上传成功!');
            this.uploadProgress = 100;
            setTimeout(() => {
              this.uploadProgress = 0;
              this.uploading = false;
            }, 1000);
          }
        }
      );
    },
    
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file);
    }
  },
};
</script>

<style scoped lang="scss">
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.video-uploader {
  display: flex;
  justify-content: center;
  
  .video-preview {
    max-width: 400px;
    max-height: 225px;
  }
  
  .upload-placeholder {
    width: 400px;
    height: 225px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fbfdff;
    color: #c0c4cc;
    
    i {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 10px;
    }
    
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
  }
}

.progress-container {
  width: 400px;
  margin: 20px auto 0;
}

.preview-container {
  margin-top: 10px;
  
  .video-preview {
    max-width: 400px;
    max-height: 225px;
  }
}
</style>
