<template>
  <div
    :class="{ fullscreen: fullscreen }"
    class="tinymce-container"
    v-loading="imgReplace"
    element-loading-text="图片上传..."
  >
    <textarea :id="tinymceId" class="tinymce-textarea" />
    <div class="editor-custom-btn-container">
      <!-- <editorFile class="editor-upload-btn" @successCBK="fileSuccessCBK" :disabled="disabled" style="margin-right: 20px;"></editorFile> -->
      <editorImg
        class="editor-upload-btn"
        @successCBK="imageSuccessCBK"
        :disabled="disabled"
        style="margin-right: 20px"
      />
      <editorVideo class="editor-upload-btn" @successCBK="videoSuccessCBK" :disabled="disabled"/>
    </div>
  </div>
</template>

<script>
// import { mapActions } from "vuex";
import plugins from "./plugins";
import toolbar from "./toolbar";
import load from "./dynamicLoadScript";
import editorImg from "./editorImg";
import { ossToken } from "@/request/service.js";
// import editorFile from './editorFile'
// import { GET_STS_CREDENTIAL } from "@/api/app";
import editorVideo from './editorVideo'

// const tinymceCDN = 'https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.3/tinymce.min.js'
const tinymceCDN =
  "https://static.medsci.cn/public-js/tinymce/@4.9.3/tinymce.min.js";
// const tinymceCDN = 'https://static.medsci.cn/public-js/tinymce/@5.x/tinymce.min.js'
import COS from "cos-js-sdk-v5";
export default {
  name: "ms-editor",
  computed: {
    userInfo() {
      return this.$store.state.backInfo.userInfo;
    },
    // ...mapActions(["uuid"]),
  },
  props: {
    id: {
      type: String,
      default: function () {
        return (
          "vue-tinymce-" +
          +new Date() +
          ((Math.random() * 1000).toFixed(0) + "")
        );
      },
    },
    modelValue: {
      type: String,
      default: "",
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return [];
      },
    },
    menubar: {
      type: String,
      default: "file edit insert view format table",
    },
    height: {
      type: [Number, String],
      required: false,
      default: 220,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isBlock: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      uuid: "test111",
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      imgReplace: false,
      imgUploadFlag: true,

      ossData: {},
      uploadYear: new Date().getFullYear(),
      uploadMouth: new Date().getMonth() + 1,
      uploadDate: new Date().getDate(),
      userId: "",
    };
  },
  // components: { editorImg,editorFile },
  components: { editorImg, editorVideo },
  watch: {
    modelValue(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() =>
          window.tinymce.get(this.tinymceId).setContent(val || "")
        );
        this.setEditorImg(val);
      }
    },
  },
  mounted() {
    this.init();

    this.getOssToken();
    this.userId = this.userInfo.userId
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce();
    }
  },
  deactivated() {
    this.destroyTinymce();
  },
  unmounted() {
    this.destroyTinymce();
    this.$store.dispatch("SetEditorImg", []);
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        if (err) {
          this.$message.error(err.message);
          return;
        }
        this.initTinymce();
      });
    },
    initTinymce() {
      const _this = this;
      window.tinymce.init({
        selector: `#${this.tinymceId}`,
        forced_root_block: this.isBlock ? "p" : "",
        language: "zh_CN",
        height: this.height,
        body_class: "panel-body ",
        object_resizing: false,
        toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
        menubar: this.menubar,
        plugins: plugins,
        end_container_on_empty_block: true,
        paste_webkit_styles: "color",
        paste_retain_style_properties: "color",
        paste_data_images: true,
        paste_convert_word_fake_lists: false,
        powerpaste_word_import: "merge",
        code_dialog_height: 450,
        code_dialog_width: 1000,
        advlist_bullet_styles: "square",
        advlist_number_styles: "default",
        imagetools_cors_hosts: ["www.tinymce.com", "codepen.io"],
        default_link_target: "_blank",
        link_title: false,
        fontsize_formats: "12px 14px 16px 18px 20px 22px 24px 30px 36px",
        // emoticons_database_url: './emojis.js',
        readonly: this.disabled,
        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
        init_instance_callback: (editor) => {
          if (_this.modelValue) {
            editor.setContent(_this.modelValue);
            this.getEditorImg(_this.modelValue);
          }
          _this.hasInit = true;
          editor.on("NodeChange Change KeyUp SetContent", () => {
            this.hasChange = true;
            this.$emit("update:modelValue", editor.getContent());
          });
          editor.on("blur", () => {
            this.setEditorImg(editor.getContent());
          });
        },
        setup(editor) {
          editor.on("FullscreenStateChanged", (e) => {
            _this.fullscreen = e.state;
          }),
            editor.addButton("clearContentLink", {
              icon: "mce-ico mce-i-format-painter",
              tooltip: "清除文本超链接",
              onclick: function () {
                editor.setContent(
                  editor.getContent().replace(/<a(.*?)<\/a>/g, "<span$1</span>")
                );
              },
            });
        },
      });
    },
    destroyTinymce() {
      const tinymce = window.tinymce.get(this.tinymceId);
      if (this.fullscreen) {
        tinymce.execCommand("mceFullScreen");
      }

      if (tinymce) {
        tinymce.destroy();
      }
    },
    // setContent(value) {
    //   window.tinymce.get(this.tinymceId).setContent(value)
    // },
    // getContent() {
    //   window.tinymce.get(this.tinymceId).getContent()
    // },
    setEditorImg(value) {
      let imgArr = [];
      // let newImg = []
      // let newValue = ''
      // let repIndex = -1
      if (value) {
        value.replace(
          /<img [^>]*src=['"]([^'"]+)[^>]*>/g,
          function (match, capture) {
            if (
              capture.substring(0, 4) !== "http" &&
              capture.substring(0, 4) !== "data"
            ) {
              imgArr.push(`http:${capture}`);
            } else {
              imgArr.push(capture);
            }
          }
        );
      }
      if (imgArr.length > 0) {
        this.imgReplace = true;
        // 循环执行，分批执行上传， 原因： 图片较多后台处理较慢
        this.uploadImg(imgArr);
      }
    },
    async uploadImg(imgArr) {
      let newImg = [];
      let newValue = "";
      let repIndex = -1;
      const promises = imgArr.map(async (url, index) => {
        if (url.indexOf("img.medsci.cn") !== -1) {
          newImg[index] = url;
        } else if (url.substring(0, 4) == "data") {
           let aliOssFile = this.aliOssBase64Upload(url);
          let fileType = aliOssFile.type.replace("image/", "");
          let fileName = `/${window.location.host}/${new Date().getTime()}_${this.userId}.${fileType}`;
          const cos =  new COS({
            SecretId: this.ossData.SecretId,
            SecretKey: this.ossData.SecretKey,
            XCosSecurityToken: this.ossData.XCosSecurityToken,
          });
           await new Promise((resolve, reject) => {
        cos.uploadFile(
          {
            Bucket: this.ossData.Bucket,
            Region: "ap-shanghai",
            Key: fileName,
            Body: aliOssFile,
          },
          (err, data) => {
            if (err) {
              this.imgUploadFlag = false;
              reject(err);
            } else {
              newImg[index] = "https://" + data.Location.replace(data.Location.split('/')[0], this.cosUrl);
              resolve();
            }
          }
        );
      });
        } else {
          newImg[index] = url;
        }
      });
      await Promise.all(promises);
      if (!this.imgUploadFlag) {
        this.$message({
          message: "部分图片粘贴上传失败，请手动上传",
          type: "warning",
        });
        this.imgUploadFlag = true;
      }
      this.imgReplace = false;
      console.log(newImg)
      newValue = window.tinymce
        .get(this.tinymceId)
        .getContent()
        .replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match) {
          repIndex++;
          // eslint-disable-next-line no-useless-escape
          let newImgCopy = JSON.parse(JSON.stringify(newImg));
          return match.replace(
            /\bsrc\b\s*=\s*[\'\"]?([^\'\"]*)[\'\"]?/g,
            `src="${newImgCopy[repIndex]}"`
          );
        });
      this.$emit("update:modelValue", newValue);
      window.tinymce.get(this.tinymceId).setContent(newValue);
      this.$store.dispatch("SetEditorImg", newImg);
    },
    getEditorImg(value) {
      let imgArr = [];
      if (value) {
        value.replace(
          /<img [^>]*src=['"]([^'"]+)[^>]*>/g,
          function (match, capture) {
            imgArr.push(capture);
          }
        );
      }
      this.$store.dispatch("SetEditorImg", imgArr);
    },
    videoSuccessCBK(arr) {
      const _this = this;
      arr.forEach((v) => {
        window.tinymce
          .get(_this.tinymceId)
          .insertContent(
            `<p><video controls="controls" width="300" height="150"><source src="${v.url}" /></video></p>`
          );
      });
    },
    fileSuccessCBK(arr) {
      const _this = this;
      arr.forEach((v) => {
        window.tinymce
          .get(_this.tinymceId)
          .insertContent(
            `<a href="${v.url}" class="ms-editor-file" data-src="${v.url}" style="color:#2f92ee;">${v.name}</a>`
          );
      });
    },
    imageSuccessCBK(url) {
      const _this = this;
      window.tinymce
        .get(_this.tinymceId)
        .insertContent(`<img class="wscnph" src="${url}" >`);
    },

    async getOssToken() {
      const response = await ossToken({ type: 1 });
      this.ossData = {
        SecretId: response.data.accessKeyId,
        SecretKey: response.data.accessKeySecret,
        XCosSecurityToken: response.data.securityToken,
        Bucket: response.data.publicBucketName,
      };
    },

    aliOssBase64Upload(url) {
      var arr = url.split(",");
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      //转换成file对象
      return new File([u8arr], "test", { type: mime });
    },
  },
};
</script>

<style scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}
/* .tinymce-container>>>.mce-tinymce {
  width: 100% !important;
} */
.tinymce-container >>> .mce-fullscreen {
  z-index: 10000;
}
.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}
.editor-custom-btn-container {
  position: absolute;
  right: 4px;
  top: 4px;
  display: flex;
  flex-direction: row !important;
}
.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}
.editor-upload-btn {
  display: inline-block;
}
</style>
