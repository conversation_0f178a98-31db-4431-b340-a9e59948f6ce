<template>
  <div class="ms-upload">
    <!-- type 1 图片 -->
    <el-upload
      class="uploader"
      v-if="type == 1"
      action
      :show-file-list="false"
      :http-request="fnUploadRequest"
      :before-upload="beforeImageUpload"
    >
      <img v-if="url" :src="url" class="uploader-image" />
      <i v-else class="el-icon-plus uploader-icon"></i>
    </el-upload>

    <!-- type 2 视频 -->
    <el-upload
      v-if="type == 2"
      action
      :show-file-list="false"
      :http-request="fnUploadRequest"
      :before-upload="beforeVideoUpload"
    >
      <video class="video" v-if="url" controls :src="url"></video>
      <div style="text-align: left;">
        <el-button size="small" type="primary">点击上传</el-button>
      </div>
    </el-upload>
  </div>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "ms-upload",
  props: {
    type: {
      type: [String, Number],
      default: 1,
    },
    value: String,
  },
  watch: {
    value() {
      this.url = this.value;
    },
  },
  data() {
    return {
      url: "",
    };
  },
  methods: {
    ...mapActions(["oss_upload"]),
    /**
     * @description [fnUploadRequest 覆盖默认的上传行为，实现自定义上传]
     * <AUTHOR>
     * @param    {object}   option [上传选项]
     * @return   {null}   [没有返回]
     */
    async fnUploadRequest(option) {
      const path = await this.oss_upload(option.file);
      this.url = path;
      this.$emit("input", path);
    },
    // 上传图片前限制
    beforeImageUpload(file) {
      const isJPG = file.type.indexOf("image") > -1;
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传图片格式不对!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    beforeVideoUpload(file) {
      const isVideo = file.type.indexOf("video") > -1;
      if (!isVideo) {
        this.$message.error("上传视频格式不对!");
      }
      return isVideo;
    },
  },
};
</script>

<style scoped lang="scss">
.ms-upload {
  .uploader {
    ::v-deep .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    ::v-deep .el-upload:hover {
      border-color: #409eff;
    }
  }
  .uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .uploader-image {
    width: 100px;
    height: 100px;
    display: block;
  }

  .video {
    width: 396px;
    height: 216px;
  }
}
</style>
