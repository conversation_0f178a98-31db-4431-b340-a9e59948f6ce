<template>
  <div class="yxd_users">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="name" placeholder="请输入姓名"></el-input>
          </div>
          <div class="col">
            <el-input v-model="mobile" placeholder="请输入手机号码"></el-input>
          </div>
          <div class="col">
            <span class="tl"></span>
            <el-cascader
            :options="listData"
            placeholder="请选择用户组"
            v-model="roleName"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles'
            }"
            clearable>
          </el-cascader>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button v-if="btnList.add_patient_user_btn" @click="addUser" 
          >添加用户</el-button
        >
        <el-button
          v-if="btnList.batch_Import_patient_user_btn"
          @click="uploadVisible = true"
          >批量导入</el-button
        >
        <el-button v-if="btnList.export_patient_user" @click="handleExportUserInfo" 
          >导出用户</el-button
        >
      </div>
      <div class="table">
        <ms-table
          :columns="data.columns"
          :data="data.list"
          ref="tableRef"
        ></ms-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <el-dialog title="批量导入用户" v-model="uploadVisible" width="50%" :before-close="outVisible">
        <div class="edit-main">
          <div class="col">
            <span>分类名称</span>
            <div class="upload">
              <el-upload
                ref="treeRef"
                class="upload-demo"
                action="#"
                :http-request="customRequest"
                :limit="3"
              >
                <el-button icon="el-icon-upload">上传文件</el-button>
              </el-upload>
              <span @click="downTemplate">下载导入模版</span>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="saveEdit" size="mini"
              >校验数据</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <h3>确定删除用户</h3>
        <el-tag class="delete-box">
          {{ currentChose.realName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveDeleteEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getUserList,
  downloadPatientTemplate,
  importPatientUser,
  deleteUser,
  exportPatientUserInfo,
  query
} from '@/request/service.js'
import initModule from '@/mixins/initModule.js'
import initScroll from '@/mixins/initScroll.js'
import { onMounted, ref, reactive, resolveComponent } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'

export default {
  name: 'users',
  mixins: [initModule, initScroll],
  emits: ['add', 'edit'],
  components: {},
  setup(props, ctx) {
    onMounted(() => {
      init()
      gitListData()
    })
    const uploadVisible = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    const name = ref('')
    const mobile = ref('')
    const roleName = ref('')
    const listData = ref([])
    const colmuns = [
      {
        label: '序号',
        prop: 'accountId'
      },
      {
        label: '用户名',
        prop: 'userName'
      },
      {
        label: '手机号码',
        prop: 'mobile'
      },
      {
        label: '用户组',
        prop: 'roleName'
      },
      {
        label: '注册时间',
        prop: 'createdTime'
      },
      {
        label: '操作',
        width:"150",
        render: (h, scope) => {
          const msHandle = resolveComponent('ms-handle')
          const items = [
            { label: '编辑', func: { func: 'add', uuid: scope }, show: true,type:"default" },
            {
              label: '删除',
              func: { func: 'remove', uuid: scope },
              show: true,
              type:"danger"
            }
          ]
          return h(msHandle, {
            items,
            onAdd: () => {
              handleEdit(scope.row)
            },
            onRemove: () => {
              handleDelete(scope.row)
            }
          })
        }
      }
    ]
    const data = reactive({
      columns: [],
      list: []
    })
    const deleteVisible = ref(false)
    const currentChose = reactive({})
    const currentFile = reactive({
      file: null
    })
    const treeRef = ref(null)
    const userImportFailList = ref([])
    const init = async () => {
      data.list=[]
      data.columns.length = 0
      data.columns.push(...colmuns)
      let params = {
        accountType: 1,
        mobile: mobile.value,
        pageIndex: currentPage.value,
        pageSize: pageSize.value,
        realName: name.value,
        // 用户组筛选数据
        // roleName.value
        userName: '',
      }
      if(!roleName.value){
        params.roleId=0
      }else if(roleName.value.length===1){
        params.roleId=roleName.value[0]
      }else if(roleName.value.length===2){
        params.roleId=roleName.value[1]
      }else{
        params.roleId=roleName.value[2]
      }
      await getUserList(params).then((res) => {
        if (res.status == 200) {
          if (res.data[0].extField) {
            res.data[0].extField.forEach((val,index) => {
              data.columns.splice(11+index, 0, {
                label: val.fieldDisplayName,
                prop: val.fieldName ? val.fieldName : '',
                "width": "100" 
              })
            })
          }
          data.list = res.data
          if (data.list[0].extField) {
            data.list.forEach((d, index) => {
              d.extField.forEach((t) => {
                data.list[index][t.fieldName] = t.fieldValue
              })
            })
          }
          // initScroll();

          total.value = res.totalSize
        }
      })
    }
     const gitListData = async () => {
         let params = {
            accountType:1,
            title: ''
          }
        await query(params).then((res) => {
            if (res.status == 200) {
            listData.value =getList(res.data)
            }
          })
     }
     const  getList =(data)=>{
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  getList(item.childRoles)
        }
     })
    }
    const downTemplate = async () => {
      await downloadPatientTemplate().then((res) => {
        if (res.status == 200) {
          var a = document.createElement('a')
          a.target = '_self'
          a.href = res.data
          a.click()
        }
      })
    }
    const customRequest = (item) => {
      currentFile.file = item.file
    }
    const handleSizeChange = (val) => {
      pageSize.value = val
      init()
    }
    const handleCurrentChange = (val) => {
      currentPage.value = val
      init()
    }
    const addUser = () => {
      // !!!
      ctx.emit('add', {})
    }
    const handleEdit = (row) => {
      ctx.emit('edit', row)
    }
    const handleDelete = (row) => {
      deleteVisible.value = true
      Object.assign(currentChose, row)
    }
    const saveEdit = async () => {
      let formData = new FormData()
      formData.append('file', currentFile.file)
      await importPatientUser(formData).then((res) => {
        if (res.status == 200) {
          if (res.data?.verfiyFail) {
            userImportFailList.value = res.data.userImportFailList
            let temp = ''
            userImportFailList.value.forEach((val) => {
              temp += val.errorMsg + '|'
            })
            ElNotification({
              title: '错误提示',
              message: `成功${res.data?.successCount}条，失败${res.data?.failCount}条，原因为${temp}`,
              type: 'error',
              position: 'bottom-right'
            })
          } else {
            ElNotification({
              title: '提示',
              message: `导入成功！`,
              type: 'success',
              position: 'bottom-right'
            })
          }
          init()
        } else {
          ElNotification({
            title: '错误提示',
            message: `导入失败！`,
            type: 'error',
            position: 'bottom-right'
          })
        }
      })
      treeRef.value.clearFiles()
      uploadVisible.value = false
    }
    const search = () => {
      init()
    }
    const outVisible = () => {
      treeRef.value.clearFiles()
      uploadVisible.value = false
    }

    const reset = () => {
      name.value = ''
      mobile.value = ''
      roleName.value = ''
    }
    const saveDeleteEdit = async () => {
      await deleteUser(currentChose.userId, 1).then((res) => {
        if (res.status == 200) {
          init()
          deleteVisible.value = false
        }
      })
    }
    const handleExportUserInfo = async () => {
      let params = {
        mobile: mobile.value,
        realName: name.value,
      }
      if(!roleName.value){
        params.roleId=0
      }else if(roleName.value.length===1){
        params.roleId=roleName.value[0]
      }else if(roleName.value.length===2){
        params.roleId=roleName.value[1]
      }else{
        params.roleId=roleName.value[2]
      }
      exportPatientUserInfo(params).then((res) => {
        if (res.status == 200) {
          let name = `导出数据.csv`
          let link = document.createElement('a')
          link.href = res.data
          link.download = name
          link.click()
        } else {
          ElMessage.warning('暂无数据导出')
        }
      })
    }
    return {
      uploadVisible,
      currentPage,
      pageSize,
      total,
      name,
      mobile,
      data,
      deleteVisible,
      currentChose,
      currentFile,
      userImportFailList,
      treeRef,
      init,
      gitListData,
      downTemplate,
      customRequest,
      handleSizeChange,
      handleCurrentChange,
      addUser,
      handleEdit,
      handleDelete,
      saveEdit,
      search,
      reset,
      saveDeleteEdit,
      handleExportUserInfo,
      roleName,
      listData,
      getList,
      outVisible
    }
  }
}
</script>

<style scoped lang="scss">
.yxd_users {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .edit-main {
    .col {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30px;
      .el-input {
        width: 30%;
      }
      span {
        margin-top: 10px;
      }
      .upload {
        display: flex;
        flex-direction: column;
        margin-left: 20px;
        span {
          width: 50px;
          white-space: nowrap;
          text-align: right;
          margin-top: 20px;
          cursor: pointer;
          color: #169bd5;
        }
      }
    }
  }
  ::v-deep .el-dialog__footer {
    text-align: center;
  }
}
</style>
