<!--描述: 文件上传组件-->
<!--备注:	 1.使用时注意初始值,即file-list需按规定格式定义.-->
<!--2.上传完成后的结果,已过滤出uuid的数组返回,建议在on-change事件中接收.-->
<!--属性:-->
<!--action					上传的服务器地址.-->
<!--fileList				已上传文件列表.格式必须是: [{name: 'test-01.jpeg', url: '', uuid: ''},{name: 'test-02.jpeg', url: '', uuid: ''}]-->
<!--showFileList			是否显示已上传的文件列表-->
<!--listType				已上传文件的展示类型:text/pictrue.-->
<!--autoUpload				是否选取后自动上传服务器(true,点击"选取文件"按钮后立即上传;false,点击"上传到服务器"按钮后上传).-->
<!--multiple				是否多选-->
<!--disabled				是否禁用-->
<!--方法:-->
<!--on-change				文件状态改变时的钩子，添加文件、上传成功和上传失败,以及移除文件  时都会被调用.-->
<!--before-upload			上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传.(可在此处控制上传的文件大小/格式等).-->
<!--on-preview				点击已上传的文件时触发.-->
<!--on-success				上传服务器成功.-->
<!--on-error				上传服务器失败.-->
<!--on-remove				删除文件时触发.-->
<!--插槽:-->
<!--放置提示文字, slot="tip".-->

<template>
  <!--:file-list="fileList"-->
  <span :class="!showList?'no-ul':''">
    <el-upload style="max-width: 500px" class="eui-upload-file" ref="upload" :action="action" :headers="uploadHeader" :on-success="handleSuccess" :on-preview="handlePreview" :on-remove="handleRemove" :on-error="handleError" :on-change="handleChange" :file-list="fileList" :before-upload="beforeUpload" :show-file-list="showFileList" :list-type="listType" :multiple="multiple" :disabled="disabled" :auto-upload="autoUpload">
      <el-button slot="trigger" v-if="multiple || result.length === 0" size="small" type="primary">选取文件</el-button>
      <span slot="trigger" v-else></span>
      <el-switch v-model="showList" v-if="fileList.length>0" active-text="编辑" inactive-text="查看" style="margin-left: 15px;"></el-switch>
      <el-button class="upload-btn" v-if="!autoUpload" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
      <div slot="tip" class="el-upload__tip">
        <slot name="tip">
          <template v-if="!showList">
            <ul v-for="(item,index) in fileList" :key="index" class="el-upload-list el-upload-list--text ms-local-ul" style="display:block">
              <li class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" style="margin-right:5px;">
                  <i class="el-icon-document"></i>{{item.name}}
                  <b v-preview="item" style="line-height: 24px;float: right;">下载</b>
                </a>
              </li>
            </ul>
          </template>
        </slot>
      </div>
    </el-upload>
  </span>
</template>

<script>
export default {
  name: "up-file",
  props: {
    action: {
      type: String,
      default: function () {
        let host = window.location.host;

        let base = window.location.origin + "/system/common";
        if (host === "localhost:9530" || host === "www.enterprise.local") {
          base = "http://www.enterprise.local/system/common";
        }
        else if (host === "office.workbench.medsci.cn:8888") {
          // todo 正式环境地址
          base = "http://office.workbench.medsci.cn:8888/media/v1/media/common";
        }
        return base;
      }
    },
    size: {
      type: String,
      default: function () {
        return "small";
      },
    },
    fileList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    listType: {
      type: String,
      default: "text"
    },
    autoUpload: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    item: {
      default: function () {
      },
    },
    index: {
      default: function () {
      },
    }
  },
  data () {
    return {
      key: "",
      showList: true,
      uploadHeader: {},
      result: []
    };
  },
  created () {
    this.key = this.index;
    this.set_header();
    this.result = this.fileList.length ? this.fileList : [];
  },
  watch: {
    index: function (newVal) {
      this.key = newVal;
    },
    fileList: {
      handler: function (val) {
        this.result = val ? val : [];
      },
      deep: true
    }
  },
  methods: {
    set_header () {
      let userToken = sessionStorage.getItem("Token");
      if (userToken) {
        userToken = userToken.substring(1, userToken.length - 1);
      }
      const authStr = "Bearer" + userToken;
      this.uploadHeader = {
        "Authorization": authStr,
        "Accept": "application/json, text/plain, */*"
      }
    },
    submitUpload () {
      this.$refs.upload.submit();
    },
    beforeUpload (file) {
      this.$emit("before-upload", file);
    },
    handleChange (file, fileList) {
      this.result = this.filterFileList(fileList);
      if (this.result.every(item => {
        return item;
      })) {
        if (this.item) {
          this.$emit("on-change", { "result": this.result, "item": this.item });
        }
        else if (this.index || this.index === 0) {
          let index = this.key,
            result = this.result,
            file = this.file,
            fileList = this.fileList;
          this.$emit("on-change", { index, result, file, fileList });
        }
        else {
          this.$emit("on-change", this.result, this.file, this.fileList);
        }
      }
    },
    handleRemove (file, fileList) {
      this.result = this.filterFileList(fileList);
      if (this.item) {
        this.$emit("on-change", { "result": this.result, "item": this.item });
      }
      else if (this.index || this.index == 0) {
        let index = this.key,
          result = this.result,
          file = this.file,
          fileList = this.fileList;
        this.$emit("on-change", { index, result, file, fileList });
      }
      else {
        this.$emit("on-change", this.result, this.file, this.fileList);
      }
      this.$emit("on-remove", this.result, file, fileList);
    },
    handlePreview (file) {
      this.$emit("on-preview", file);
    },
    handleSuccess (response, file, fileList) {
      this.result = this.filterFileList(fileList);
      this.$emit("on-success", this.result, response, file, fileList);
    },
    handleError (error, file, fileList) {
      this.result = this.filterFileList(fileList);
      this.$emit("on-error", this.result, error, file, fileList);
    },
    filterFileList (list) {
      if (!Array.isArray(list)) {
        return;
      }
      let uuidArray = [];
      list.forEach(function (item) {
        if (item && item.response && item.response.result && item.response.result.attaches) {
          uuidArray.push({
            uuid: item.response.result.attaches.uuid,
            name: item.response.result.attaches.origin_name,
            url: item.response.result.attaches.path
          });
        }
        else {
          uuidArray.push({
            uuid: item.uuid,
            name: item.name,
            url: item.url
          });
        }
      });
      return uuidArray;
    }
  }
};
</script>

<style>
.upload-btn {
  margin-left: 10px;
}
.el-switch__label span {
  display: inline-block;
  font-size: 12px;
}
.no-ul .el-upload-list {
  display: none;
}
.ms-local-ul .el-upload-list__item {
  margin-top: 5px;
}
</style>
