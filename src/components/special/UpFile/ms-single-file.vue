<template>
    <el-upload action=""
                :http-request="upLoad"
                :before-upload="beforeUpload"
                :file-list="fileLi"
                :show-file-list="false"
                :multiple="false">
        <el-button :loading="buttonLoading" icon="el-icon-upload">{{buttonLabel}}</el-button>
    </el-upload>
</template>

<script>
import upload from "./upload"
export default {
    name: 'ms-single-file',
    mixins: [upload],
    data() {
        return {
        fileLi: []
        }
    },
    props: ["model","buttonLabel"],
    methods: {
        uploadSuccess(res,arg) {
            this.$emit('update:model', `https://${arg.bucket}.${arg.region}.aliyuncs.com/${res.name}`)
        },
        uploadFiled(err) {
            this.$message.error(err.message || '上传失败');
        },
    }
}
</script>
