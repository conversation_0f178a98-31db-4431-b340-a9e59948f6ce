<template>
    <div>
      <div class="single-upload" @click="dialogVisible = true">
        <img v-if="imageUrl" :src="imageUrl" class="single-img">
        <i v-else class="el-icon-plus single-icon"></i>
        <span class="single-action" @click.stop="" v-if="imageUrl">
          <span @click="previewVisible = true"><i class="el-icon-zoom-in"></i></span>
          <span @click="dialogVisible = true"><i class="el-icon-edit"></i></span>
          <span @click="deleteImg()"><i class="el-icon-delete"></i></span>
        </span>
      </div>
      <msImageDialog :dialog="dialogVisible" @close="dialogVisible=false" @getImgUrl="getImgUrl"></msImageDialog>
      <el-dialog :visible.sync="previewVisible" :append-to-body="true">
        <div style="text-align: center;">
          <img style="width: 100%;max-width: 600px;" :src="imageUrl" alt="">
        </div>
      </el-dialog>
    </div>
</template>

<script>
import msImageDialog from './ms-image-dialog'
export default {
  name: 'ms-single-image',
  data() {
    return {
      dialogVisible: false,
      previewVisible: false
    }
  },
  components: {
    msImageDialog
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  computed: {
    imageUrl() {
      return this.value
    }
  },
  methods: {
    deleteImg() {
      this.$emit('input', '')
    },
    getImgUrl(data) {
      this.$emit('input', data[0] && data[0].url || '')
    } 
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  .single-upload {
    width: 100%;
    max-width: 180px;
    height: 104px;
    border: 1px dashed rgba(0,0,0,0.2);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    text-align: center;
    &:hover {
      // border-color: #4B74FF;
      border-color: #409EFF;
    }
    .single-icon {
      font-size: 24px;
      color: rgba(0,0,0,0.2);
      width: 104px;
      height: 104px;
      text-align: center;
      line-height: 104px;
    }
    .single-img {
      width: 100%;
      height: 104px;
      display: block;
      -o-object-fit: contain;
      object-fit: contain;
    }
  }
  .single-action {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0,0,0,.5);
    transition: opacity .3s;
    line-height:104px;
    &:hover {
      opacity: 1;
      span {
        display: inline-block;
      }
    }
    span {
      display: none;
      cursor: pointer;
    }
  }
</style>
