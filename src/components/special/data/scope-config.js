import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
        }
      }
    },
    specialTopicTitle: () => {
      return {
        type: 'preview',
        config: {
          field: 'status',
          pageUrl: `${serveUrl['subject']}`,
          paramsVal: ['id'],
          query: '-1'
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'allHits', way: 'text'},
          {name: 'userHits', way: 'text'},
          // {name: 'shares', way: 'text'}
        ]
      }
    },
    publishedTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    userName: () => {
      return {
        type: 'webLink',
        config: {
          role: '',
          operation: 'edit',
          way: 'page',
          path: 'user-detail',
          params: [{
            keyName: 'id',
            valName: 'encodeUserId'
          }],
        }
      }
    },
    edaTitle: () => {
      return {
        type: 'preview',
        config: {
          noCheck: true,
          pageUrl: `${serveUrl['eda']}`,
          paramsVal: ['encryptionEdaId']
        }
      }
    },
    isExam: () => {
      return {
        type: 'code',
        rule: {
          0: { label: '否' },
          1: { label: '是' },
        }
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: '' },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
