<template>
  <section class="container " v-loading="getLoading">
    <el-tabs v-model="activeName">
    <div style="margin-bottom:20px"></div>

      <el-tab-pane label="合集内容" name="content">
        <ms-special-tab1 ref="content" @changeTab="changeTab" :submitData="submitData"></ms-special-tab1>
      </el-tab-pane>
      <el-tab-pane label="合集图片" name="category">
    <div style="margin-bottom:20px"></div>
        <ms-special-tab2 ref="content1" @changeTab="changeTab" :submitData="submitData"></ms-special-tab2>
        <!-- <ms-info-setting :categoryModel="submitData.categoryList" :categoryConfigChild="{moduleName: 'specialTopic'}" headerShow></ms-info-setting> -->
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
      <div class="btns">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </div>
        
   
  </section>
</template>

<script>
import { addMedsciSpecialTopic ,medsciSpecialTopicDetail,medsciSpecialTopicUpload} from "@/request/service.js";
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msSpecialTab1 from './tool/ms-special-tab1.vue'
import msSpecialTab2 from './tool/ms-special-tab2.vue'
import { mapGetters } from "vuex";
import { ElMessage } from 'element-plus';
export default {
  name: "special-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: 0,
      submitData: {
        categoryList:[],
        contentList: [],
        h5Cover: "",
        pcCover: "",
        shareCover: "",
        guideDownload: 0,
        summary: "",
        classifyList: [],
        skipUrl: '',
        allTab:true,
        tags:'',
        tag:''
      },
      activeName: 'content',
		}
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    FooterToolBar,
    msSpecialTab1,
    msSpecialTab2
    // msInfoSetting,
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dataId=this.$route.query.id ? this.$route.query.id : 0
      let id = this.$route.query.id ? this.$route.query.id : 0

      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        medsciSpecialTopicDetail( id).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            response.data.classifyList=response.data.tags.map(element => {
              return {classifyName:element}
            });

            let res = response.data
            if(!res.classifyList) {
              res.classifyList = []
            }
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          try {
             Promise.all([
              this.$refs['content'].validateData(),
              this.$refs['content1'].validateData()
            ]).then(() => {
              this.dataId ? this.updateForm() : this.createForm();
            }).catch((err)=>{
            });
          } catch (error) {
            
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    createForm() {
      this.buttonLoading = true;
   
      let params = {
        ...this.submitData,
      }
      
      addMedsciSpecialTopic(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      
      let params = {
        ...this.submitData
      }
      medsciSpecialTopicUpload(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.$router.back()
        } 
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>
<style lang="scss" scoped>
    .box{
      margin-bottom: 10px;
    
    }
      .btns{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 20px;
      }
</style>
