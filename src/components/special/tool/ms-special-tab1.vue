<template>
  <section>
    <el-form
      :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="合集标题" prop="title">
                <el-input
                  v-model="submitData.title"
                  style="width: 100%"
                  maxlength="64"
                  placeholder="请输入≤64字的标题"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="合集标签" class="flex-item" prop="tags">
                <el-input
                  v-model.trim="submitData.tags"
                  style="width: 100%"
                  maxlength="64"
                  placeholder="请输入"
                  show-word-limit
                ></el-input>
                <el-button
                  style="flex: 1; margin-left: 10px"
                  type="primary"
                  :disabled="submitData.tags === ''"
                  @click="addTag"
                  >添加</el-button
                >
              </el-form-item>
              <div class="tag-wrap">
                <div
                  class="tag-btn"
                  v-for="(item, index) in submitData.classifyList"
                  :key="'tag' + index"
                >
                  <label>{{ item.classifyName }}</label>
                  <i class="el-icon-close" @click="delTag(index)"></i>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16">
              <el-form-item
                label="选择内容"
                prop="contentList"
                class="flex-item"
              >
                <el-select
                  style="width: 100%; margin-right: 10px"
                  v-model="submitData.tags1"
                  value-key="contentId"
               
                  :remote-method="remoteContent"
                  placeholder="请选择标签"
                  clearable
                >
                  <el-option
                    v-for="item in submitData.classifyList"
                    :key="item.classifyName"
                    :label="item.classifyName"
                    :value="item.classifyName"
                  >
                  </el-option>
                </el-select>
                <ms-dictionary-search
                  v-model="keywords"
                  :model="keywords"
                  @model="changekeywords"
                  type="special_topic"
                  :clearable="true"
                  style="width: 100%"
                ></ms-dictionary-search>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label-width="10px" prop="content" class="flex-item">
                <el-select
                  style="width: 100%"
                  v-model="contentData"
                  :loading="getLoading"
                  filterable
                  remote
                  :remote-method="remoteContent"
                  placeholder="输入标题关键字搜索"
                  clearable
                >
                  <el-option
                    v-for="item in contentList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <el-button
                  style="flex: 1; margin-left: 10px"
                  type="primary"
                  :disabled="contentData === ''"
                  @click="addContent"
                  >添加</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="全部tab" prop="allTab">
                  <el-switch
                    v-model="submitData.allTab"
                    :active-value="true"
                    :inactive-value="false"
                  ></el-switch>
                </el-form-item>
              </el-col>
            </el-row>
            <el-col :span="24">
              <el-form-item label="已选择">
                <el-table
                  ref="dragTable"
                  row-key="additionId"
                  :data="submitData.contentList"
                  :header-cell-style="headerCellStyle"
                  :header-row-style="headerRowStyle"
                  style="
                    width: 100%;
                    border-left: 1px solid #ebeef5;
                    border-right: 1px solid #ebeef5;
                  "
                >
                  <el-table-column align="center" label="序号" min-width="50px">
                    <template #default="scope">
                      <span>{{ scope.row.sort }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="合集标签" min-width="200px">
                    <template #default="scope">
                      <label>{{ scope.row.tag }}</label>
                    </template>
                  </el-table-column>
                  <el-table-column label="模块" min-width="100px">
                    <template #default="scope">
                      <span>{{ enums[scope.row.contentType] }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    label="内容标题"
                    min-width="300px"
                  >
                    <template #default="scope">
                      <span>{{ scope.row.contentTitle }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" min-width="80px">
                    <template #default="scope">
                      <el-tooltip
                        effect="dark"
                        content="删除"
                        placement="bottom"
                      >
                        <span @click="deleteRow(scope.$index)" class="del">
                          <el-icon><Delete /></el-icon>
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="tips">说明：上下拖动记录，调整展示顺序</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import Sortable from "sortablejs";
import msDictionarySearch from "@/components/MsCommon/ms-dictionary-search";
import { mapGetters } from "vuex";
import { medsciSpecialTopicSearch } from "@/request/service.js";
import { Delete } from "@element-plus/icons";
import { ElMessage } from "element-plus";
export default {
  name: "ms-article-content",
  data() {
    return {
      rules: { 
        title: [
          {
            required: true,
            message: "标题不能为空且长度不能超过64字",
            trigger: "blur",
          },
        ],
        tags: [
          { required: true, message: "合集标签不能为空", trigger: "blur" },
        ],
        contentList: [
          {
            required: true,
            message: "必须选择内容，请选择后重试",
            trigger: "blur",
          },
        ],
      },
      enums:{
        'article':"资讯",
        'course':"课程",
        'live_info':"直播",
        'case':"病例",
        'survey':"调研",
        'eda':"医讯达",
        'guider':"指南",
        'tool_impact_factor':"期刊"
      },
      optionSearch: [
        {
          label: "资讯",
          value: "article",
        },
        {
          label: "课程",
          value: "course",
        },
        {
          label: "直播",
          value: "live_info",
        },
        {
          label: "病例",
          value: "case",
        },
        {
          label: "调研",
          value: "survey",
        },
        {
          label: "医讯达",
          value: "eda",
        },
        {
          label: "指南",
          value: "guider",
        },
        {
          label: "期刊",
          value: "tool_impact_factor",
        },
      ],
      pickerConfig: {
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < new Date().getTime() - 8.64e7;
          },
        },
      },
      contentList: [],
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5",
      },
      headerRowStyle: {
        "background-color": "#EBEEF5",
      },
      search: {
        contentTitle: "",
        contentType: "",
      },
      contentType: "",
      contentTypeTitle: "",
      contentData: "",
      keywords: "",
      // tagsList: ['资讯', '课程', '课程3课程3课程3', '课程4', '课程5', '课程6', '课程7', '课程8', '课程9', '课程10'],
      tags: "",
      tags1: [],
    };
  },
  components: {
    msDictionarySearch,
    Delete,
  },
  props: ["submitData"],
  computed: {
    ...mapGetters(["info"]),
  },
  watch: {
    keywords: {
      handler: function () {
        this.changeType();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.setSort()
  },
  methods: {
      rowDrop() {
      const tbody =  this.$refs.dragTable
      let that = this;
      Sortable.create(tbody, {
      filter: ".el-table__row--level-1,.el-table__row--level-2",
        // fallbackOnBody: true,
        // group: {
        //   name: 'shared',
        //   pull: false,
        // },

        onEnd: function(evt) {
          //拖拽结束发生该事件
          that.submitData.contentList.splice(
            evt.newIndex,
            0,
            that.submitData.contentList.splice(evt.oldIndex, 1)[0]
          );
          var newArray = that.submitData.contentList.slice(0);
          that.submitData.contentList = [];
          that.$nextTick(function() {
            that.submitData.contentList = newArray;
          });
        },
      });
    },
    changekeywords(val) {
      this.keywords = val;
    },
    addTag() {
      if (this.submitData.classifyList.length > 9) {
        ElMessage.warning("最多可以创建10个标签");
        return false;
      }
      for (let i = 0; i < this.submitData.classifyList.length; i++) {
        if (
          this.submitData.classifyList[i].classifyName == this.submitData.tags
        ) {
          ElMessage.warning("标签已经存在");
          return false;
        }
      }
      this.submitData.classifyList.push({
        classifyName: this.submitData.tags,
      });

      this.submitData.tags = "";
    },
    delTag(index) {
      let value = this.submitData.classifyList[index].classifyName;

  this.submitData.classifyList.splice(index, 1);
  this.submitData.contentList = this.submitData.contentList.filter(el => el.tag !== value);
    },
    delTagV(scope, index) {
      this.submitData.contentList[scope.$index].classifyList.splice(index, 1);
    },
    remoteContent(keyWord) {
      this.contentList = [];
      if (!(keyWord && keyWord.length > 1)) {
        return;
      }
      this.getLoading = true;
      this.search.moduleName = this.keywords;
      this.search.title = keyWord;
      let params = {
        pageIndex: 1,
        pageSize: 10,
        ...this.search,
      };
      medsciSpecialTopicSearch(params)
        .then((response) => {
          this.getLoading = false;
          if (response.status === 200) {
            this.contentList = response.data;
          }
        })
        .catch(() => {
          this.getLoading = false;
        });
    },
    changeType() {
      this.contentData = "";
      this.contentList = [];
    },
    addContent() {
      // 去重

      if (this.submitData.contentList.length > 500) {
        ElMessage.warning("最多添加500条内容");
        return;
      }
      let classifyList = [];
      if (this.tags1.length) {
        this.tags1.forEach((item) => {
          classifyList.push({
            classifyName: item,
          });
        });
      }
      this.enum();
      for (let i = 0; i < this.submitData.contentList.length; i++) {
        if (
          this.submitData.contentList[i].contentId == this.contentData &&
          this.submitData.contentList[i].contentTitle == this.contentTitle &&
          this.submitData.contentList[i].contentType == this.contentType &&
          this.submitData.contentList[i].tag == this.submitData.tags1
        ) {
          ElMessage.warning("选择内容不能重复");
          this.contentData = "";
          this.contentList = [];
          this.submitData.tags1 = "";
          this.search.contentTitle = "";
          this.keywords = "";
          return false;
        }
      }
      let row = {
        contentId: this.contentData ? this.contentData : 0,
        contentTitle: this.contentTitle ? this.contentTitle : "",
        contentType: this.contentType ? this.contentType : "",
        sort: this.submitData.contentList.length + 1,
        tag: this.submitData.tags1,
      };
      this.submitData.contentList.push(row);
      this.contentData = "";
      this.contentList = [];
      this.submitData.tags1 = "";
      this.search.contentTitle = "";
      this.keywords = "";
    },
    enum() {
      this.optionSearch.forEach((item) => {
        if (item.value == this.keywords) {
          this.contentType = item.value;
        }
      });

      this.contentList.forEach((item) => {
        if (item.id == this.contentData) {
          this.contentTitle = item.title;
        }
      });
    },
    deleteRow(index) {
      this.submitData.contentList.splice(index, 1);
      // this.resort();
    },
    resort() {
      var newArray = this.submitData.contentList.slice(0);
      this.submitData.contentList = [];
      newArray.forEach((item, index) => {
        this.submitData.contentList.push({
          classifyList: item.classifyList,
          contentCover: item.contentCover,
          contentId: item.contentId,
          contentSummary: item.contentSummary,
          contentTitle: item.contentTitle,
          contentType: item.contentType,
          createdName: item.createdName,
          publishedTime: item.publishedTime,
          type: item.type,
          sort: index + 1,
        });
      });
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost",
        setData: function (dataTransfer) {
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          let drapObj = this.submitData.contentList[evt.oldIndex];
          this.submitData.contentList.splice(evt.oldIndex, 1);
          this.submitData.contentList.splice(evt.newIndex, 0, drapObj);
          var newArray = this.submitData.contentList.slice(0);
          this.submitData.contentList = [];
          this.$nextTick(() => {
            newArray.forEach((item,index)=>{
              item.sort=index+1
            })
              this.submitData.contentList=newArray
            
          });
        },
      });
    },
    // 数据校验
    validateData() {
         let tags= this.submitData.classifyList.map(element => {
        return element.classifyName
       });
      this.submitData.tags = tags
      return new Promise((resolve,reject) => {
        this.$refs["submitRef"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            if(!this.submitData.title|| !this.submitData.tags ||!this.submitData.contentList){
                this.$emit("changeTab", "content");
            }
          }
        });
      });
    },
  },
};
</script>
<style scoped>
.flex-item ::v-deep .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.tag-wrap {
  display: flex;
  justify-content: flex-start;
  padding-left: 100px;
  margin: -10px 0 10px 0;
  flex-wrap: wrap;
}
.tag-btn {
  flex: none;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 6px;
  position: relative;
  height: 24px;
  line-height: 24px;
  padding: 0 10px 0 15px;
  border-radius: 5px;
  background-color: #f0f2f5;
  font-size: 11px;
  font-weight: 400;
  display: inline-block;
  cursor: pointer;
  color: #606266;
}
.tag-btn label {
  margin-right: 5px;
}
.del:hover {
  cursor: pointer;
}
</style>