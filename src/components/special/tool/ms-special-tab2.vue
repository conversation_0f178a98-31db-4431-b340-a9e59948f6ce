<template>
  <section>
    <el-form
      :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="10">
        <el-col :span="18">
          <el-row>
            <el-col :span="8">
              <el-form-item label="PC封面图片" prop="pcCover">
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>尺寸：1180*392px</div>
                    </div>
                  </template>
                </el-tooltip>
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload1"
                  :auto-upload="true"
                >
                  <img
                    v-if="submitData.pcCover"
                    :src="submitData.pcCover"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="H5封面图片" prop="h5Cover">
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>尺寸：686*228px</div>
                    </div>
                  </template>
                </el-tooltip>
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload2"
                  :auto-upload="true"
                >
                  <img
                    v-if="submitData.h5Cover"
                    :src="submitData.h5Cover"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分享图片" prop="shareCover">
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>尺寸：100*100px</div>
                      <div>说明：按照微信的要求，给项目域名配置订阅号/服务号，方可生效</div>
                    </div>
                  </template>
                </el-tooltip>
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload3"
                  :auto-upload="true"
                >
                  <img
                    v-if="submitData.shareCover"
                    :src="submitData.shareCover"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="跳转链接" prop="skipUrl">
                <el-input
                  v-model.trim="submitData.skipUrl"
                  style="width: 100%"
                  placeholder="请输入(链接需以http或https开头)"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="摘要" prop="summary">
                <el-input
                  v-model="submitData.summary"
                  type="textarea"
                  :rows="3"
                  :placeholder="'选填；字数≤50；用于在转发卡片中展示'"
                  maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import Sortable from "sortablejs";
import msDictionarySearch from "@/components/MsCommon/ms-dictionary-search";
// import msSingleImage from "../UpFile/ms-single-image.vue";
import { mapGetters } from "vuex";
import { beforeAvatarUpload } from "@/utils/upload.js";
import {ms_rule_url_http} from "@/utils/form-rule.js";

export default {
  name: "ms-article-content",
  data() {
    return {
      rules: {
        specialTopicTitle: [
          { required: true, message: "标题不能为空且长度不能超过64字", trigger: 'blur' }
        ],
        contentList: [
          { required: true, message: "必须选择内容，请选择后重试", trigger: 'blur' },
        ],
        pcCover: [
          { required: true, message: "请上传PC封面", trigger: 'blur' }
        ],
        h5Cover: [
          { required: true, message: "请上传H5封面", trigger: 'blur' }
        ],
        skipUrl: [
          { validator: ms_rule_url_http, trigger: 'blur' }
        ]
      },
      pickerConfig: {
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < new Date().getTime() - 8.64e7;
          },
        },
      },
      contentList: [],
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5",
      },
      headerRowStyle: {
        "background-color": "#EBEEF5",
      },
      search: {
        contentTitle: "",
        contentType: "",
      },
      contentData: {},
      keywords: null,
      // tagsList: ['资讯', '课程', '课程3课程3课程3', '课程4', '课程5', '课程6', '课程7', '课程8', '课程9', '课程10'],
      tags: [],
      tagValue: "",
    };
  },
  components: {
    msDictionarySearch,
    // msSingleImage,
  },
  props: ["submitData"],
  computed: {
    ...mapGetters(["info"]),
  },
  watch: {
    keywords: {
      handler: function () {
        this.changeType();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    async beforeAvatarUpload1(file) {
      this.submitData.pcCover= await beforeAvatarUpload('','',file)
    },
    async beforeAvatarUpload2(file) {
      this.submitData.h5Cover= await beforeAvatarUpload('','',file)
    },
    async beforeAvatarUpload3(file) {
      this.submitData.shareCover= await beforeAvatarUpload('','',file)
    },

    handleCheckChange() {
      this.addCheckList = this.$refs.tree.getCheckedKeys();
    },
    addTag() {
      if (this.submitData.classifyList.length > 9) {
        this.PUBLIC_Methods.apiNotify("最多可以创建10个标签", "warning");
        return false;
      }
      if (this.submitData.classifyList.includes(this.tagValue)) {
        this.PUBLIC_Methods.apiNotify("标签已经存在", "warning");
      } else {
        this.submitData.classifyList.push({
          classifyName: this.tagValue,
        });
        this.tagValue = "";
      }
    },
    delTag(index) {
      let value = this.submitData.classifyList[index].classifyName;
      this.submitData.classifyList.splice(index, 1);
      this.submitData.contentList.forEach((el) => {
        if (el.classifyList && el.classifyList.length) {
          el.classifyList.forEach((els, elsIndex) => {
            if (els.classifyName == value) {
              el.classifyList.splice(elsIndex, 1);
            }
          });
        }
      });
    },
    delTagV(scope, index) {
      this.submitData.contentList[scope.$index].classifyList.splice(index, 1);
    },
    remoteContent(keyWord) {
      this.contentList = [];
      if (!(keyWord && keyWord.length > 1)) {
        return;
      }
      this.getLoading = true;
      this.search.contentTitle = keyWord;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.search,
      };
      this.api
        .searchContentTitle(params)
        .then((response) => {
          this.getLoading = false;
          if (response.status === 200) {
            this.contentList = response.data;
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => {
          this.getLoading = false;
        });
    },
    changeType() {
      this.contentData = {};
      this.contentList = [];
    },
    addContent() {
      // 去重
      if (
        this.submitData.contentList.some(
          (item) => item.contentId == this.contentData.contentId
        )
      ) {
        this.PUBLIC_Methods.apiNotify("此专题已存在，请勿重复添加", "warning");
        return;
      }
      if (this.submitData.contentList.length > 499) {
        this.PUBLIC_Methods.apiNotify("最多添加500条内容", "warning");
        return;
      }
      let classifyList = [];
      if (this.tags.length) {
        this.tags.forEach((item) => {
          classifyList.push({
            classifyName: item,
          });
        });
      }
      let row = {
        classifyList: classifyList,
        contentCover: this.contentData.contentCover
          ? this.contentData.contentCover
          : "",
        contentId: this.contentData.contentId ? this.contentData.contentId : 0,
        contentSummary: this.contentData.contentSummary
          ? this.contentData.contentSummary
          : "",
        contentTitle: this.contentData.contentTitle
          ? this.contentData.contentTitle
          : "",
        contentType: this.contentData.contentType
          ? this.contentData.contentType
          : "",
        createdName: this.contentData.createdName
          ? this.contentData.createdName
          : "",
        publishedTime: this.contentData.publishedTime
          ? this.contentData.publishedTime
          : "",
        type: this.contentData.type ? this.contentData.type : 0,
        sort: this.submitData.contentList.length + 1,
      };
      this.submitData.contentList.push(row);
      this.contentData = {};
      this.contentList = [];
      this.search.contentTitle = "";
    },
    deleteRow(index) {
      this.submitData.contentList.splice(index, 1);
      this.resort();
    },
    resort() {
      var newArray = this.submitData.contentList.slice(0);
      this.submitData.contentList = [];
      newArray.forEach((item, index) => {
        this.submitData.contentList.push({
          classifyList: item.classifyList,
          contentCover: item.contentCover,
          contentId: item.contentId,
          contentSummary: item.contentSummary,
          contentTitle: item.contentTitle,
          contentType: item.contentType,
          createdName: item.createdName,
          publishedTime: item.publishedTime,
          type: item.type,
          sort: index + 1,
        });
      });
    },

    // 数据校验
    validateData() {
         let tags= this.submitData.classifyList.map(element => {
        return element.classifyName
       });
      this.submitData.tags = tags
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            if(this.submitData.title&& this.submitData.tags &&this.submitData.contentList){
                this.$emit("changeTab", "category");
            }
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.flex-item ::v-deep .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.tag-wrap {
  display: flex;
  justify-content: flex-start;
  padding-left: 100px;
  margin: -10px 0 10px 0;
  flex-wrap: wrap;
}
.tag-btn {
  flex: none;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 6px;
  position: relative;
  height: 24px;
  line-height: 24px;
  padding: 0 10px 0 15px;
  border-radius: 5px;
  background-color: #f0f2f5;
  font-size: 11px;
  font-weight: 400;
  display: inline-block;
  cursor: pointer;
  color: #606266;
}
.tag-btn label {
  margin-right: 5px;
}
.info-form{
  
   ::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  ::v-deep .avatar-uploader-icon {
    font-size: 28px;
  color: #8c939d;
  width: 124px;
  height: 124px;
  line-height: 122px !important;
  text-align: center;
  }
  ::v-deep .avatar {
    width: 124px;
    height: 124px;
    display: block;
  }
}
</style>