<template>
  <div class="yxd_classify">
    <!-- 删除分类弹框 -->
    <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
      <span
        >是否删除 <el-tag>{{ currentChose.name }}</el-tag></span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="deleteSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 添加/编辑分类弹框 -->
    <el-dialog :title="titleValue" v-model="editVisible" width="70%">
      <div class="collapse-content">
        <el-row>
          <el-col :span="12" class="col">
            <span class="content-title"
              ><span style="color: red">*</span>分类名称</span
            >
            <div class="content-text">
              <el-input
                v-model.trim="name"
                placeholder="请输入分类名称"
                show-word-limit
                maxlength="10"
              ></el-input>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" class="col">
            <span class="content-title">分类描述</span>
            <div class="content-text">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入描述"
                v-model="description"
              >
              </el-input></div
          ></el-col>
        </el-row>
      </div>
      <div class="collapse-content">
        <el-row>
          <el-col :span="12" class="col">
            <span class="content-title"
              ><span style="color: red">*</span>分类图标
              <el-tooltip
                popper-class="atooltip"
                effect="light"
                placement="top"
              >
                <i
                  class="el-icon-question"
                  style="font-size: 14px; vertical-align: middle"
                ></i>
                <template #content>
                  <div>
                    <div>66*66px</div>
                  </div>
                </template>
              </el-tooltip>
            </span>
            <div class="content-text">
              <div class="upload">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :http-request="customRequest"
                >
                  <img v-if="icon" :src="icon" class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </div></div
          ></el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel" size="mini">取 消</el-button>
          <el-button type="primary" @click="saveEdit()" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div>
        <span
          >下表中的序号，为当前分类板块，在前台首页的展示顺序。上下拖动记录，可调整展示顺序。</span
        >
        <div class="operate">
          <!-- <el-button
              @click="quit()"
              >返回</el-button
            >
            <el-button
              type="primary"
              @click="updateList"
              >提交</el-button
            > -->
          <el-button type="primary" @click="addEdit" icon="el-icon-plus"
            >添加分类</el-button
          >
        </div>
      </div>
      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          row-key="sort"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-sort="{ prop: 'date', order: 'descending' }"
        >
          <el-table-column
            prop="sort"
            label="序号"
            width="240"
            align="center"
            type="index"
          >
          </el-table-column>
          <el-table-column prop="id" label="分类ID" width="240" align="center">
          </el-table-column>
          <el-table-column prop="name" label="分类名称" align="center">
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="350"
          >
            <template #default="scope">
              <el-button size="mini" @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                plain
                :type="scope.row.status ? 'danger' : 'primary'"
                size="mini"
                @click="handleStatus(scope.row)"
                >{{ scope.row.status ? "禁用" : "启用" }}</el-button
              >
              <el-button
                plain
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination">
        <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination> -->
      </div>
    </div>
  </div>
</template>

<script>
import Sortable from "sortablejs";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { ElMessage } from "element-plus";
import {
  goodsCategoryList,
  updateSort,
  addGoodsCategory,
  uploadAvatar,
  goodsCategoryget,
  goodsCategoryOperate,
  goodsCategoryDelete,
  goodsCategoryUpdate,
  goodsCategorySortUpdate,
} from "@/request/service.js";
import classify from "@/constant/classify";
const { classifyType } = classify;
export default {
  name: "classify",
  mixins: [initModule, initScroll],
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editVisible: false,
      titleValue: "",
      name: "",
      description: "",
      icon: "",
      state: "",
      tableData: [],
      deleteVisible: false,
      currentChose: {},
    };
  },
  mounted() {
    this.rowDrop();
    this.init();
  },
  methods: {
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function () {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    // 删除
    deleteSave() {
      let params = {
        id: this.currentChose.id,
      };
      goodsCategoryDelete(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    // 点击确定添加/编辑分类
    saveEdit() {
      if (!this.name) {
        ElMessage.warning("分类名称不可为空！");
        return;
      } else if (this.name.length > 10) {
        ElMessage.warning("请输入10字以内的分类名称");
        return;
      } else if (!this.icon) {
        ElMessage.warning("请上传分类图标");
        return;
      }
      let params = {
        name: this.name,
        icon: this.icon,
        description: this.description,
      };
      if (this.state == "add") {
        addGoodsCategory(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("添加分类成功");
            this.editVisible = false;
            this.name = "";
            this.description = "";
            this.icon = "";
            this.init();
          }
        });
      } else {
        params.id = this.currentChose.id;
        goodsCategoryUpdate(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("编辑分类成功");
            this.editVisible = false;
            this.name = "";
            this.description = "";
            this.icon = "";
            this.init();
          }
        });
      }
    },
    customRequest(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function (img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.icon = res.data.url;
          }
        });
      });
    },
    // 添加分类
    addEdit() {
      this.editVisible = true;
      this.name = "";
      this.description = "";
      this.icon = "";
      this.state = "add";
      this.titleValue = "添加分类"
    },
    // 删除分类
    handleDelete(val) {
      this.deleteVisible = true;
      this.currentChose = val;
    },
    // 修改状态
    handleStatus(row) {
      let params = {
        id: row.id,
        status: row.status == 0 ? 1 : 0,
      };
      goodsCategoryOperate(params).then((res) => {
        if (res.status == 200) {
          this.init();
        }
      });
    },
    handleEdit(val) {
      this.editVisible = true;
      this.currentChose = val;
      this.state = "edit";
      this.name = "";
      this.description = "";
      this.icon = "";
       this.titleValue = "编辑分类"
      let params = {
        id: val.id,
      };
      goodsCategoryget(params).then((res) => {
        if (res.status == 200) {
          this.name = res.data.name;
          this.description = res.data.description;
          this.icon = res.data.icon;
        }
      });
    },
    init() {
      this.tableData = [];
      let params = {
        pageIndex: 1,
        pageSize: 100,
        tagName: this.title,
        tagType: this.type,
      };
      goodsCategoryList(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll();
        }
      });
    },
    rowDrop() {
      const tbody = document.querySelector(".el-table__body-wrapper tbody");
      let that = this;
      Sortable.create(tbody, {
        filter: ".el-table__row--level-1,.el-table__row--level-2",
        // fallbackOnBody: true,
        // group: {
        //   name: 'shared',
        //   pull: false,
        // },

        onEnd: function (evt) {
          //拖拽结束发生该事件
          that.tableData.splice(
            evt.newIndex,
            0,
            that.tableData.splice(evt.oldIndex, 1)[0]
          );
          var newArray = that.tableData.slice(0);
          that.tableData = [];
          that.$nextTick(function () {
            that.tableData = newArray;
            this.ids = [];
            that.tableData.forEach((d) => {
              this.ids.push(d.id);
            });
            let params = {
              ids: this.ids,
            };
            goodsCategorySortUpdate(params).then((res) => {});
          });
        },
      });
    },
    // 提交分类列表
    updateList() {
      updateSort(this.ids).then((res) => {
        if (res.status == 200) {
          this.goBack();
        }
      });
    },
    goBack() {
      this.$emit("back");
    },
    cancel(){
      this.editVisible = false;
    },
    quit() {
      this.$confirm("排序还未保存,是否退出?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$emit("back");
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    // 格式化分类类别
    formatTagName(row, column, cellValue) {
      const obj = classifyType.find((item) => item.name === cellValue);
      return obj ? obj.value : "未知";
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_classify {
  .operate {
    float: right;
    ::v-deep {
      .el-button {
        width: 120px;
        margin-bottom: 30px;
      }
    }
  }
  .edit-main {
    .col {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-input {
        width: 30%;
      }
      .el-textarea {
        width: 30%;
      }
      .tl {
        width: 70px;
        white-space: nowrap;
        margin-right: 20px;
        text-align: right;
      }
    }
  }
}
.collapse-content {
  // margin-top: 20px;
  padding: 20px;
  .col {
    display: flex;
    padding: 10px 20px;
    .content-title {
      width: 120px;
      text-align: center;
    }
    .content-text {
      display: flex;
      width: calc(100% - 120px);
    }
  }
}
.upload {
  ::v-deep .avatar-uploader {
    .el-upload--text {
      width: 120px;
      height: 120px;
      line-height: 120px;
      background-color: #fff;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      box-sizing: border-box;
      text-align: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
  .warn {
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    white-space: nowrap;
  }

  .avatar {
    width: 120px;
    height: 120px;
    object-fit: contain;
  }
}
</style>
