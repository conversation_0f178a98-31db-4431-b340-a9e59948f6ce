<template>
  <div class="yxd_classify" v-if="!$route.query.tem">
    <!-- 删除单条 -->
    <el-dialog title="批量删除" v-model="deleteCourseSome" width="70%">
      <span
        >删除后，对应分类下的内容，会批量从前台下线，用户无法查看</span
      >
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteCourseSome = false" size="mini">取 消</el-button>
          <el-button type="primary" @click="deleteSaveSome" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <!-- <span class="tl">筛选分类</span>
            <el-select v-model="categoryValue" placeholder="请选择">
              <el-option
                v-for="item in categoryList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                @click="init"
              >
              </el-option>
            </el-select> -->
          </div>
        </div>
        <div class="right">
          <div class="operate">
             <el-button type="default" v-if="btnList.batch_remove_btn" :disabled="selectedRows.length<2" @click="delSome">批量删除</el-button>
            <el-button
              v-if="btnList.add_category_btn"
              type="primary"
              @click="addEdit"
              icon="el-icon-plus"
              >添加分类</el-button
            >
            <el-button type="primary" @click="sortClassify">分类排序</el-button>
            <!-- <el-button
              type="primary"
              @click="updateList"
              >提交</el-button
            > -->
          </div>
          <!-- <el-button icon="el-icon-search" >查询</el-button>
          <el-button @click="reset">重置</el-button> -->
        </div>
      </div>

      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          default-expand-all
          ref="table"
          row-key="id"
          :tree-props="{ children: 'childTags', hasChildren: 'hasChildren' }"
          :default-sort="{ prop: 'date', order: 'descending' }"
          @select="handleSelect"
          @selection-change="handleSelectionChange"
          @select-all="handleSelectAll"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            prop="sortList"
            label="序号"
            width="120"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column prop="id" label="分类ID" width="200" align="center">
          </el-table-column>
          <el-table-column
            prop="tagName"
            label="分类名称"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="tagType"
            :formatter="formatTagName"
            label="分类类型"
            width="300"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="350"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.edit_btn"
                size="mini"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="
                  (scope.row.parentId == 0 &&
                    scope.row.tagType == 'exam_paper') ||
                  (scope.row.parentId == 0 &&
                    scope.row.tagType == 'live_info') ||
                  (scope.row.parentId == 0 && scope.row.tagType == 'course') ||
                  (scope.row.parentId == 0 && scope.row.tagType == 'article') ||
                  (scope.row.parentId == 0 && scope.row.tagType == 'eda') ||
                  scope.row.tagType == 'case'
                "
                size="mini"
                @click="handleTem(scope.$index, scope.row, scope.row.tagType)"
                >{{
                  !scope.row.h5TagUi && !scope.row.childTagUi
                    ? "选择模板"
                    : "更换模板"
                }}</el-button
              >
              <el-button
                size="mini"
                type="primary"
                plain
                @click="addEditChildren(scope.$index, scope.row)"
                >添加子分类</el-button
              >
              <el-button
                v-if="btnList.remove_btn"
                plain
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div> -->
      <!-- 添加/编辑分类弹框 -->
      <el-dialog :title="titleValue" v-model="editVisible" width="70%">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item title="添加分类" name="1">
            <div class="collapse-content">
              <el-row>
                <el-col :span="12" class="col">
                  <span class="content-title"
                    ><span style="color: red">*</span>分类名称</span
                  >
                  <div class="content-text">
                    <el-input
                      v-model.trim="title"
                      placeholder="请输入62字以内的名称"
                    ></el-input>
                  </div>
                </el-col>
                <el-col :span="12" class="col">
                  <span class="content-title" v-if="state === 'add'"
                    ><span style="color: red">*</span> 分类类别</span
                  >
                  <span
                    class="content-title"
                    v-if="state === 'edit' && categoryDialog == 'ugc'"
                    ><span style="color: red">*</span> 首页按钮文案</span
                  >
                  <div class="content-text">
                    <el-select
                      v-if="state === 'add'"
                      v-model="categoryDialog"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in categoryList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                    <div class="content-text">
                      <el-input
                        v-if="categoryDialog == 'ugc'"
                        v-model.trim="btnName"
                        placeholder="请输入首页按钮文案"
                      ></el-input>
                    </div></div
                ></el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="col" v-if="state === 'add'">
                  <span class="content-title" v-if="state === 'add'"
                    >分类描述</span
                  >
                  <div class="content-text">
                    <el-input
                      type="textarea"
                      :rows="4"
                      placeholder="请输入内容"
                      v-model="textarea"
                    >
                    </el-input></div
                ></el-col>
              </el-row>
            </div>
          </el-collapse-item>
          <el-collapse-item title="广告位（仅在移动端有效）" name="2">
            <div class="collapse-content">
              <el-row>
                <el-col :span="12" class="col">
                  <span class="content-title">广告位标题</span>
                  <div class="content-text">
                    <el-input
                      v-model="adName"
                      placeholder="请输入62字以内的广告位标题"
                    /></div
                ></el-col>
                <el-col :span="12" class="col">
                  <span class="content-title">设置有效期</span>
                  <div class="content-text">
                    <el-date-picker
                      v-model="time"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM-DD HH:mm:ss"
                    >
                    </el-date-picker></div
                ></el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="col">
                  <span class="content-title">跳转地址</span>
                  <div class="content-text">
                    <el-select
                      v-model="typeValue"
                      @change="optionChange"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                    <div style="width: 200px">
                      <el-input
                        v-if="typeValue == '2'"
                        v-model="adUrl"
                        placeholder="请输入网址"
                      ></el-input>
                    </div></div
                ></el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="col">
                  <span class="content-title"
                    >封面
                    <!-- <el-tooltip
                      popper-class="atooltip"
                      effect="light"
                      placement="top"
                    >
                      <i
                        class="el-icon-question"
                        style="font-size: 14px; vertical-align: middle"
                      ></i>
                      <template #content>
                        <div>
                          <div>
                            尺寸685*137px， 格式jpg/jpeg/png， 大小2M以内
                          </div>
                        </div>
                      </template>
                    </el-tooltip> -->
                  </span>
                  <div class="content-text">
                    <div class="upload">
                      <el-upload
                        class="avatar-uploader"
                        action="#"
                        :show-file-list="false"
                        :http-request="customRequest"
                      >
                        <img v-if="adCover" :src="adCover" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                    </div></div
                ></el-col>
              </el-row>
            </div>
          </el-collapse-item>
        </el-collapse>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancel" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveEdit()" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 删除分类弹框 -->
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <span
          >是否删除 <el-tag>{{ currentChose.tagName }}</el-tag></span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="deleteSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 添加/编辑子分类弹框 -->
      <el-dialog
        :title="state === 'add' ? title + '——添加子分类' : '编辑子分类'"
        v-model="ChildrenVisible"
        width="70%"
      >
        <div class="edit-main">
          <div class="col">
            <span class="tl"><span style="color: red">*</span> 分类名称</span>
            <el-input
              v-model.trim="titleChildren"
              placeholder="请输入10字以内的名称"
            ></el-input>
          </div>
          <div class="col" v-if="tagType == 'ugc'">
            <span class="tl"
              ><span style="color: red">*</span>首页按钮文案</span
            >
            <el-input
              v-model.trim="btnTitle"
              placeholder="请输入首页按钮文案"
            ></el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="ChildrenVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveChildren()" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
  <templates v-else @close="close"></templates>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import templates from "@/views/template.vue";
import { beforeAvatarUpload } from "@/utils/upload.js";
import { ElMessage } from "element-plus";
import {
  addTag,
  deleteTag,
  listTag,
  updateTag,
  updateSort,
  uploadAvatar,
  tagBatch,
} from "@/request/service.js";
import classify from "@/constant/classify";
import { ref } from "vue";
import { Failed } from '@element-plus/icons';
const { classifyType } = classify;
let i = 1;
export default {
  name: "classify",
  components: {
    templates,
  },
  mixins: [initModule, initScroll],
  data() {
    return {
      activeNames: ["1", "2"],
      time: "",
      startTime: "",
      endTime: "",
      deleteCourseSome:false,
      options: [
        {
          value: "1",
          label: "无跳转",
        },
        {
          value: "2",
          label: "网址",
        },
      ],
      typeValue: "",
      adName: "",
      adCover: "",
      adUrl: "",
      btnName: "",
      categoryValue: "",
      categoryDialog: "",
      selectedRows: [],
      selecting:false,
      categoryList: [
        {
          label: "资讯",
          value: "article",
        },
        {
          label: "课程",
          value: "course",
        },
        {
          label: "直播",
          value: "live_info",
        },
        {
          label: "病例",
          value: "case",
        },
        {
          label: "调研",
          value: "survey",
        },
        {
          label: "医讯达",
          value: "eda",
        },
        {
          label: "指南",
          value: "guider",
        },
        {
          label: "期刊",
          value: "tool_impact_factor",
        },
        {
          label: "考试",
          value: "exam_paper",
        },
        {
          label: "UGC",
          value: "ugc",
        },
      ],
      
      titleValue: "",
      title: "",
      textarea: "",
      editVisible: false,
      deleteVisible: false,
      ChildrenVisible: false,
      currentChose: {},
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      state: "",
      ids: [],
      titleChildren: "",
      parentId: "",
      tagType: "",
      btnTitle: "",
    };
  },
  // created(){
  //   this.categoryValue='article'
  // },

  mounted() {
    this.init();
  },
  methods: {
    // 批量删除
    delSome(){
      this.deleteCourseSome = true
    },
    // 确认删除
    deleteSaveSome(){
      let params = {
      }
      params.ids = this.selectedRows.map((item)=>{
        return item.id
      })
      tagBatch(params).then((res) => {
        if(res.status==200){
          ElMessage.success("批量删除成功");
          this.init()
          this.deleteCourseSome = false
        }
      })
    },
    // 全选
    handleSelectAll(selection) {
      if (selection.length === this.tableData.length) {
        // 全选
        this.selectAllRows();
      } else {
        // 取消全选
        this.clearSelection();
      }
    },
     selectAllRows() {
      this.tableData.forEach(row => {
        this.$refs.table.toggleRowSelection(row, true);
        if (row.childTags) {
          this.selectAllChildren(row.childTags);
        }
      });
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
    selectAllChildren(rows) {
        rows.forEach(row => {
          this.$refs.table.toggleRowSelection(row, true);
          if (row.childTags && row.childTags.length > 0) {
            this.selectAllChildren(row.childTags);
          }
        });
      },
    // 多选
     handleSelect(selection, row) {
      // 如果父级被选中，阻止子级被取消选中
      if (selection.includes(row)) {
        this.addRowAndChildrenToSelection(row);
      } else {
        // 如果父级没有被选中，允许子级自由选中
        this.removeRowAndChildrenFromSelection(row);
      }
    },
    selectAllChildren(rows) {
      rows.forEach(row => {
        this.$refs.table.toggleRowSelection(row, true);
        if (row.childTags) {
          this.selectAllChildren(row.childTags);
        }
      });
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    removeRowAndChildrenFromSelection(row) {
      // 只有当父级没有被选中时，才允许取消子级的选中状态
      if (!this.isParentSelected(row)) {
        this.$refs.table.toggleRowSelection(row, false);
        if (row.childTags) {
          setTimeout(() => {
              row.childTags.forEach(child => {
                this.removeRowAndChildrenFromSelection(child);
              });
          },0)
        }
      }else{
        setTimeout(() => {
          this.$refs.table.toggleRowSelection(row, true);
          },0)
      }
    },
    addRowAndChildrenToSelection(row) {
      this.$refs.table.toggleRowSelection(row, true);
      if (row.childTags) {
        setTimeout(() => {
          row.childTags.forEach(child => {
            this.addRowAndChildrenToSelection(child);
          });
        }, 0);
      }
    },
    isParentSelected(row) {
    // 检查当前行的父级是否被选中
        const parent = this.findParent(row, this.tableData);
        console.log(parent)
        return parent ? this.selectedRows.includes(parent) : false;
    },

    findParent(child, rows) {
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        if (row.childTags && row.childTags.includes(child)) {
          return row;
        }
        if (row.childTags) {
          const parent = this.findParent(child, row.childTags);
          if (parent) {
            return parent;
          }
        }
      }
      return null;
    },
    optionChange(e) {
      if (e == 1) {
        this.adUrl = "";
      }
    },
    handleChange(val) {},
    async beforeAvatarUpload1(file) {
      this.adCover = await beforeAvatarUpload("", "", file);
      // const isLt2M = file.size / 1024 / 1024 < 2;
      // if (!isLt2M) {
      //   ElMessage.error("上传头像图片大小不能超过 2MB!");
      //   return false;
      // } else {
      //   return true;
      // }
    },
    filetoDataUrl(file, callback) {
      var reader = new FileReader();
      reader.onload = function () {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    },
    customRequest(item) {
      let _this = this;
      this.filetoDataUrl(item.file, function (img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            _this.adCover = res.data.url;
          }
        });
      });
    },
    uncheckAll() {
      // 获取 el-table 的所有数据行
      const rows = this.tableData;

      // 递归遍历所有行,取消选中
      this.uncheckRows(rows);

      // 手动清空 el-table 的选中项
      this.$refs.table.clearSelection();

      // 清空记录的选中行
      this.selectedRows = [];
    },

    uncheckRows(rows) {
      rows.forEach((row) => {
        // 取消选中当前行
        this.$refs.elTable.toggleRowSelection(row, false);

        // 如果有子行,递归取消子行的选中
        if (row.c) {
          this.uncheckRows(row.children);
        }
      });
    },
    close() {
      this.$router.push("/category");
      this.init();
    },
    init() {
      this.tableData = [];
      this.selectedRows = []
      i = 1;
      let params = {
        // pageIndex: this.currentPage,
        // pageSize: this.pageSize,
        // tagName: this.title,
        tagIds: [],
        tagType: this.categoryValue,
      };
      listTag(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = this.getSort(res.data);
          // this.tableData=[{
          //   description: "资讯分类45",
          //   id: "190428",
          //   sort: 1,
          //   tagId: "448505",
          //   tagName: "sun002",
          //   tagType: "article"
          // },{
          //   description: "4345",
          //   id: "190407",
          //   sort: 2,
          //   tagId: "95535",
          //   tagName: "123",
          //   tagType: "article",
          //   children:[{
          //     description: "4345",
          //     id: "190408",
          //     sort: 3,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort: 4,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article",
          //     children:[{
          //     description: "4345",
          //     id: "190408",
          //     sort: 11,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort:12,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort: 13,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort: 14,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   }]
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort: 5,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   },{
          //     description: "4345",
          //     id: "190408",
          //     sort: 6,
          //     tagId: "827913",
          //     tagName: "1233",
          //     tagType: "article"
          //   }]
          // }]
          this.initScroll();
        }
      });
    },
    // 排序
    getSort(val) {
      let data = ref(val);
      return data.value.filter((item) => {
        if (item.childTags.length > 0) {
          setTimeout(() => {
            this.getSort(item.childTags);
          }, 0);
        }
        return (item.sortList = i++);
      });
    },
    handleTem(index, row, type) {
      this.$router.push(
        "/category?tem=" + row.tagType + "&row=" + JSON.stringify(row)
      );
    },
    // 编辑
    handleEdit(index, row) {
      if (row.parentId === 0) {
        if (row.adUrl) {
          this.typeValue = "2";
        } else {
          this.typeValue = "1";
        }
        this.titleValue = "编辑分类";
        this.state = "edit";
        this.title = row.tagName;
        this.textarea = row.description;
        this.categoryDialog = row.tagType;
        this.currentChose = row;
        this.editVisible = true;
        this.btnName = row.btnTitle;
        this.adName = row.adName;
        this.adUrl = row.adUrl;
        this.adCover = row.adCover;
        this.time = [row.startTime, row.endTime];
      } else {
        this.state = "edit";
        this.titleChildren = row.tagName;
        this.ChildrenVisible = true;
        this.categoryDialog = row.tagType;
        this.tagType = row.tagType;
        this.currentChose = row;
        this.btnTitle = row.btnTitle;
      }
    },
    // 添加分类
    addEdit() {
      this.titleValue = "添加分类";
      this.state = "add";
      this.title = "";
      this.textarea = "";
      this.categoryDialog = "";
      this.adCover = "";
      this.editVisible = true;
      this.btnTitle = "";
      this.typeValue = "";
    },
    // 点击取消
    cancel() {
      this.editVisible = false;
      this.ChildrenVisible = false;
      this.textarea = "";
      this.title = "";
      this.categoryDialog = "";
      this.adCover = "";
      this.currentChose = {};
    },
    // 删除分类
    handleDelete(index, row) {
      this.currentChose = row;
      this.deleteVisible = true;
    },
    // 点击确定添加/编辑分类
    saveEdit() {
      if (!this.title) {
        ElMessage.warning("分类名称不可为空！");
        return;
      } else if (this.title.length > 62) {
        ElMessage.warning("请输入62字以内的分类名称");
        return;
      } else if (!this.btnName && this.categoryDialog == "ugc") {
        ElMessage.warning("请输入首页按钮文案");
        return;
      } else if (
        this.btnName &&
        this.categoryDialog == "ugc" &&
        this.btnName.length > 15
      ) {
        ElMessage.warning("按钮文案不得超过十五个字");
        return;
      }
      //  else if (!this.typeValue) {
      //   ElMessage.warning("请选择跳转地址");
      //   return;
      // }
      else if (this.typeValue == 2 && !this.adUrl) {
        ElMessage.warning("请输入跳转地址");
        return;
      } else if (this.adName&&this.adName.length > 62) {
        ElMessage.warning("请输入62字以内的分类名称");
        return;
      } else if (this.adUrl) {
        if (
          !/(http|https):\/\/([\w.]+(?:\.[\w\.-]+))+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(
            this.adUrl
          )
        ) {
          ElMessage.warning("请输入带有协议前缀的正确网站域名");
          return;
        }
        if (!this.adCover) {
          ElMessage.warning("有跳转地址，图片必传");
          return;
        }
      }
      let params = {
        description: this.textarea,
        tagName: this.title,
        tagType: this.categoryDialog,
        btnTitle: this.categoryDialog == "ugc" ? this.btnName : "",
        adName: this.adName,
        adUrl: this.adUrl,
        adCover: this.adCover,
        startTime: this.time[0],
        endTime: this.time[1],
      };
      if (this.state == "add") {
        addTag(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("添加分类成功");
            this.editVisible = false;
            this.title = "";
            this.textarea = "";
            this.categoryDialog = "";
            this.init();
          }
        });
      } else {
        params.id = this.currentChose.id;
        (params.btnTitle = this.categoryDialog == "ugc" ? this.btnName : ""),
          updateTag(params).then((res) => {
            if (res.status == 200) {
              ElMessage.success("编辑分类成功");
              this.editVisible = false;
              this.title = "";
              this.textarea = "";
              this.categoryDialog = "";
              this.init();
            }
          });
      }
    },
    // 点击添加子分类
    addEditChildren(index, row) {
      this.state = "add";
      this.title = row.tagName;
      this.titleChildren = "";
      this.parentId = row.id;
      this.ChildrenVisible = true;
      this.tagType = row.tagType;
      this.btnTitle = "";
    },
    // 点击确定添加/编辑子分类
    saveChildren() {
      if (!this.titleChildren) {
        ElMessage.warning("分类名称不可为空！");
        return;
      } else if (this.titleChildren.length > 10) {
        ElMessage.warning("请输入10字以内的分类名称");
        return;
      } else if (!this.btnTitle && this.tagType == "ugc") {
        ElMessage.warning("按钮文案不能为空");
        return;
      } else if (this.btnTitle.length > 15 && this.tagType == "ugc") {
        ElMessage.warning("请输入十五字以内的按钮文案");
        return;
      }
      if (this.state === "add") {
        let params = {
          tagName: this.titleChildren,
          parentId: this.parentId,
          tagType: this.tagType,
          btnTitle: this.tagType == "ugc" ? this.btnTitle : "",
        };
        addTag(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("添加子分类成功");
            this.ChildrenVisible = false;
            this.title = "";
            this.titleChildren = "";
            this.parentId = "";
            this.tagType = "";
            this.init();
          }
        });
      } else {
        let params = {
          // description: this.textarea,
          tagName: this.titleChildren,
          tagType: this.categoryDialog,
          btnTitle: this.btnTitle,
        };
        params.id = this.currentChose.id;
        updateTag(params).then((res) => {
          if (res.status == 200) {
            ElMessage.success("编辑分类成功");
            this.ChildrenVisible = false;
            this.title = "";
            this.titleChildren = "";
            this.parentId = "";
            this.tagType = "";
            this.categoryDialog = "";
            this.init();
          }
        });
      }
    },

    deleteSave() {
      let params = {
        id: this.currentChose.id,
      };
      deleteTag(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    // 提交分类列表
    updateList() {
      updateSort(this.ids).then((res) => {
        if (res.status == 200) {
          this.init();
        }
      });
    },
    // indexMethod(index){
    //    return (this.currentPage-1)*10+index+1;
    // },

    // 重置按钮
    reset() {
      this.categoryValue = "";
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    sortClassify() {
      this.$emit("sort", this.categoryValue);
    },
    // 格式化分类类别
    formatTagName(row, column, cellValue) {
      const obj = classifyType.find((item) => item.name === cellValue);
      return obj ? obj.value : "未知";
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-collapse-item__header {
    border-bottom-color: transparent;
    background: #f4f4f5;
    padding-left: 20px;
  }
  .el-collapse-item__header.is-active {
    border-bottom-color: transparent;
    background: #f4f4f5;
    padding-left: 20px;
  }
}
.upload {
  ::v-deep .avatar-uploader {
    .el-upload--text {
      width: 120px;
      height: 120px;
      line-height: 120px;
      background-color: #fff;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      box-sizing: border-box;
      text-align: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
  .warn {
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    margin-right: 20px;
    white-space: nowrap;
  }

  .avatar {
    width: 120px;
    height: 120px;
    object-fit: contain;
  }
}
.collapse-content {
  // margin-top: 20px;
  padding: 20px;
  .col {
    display: flex;
    padding: 10px 20px;
    .content-title {
      width: 120px;
      text-align: center;
    }
    .content-text {
      display: flex;
      width: calc(100% - 120px);
    }
  }
}
.yxd_classify {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
        .col_space {
          margin-right: 20px;
        }
      }
    }
  }
  .table {
    ::v-deep {
      td:nth-child(1) {
        text-align: left;
        padding-left: 10px;
      }
    }
  }
  .edit-main {
    .col {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-input {
        width: 30%;
      }
      .el-textarea {
        width: 30%;
      }
      .tl {
        width: 110px;
        white-space: nowrap;
        margin-right: 20px;
        text-align: right;
      }
    }
  }
  ::v-deep {
    .el-table__row--level-2 .cell button:nth-child(2) {
      // visibility: hidden;
      display: none;
    }
  }
}
</style>
