<template>
  <div class="yxd_comment">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
     <div style="color: #ffca32; margin: 0 auto; margin-bottom: 15px;">所有设置的任务，必须在用户登录状态下，方可生效。若开发式项目的任务中选择的模块内容未要求登录，则未登录用户到达任务要求也无法发放梅花。</div>
      <div class="header" v-if="!addTask">
        <div class="left">
          <div class="col">
            <el-button  @click="goTab()" type="primary" v-if="btnList.new_task">新建任务</el-button>
          </div>
        </div>
        <div class="right">
          <el-input v-model="comment"  placeholder="搜索任务名称" @keydown.enter="init">
            <template #append>
               <el-button 
                 icon="el-icon-search"
                 @click.stop="init">
               </el-button> 
            </template>
          </el-input>
        </div>
      </div>
      <div class="total">共 {{total}} 个任务</div>

      <div class="table" v-if="!addTask">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
        >
         <!-- <template slot="empty">
             <img src="https://static.medsci.cn/public-image/ms-image/d95c8350-59a8-11ed-b66b-937b834e3ef9_Snipaste_2022-11-01_13-47-10.png" alt="">
             <div class="noTask">
                 还没有新建任务哦，快去新建吧~
             </div>
             <el-button  @click="goTab" type="primary">新建任务</el-button>
         </template> -->
         <!-- <el-table-column
         type="selection"
         width="50"
         >
         </el-table-column> -->
          <el-table-column
            prop="id"
            label="任务ID"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="taskName"
            label="任务名称"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="taskProperty"
            :formatter="formatTagName"
            label="任务属性"
            align="center"
            min-width="95px"
          >
          <template 
          v-slot:header
          #header="scope"> 
              <el-dropdown trigger="click">
                <div>
                  <span>任务属性</span>
                  <el-icon><Filter /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="init('taskProperty',1)">日常任务</el-dropdown-item>
                    <el-dropdown-item @click="init('taskProperty',2)">新手任务</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
          </template>
          </el-table-column>
          <el-table-column
            :formatter="formatObjectType"
            prop="objectType"
            label="任务模块/模块ID"
            align="center"
            min-width="150"
          >
          <template 
          v-slot:header
          #header="scope"
          >
            <el-dropdown trigger="click">
                <div>
                  <span>任务模块/模块ID</span>
                  <el-icon><Filter /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="init('objectType','live_info')">直播</el-dropdown-item>
                    <el-dropdown-item @click="init('objectType','article')">资讯</el-dropdown-item>
                    <el-dropdown-item @click="init('objectType','course')">课程</el-dropdown-item>
                    <el-dropdown-item @click="init('objectType','exam_paper')">考试</el-dropdown-item>
                    <el-dropdown-item @click="init('objectType','ugc')">UGC</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
          </template>
          <template #default="scope">
            {{scope.row.objectType=="live_info"?"直播/":scope.row.objectType=="article"?"资讯/":scope.row.objectType=="course"?"课程/":scope.row.objectType=="exam_paper"?"考试/":scope.row.objectType=="ugc"?"UGC/":''}}<div v-if="scope.row.objectList[0]?.objectId===-1">- -</div>
              <div v-else>
                <a v-for="item in scope.row.objectList" :key="item.objectId" :href="item.gotoUrl" target="_blank">{{item.objectId}}<br></a>
              </div>
          </template>
          </el-table-column>
          <el-table-column
            prop="paymentAmount"
            label="积分额度"
            align="center"
          >
          </el-table-column>
          
          <el-table-column
            prop="taskStatus"
            label="状态"
            min-width="70px"
            align="center">
           <template #default="scope">
            <div>
                {{scope.row.taskStatus=='1'?'已生效':'未生效'}}
            </div>
           </template>
           <template 
            v-slot:header
            #header="scope"
            >
            <el-dropdown trigger="click">
                <div>
                  <span>状态</span>
                  <el-icon><Filter /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="init('taskStatus',1)">已生效</el-dropdown-item>
                    <el-dropdown-item @click="init('taskStatus',2)">未生效</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
          </template>
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间/启用时间/禁用时间"
            align="center"
            min-width="180"
          >
          <template #default="scope"> 
            {{scope.row.createdTime}}/{{scope.row.startTime?scope.row.startTime:"暂无"}}/{{scope.row.disableTime?scope.row.disableTime:"暂无"}}
          </template>
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="times"
            label="完成次数/发放数量"
            align="center"
            min-width="130"
          >
          <template #default="scope"> 
            {{scope.row.times}}/{{scope.row.grantIntegralCount}}
          </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="210px"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.update_task"
                plain
                size="mini"
                @click="goTab(scope.row)"
                >修改</el-button
              >
              <el-button
                v-if="btnList.off_task"
                plain
                size="mini"
                @click="handleUp(scope.row)"
                >{{scope.row.taskStatus===1?'禁用':'启用'}}</el-button
              >
              <el-button :disabled="scope.row.taskStatus === 1" v-if="btnList.delete_task" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 暂无任务 -->
      <div class="notable" v-if="addTask">
        <el-table
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
          empty-text=" "
        >
          <el-table-column
            prop="objectTitle"
            label="任务ID"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="content"
            label="任务名称"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="hasAttachment"
            label="任务属性"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="objectType"
            label="任务模块"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="objectType"
            label="模块ID"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="积分额度"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="状态"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="启用时间"
          
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="禁用时间"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="已完成次数"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="已发放梅花"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
          >
          </el-table-column>
        </el-table>
        <div class="content">
             <img src="https://static.medsci.cn/public-image/ms-image/d95c8350-59a8-11ed-b66b-937b834e3ef9_Snipaste_2022-11-01_13-47-10.png" alt="">
             <div class="noTask">
                 还没有新建任务哦，快去新建吧~
             </div>
             <el-button  @click="goTab()" type="primary">新建任务</el-button>
        </div>
      </div>

      <div class="pagination" v-if="!addTask">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <!-- 弹框 -->
      <el-dialog  title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.taskName }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>


  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { IntegralTask, offIntegralTask , deleteTask} from "@/request/service.js";
import classify  from  '@/constant/classify'
const { taskProperty, classifyType } = classify
import { Filter } from "@element-plus/icons"
export default {
  name: "task",
  mixins: [initModule, initScroll],
  data() {
    return {
      comment: "",
      time: "",
      deleteVisible: false,
      currentChose: {},
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      addTask:false,
    };
  },
  components:{
    Filter
  },
async mounted() {
   await this.init();
    if(this.tableData.length===0){
        this.addTask=true
    }
  },
  methods: {
  async init(type,value) {
      this.tableData=[]
      // let projectIds = [];
      // projectIds.push(this.$store.state.projectInfo.id);
      // let beginTime;
      // let endTime;
      // if (this.time && this.time.length > 0) {
      //   beginTime = this.time[0];
      //   endTime = this.time[1];
      // }
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
      };
      if(this.comment){
        params.name = this.comment
      }
      if(type==='objectType'){
        params.objectType=value
      }else if(type==='taskProperty'){
        params.taskProperty=value
      }else if(type==='taskStatus'){
        params.taskStatus=value
      }
      await IntegralTask(params).then((res) => {
        if (res.status == 200) {
          if(res.data){
            this.tableData =res.data;
            // this.tableData = []
          }else{
            this.$message.warning("未检索到相关任务信息");
            this.tableData = []
          }
          this.total = res.totalSize;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.hasAttachment) {
              val.hasAttachment = "是";
            } else {
              val.hasAttachment = "否";
            }
            if(val.objectType==="article"){
              val.objectList.forEach((item)=>{
                item.gotoUrl = 
                // `https://onc.medon.com.cn` +
              window.location.origin +
              `/news/detail/${item.objectName}`;
              })
            }else if(val.objectType==="live_info"){
              val.objectList.forEach((item)=>{
                item.gotoUrl = 
                // `https://onc.medon.com.cn` +
              window.location.origin +
              `/live/${item.objectName}`;
              })
            }else if(val.objectType==="course"){
              val.objectList.forEach((item)=>{
                item.gotoUrl = 
                // `https://onc.medon.com.cn` +
              window.location.origin +
              `/class/detail/${item.objectName}`;
              })
            }else if(val.objectType==="exam_paper"){
              val.objectList.forEach((item)=>{
                item.gotoUrl = 
                // `https://onc.medon.com.cn` +
              window.location.origin +
              `/explain?examId=${item.objectName}`;
              })
            }
          });
        }
      });
    },
    // 格式化任务类别
    formatTagName(row, column, cellValue){
       const obj = taskProperty.find(item=>item.name === cellValue)
       return obj ? obj.value :'未知'
    },
    //  格式化模块类型
    formatObjectType(row, column, cellValue){
       const obj = classifyType.find(item=>item.name === cellValue)
       return obj ? obj.value :''
    },
    formatId(row, column, cellValue){
       if(cellValue[0]===-1){
        return  '- -'
       }else{
        return cellValue
       }
    },
    handleUp(row){
       if(row.taskStatus===1){
        this.$confirm('禁用后，用户完成对应的任务将无法获得梅花,确认禁用么？', '提示', {
          confirmButtonText: '禁用',
          cancelButtonText: '取消',
        }).then(() => {
         offIntegralTask({id:row.id,type:2}).then((res) => {
          if (res.status == 200) {
          this.init();
          this.$message.success("禁用任务成功");
          }
        });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消禁用'
          });          
        });
       }else{
        offIntegralTask({id:row.id,type:1}).then((res) => {
        if (res.status == 200) {
          this.init();
          this.$message.success("启用任务成功");
        }
        });
       }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    goTab(data){
      this.$emit("go", data);
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    deleteSave() {
      let params = {
        id: this.currentChose.id
      };
      deleteTask(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.$message.success("删除任务成功");  
          this.init();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_comment {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      flex:1;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
      display: flex;
      width: 500px;
      .tl{
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 40px;
          margin-right: 20px;
          white-space: nowrap;
         
      }
    }
  }
  .table {
    ::v-deep{
      .cell{
      span{
        color: #909399;
      }
      i{
        font-size: 16px;
        vertical-align:text-bottom
      }
    }
    }
  }
  .total{
    margin-top: 30px;
  }
  .notable{
    margin-top: 10px;
    ::v-deep{
      .el-table--fit,.el-table::before{
        width: 0px;
      }
      .el-table__fixed-right::before{
        height: 0px;
      }
      // .el-table__header-wrapper{
      //   height: 45px;
      // }
      // .el-table__header{
      //   height: 45px;
      // }
    }
      display: flex;
      flex-direction: column;
      justify-content:center;
      align-items: center;
    .content{
      width: 400px;
      height: 300px;
      display: flex;
      flex-direction: column;
      justify-content:center;
      align-items: center;
      button{
        margin-top: 20px;
      }
    }
  }
}
</style>
