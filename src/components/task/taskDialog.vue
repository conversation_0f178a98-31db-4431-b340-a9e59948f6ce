<template>
  <div class="yxd_information-create">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
      @click.stop="showSearch = false;"
    >
      <div style="color: #ffca32;width: 900px;margin: 0 auto;">所有设置的任务，必须在用户登录状态下，方可生效。若开发式项目的任务中选择的模块内容未要求登录，则未登录用户到达任务要求也无法发放梅花。</div>
       <div class="dialogContent">
        <el-form :model="formData">
         <el-form-item class="col">
           <span class="tl"><span style="color:red">*</span>任务名称</span>
           <el-input style="width:58.5%" v-model.trim="formData.taskName" placeholder="请输入任务名称"></el-input>
         </el-form-item>
         <el-form-item class="col">
           <span class="tl"><span style="color:red">*</span>任务属性</span>
           <el-radio-group v-model="formData.taskProperty">
              <el-radio :disabled="disable"  :label="2">新手任务</el-radio>
              <el-radio :disabled="disable"  :label="1">日常任务</el-radio>
           </el-radio-group>
         </el-form-item>
        <el-form-item class="col" v-if="formData.taskProperty=='2'"> 
          <span class="tl"><span style="color:red">*</span>选择任务</span>
          <el-select class="task" :disabled="disable" v-model="taskValidType">
           <el-option  label="初次登录" :value="3"></el-option>
           <el-option  label="绑定微信" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="col" v-if="formData.taskProperty=='2'">
           <span class="tl"><span style="color:red">*</span>发放对象</span>
           <el-radio-group v-model="formData.taskType">
              <el-radio :disabled="disable" :label="1">
              <div class="tooltip-base-box">
                <el-tooltip
                popper-class="tips-con"
                placement="top"
                content="主站已注册过，但项目初次注册登录的用户"
                >
                <span>项目新用户</span>
                </el-tooltip>
              </div>
              </el-radio>
              <el-radio :disabled="disable" :label="2">
                <div class="tooltip-base-box">
                <el-tooltip
                popper-class="tips-con"
                placement="top"
                content="主站未注册过，并且项目初次注册登录的用户"
                >
                <span>新用户</span>
                </el-tooltip>
              </div>
              </el-radio>
           </el-radio-group>
        </el-form-item>
        <!-- 模块类型 -->
        <el-form-item class="col live" v-if="formData.taskProperty=='1'"> 
          <span class="tl"><span style="color:red">*</span>模块类型</span>
          <el-select :disabled="disable" v-model="objectType">
           <el-option label="资讯" value="article"></el-option>
           <el-option label="直播" value="live_info"></el-option>
           <el-option label="课程" value="course"></el-option>
           <el-option label="考试" value="exam_paper"></el-option>
           <el-option label="UGC" value="ugc"></el-option>
           <!-- <el-option label="指南" value="guider"></el-option>
           <el-option label="期刊" value="tool_impact_factor"></el-option> -->
          </el-select>


          <!-- <div class="textBox">
              <el-input :disabled="disable" v-model.trim="objectContent"  placeholder="请搜索选择直播ID"  style="margin-left: 20px;">
                <template #append>
                  <el-button 
                    :disabled="disable"
                    icon="el-icon-search"
                    @click.stop="idChange"
                    >
                  </el-button> 
                </template>
              </el-input>
              <ul v-if="showSearch">
                <li
                  v-for="item in searchList"
                  :key="item.id"
                  :value="item.name"
                  @click.stop="chose(item)">
                  {{ item.id+' '+item.name }}
                </li>
              </ul>
          </div> -->
          
          <div class="textBox" @focus="NameSearch" @input="NameSearch">
              <el-cascader
              :placeholder="'请搜索选择'+objectTypeName+'名称'"
              v-model="taskInputId"
              :options="searchList"
              ref="cascader"
              @change="change"
              v-if="objectTypeName!=='ugc'"
              :props="{ 
              checkStrictly: true,
              value: 'id',
              label:'objectContent',
              multiple: true
              }"
              filterable
              clearable
              :disabled="disable"
              >
              </el-cascader> 
          </div>

          <span  class="hint" v-if="objectType!=='ugc'">若不选择{{objectTypeName}}名称,则为项目全部{{objectTypeName}}有效</span>
        </el-form-item>
        <el-form-item class="col" v-if="formData.taskProperty=='1'">
           <span class="tl"><span style="color:red">*</span>任务类型</span>
           <el-radio-group v-model="formData.taskType" @change="changeTasktype">
            <!-- <el-tooltip class="item" :hide-after="0" effect="dark" content="Bottom Left 提示文字" placement="bottom-start">
              <el-button>下左</el-button>
            </el-tooltip> -->
              <el-radio v-show="objectType==='article'" :disabled="disable" :label="5">查看任务</el-radio>
              <el-radio v-show="objectType==='live_info'" :disabled="disable" :label="3">弹幕任务</el-radio>
              <el-radio v-show="objectType!=='exam_paper'&&objectType!=='ugc'"  :disabled="disable" :label="4">时长任务</el-radio>
              <el-radio v-show="objectType==='exam_paper'" :disabled="disable" :label="6">按考试分数积分</el-radio>
              <el-radio v-show="objectType==='exam_paper'" :disabled="disable" :label="7">考试通过积分</el-radio>
              <el-radio v-show="objectType==='ugc'" :disabled="disable" :label="8">提交成功得积分</el-radio>
              <el-radio v-show="objectType==='ugc'" :disabled="disable" :label="9">审核通过积分</el-radio>
           </el-radio-group>
        </el-form-item>
        <div v-show="formData.taskType===4" class="setDuration">
          <div v-show="formData.taskType===4">
              <span>观看时长达到 </span>   
              <el-input-number v-model="formData.taskDuration" :precision="1" controls-position="right"  :min="0.1" :max="10000"></el-input-number>
              <span> 分钟</span>
          </div>
        </div>
        <el-form-item class="col" v-if="formData.taskProperty=='1'&&objectType==='live_info'">
           <span class="tl"><span style="color:red">*</span>任务有效期</span>
           <el-select v-model="taskValidType">
            <el-option v-show="formData.taskType===3" label="全部" :value="0"></el-option>
            <el-option v-show="formData.taskType===3" label="直播前" :value="1"></el-option>
            <el-option label="直播中" :value="2"></el-option>
           </el-select>
        </el-form-item>
        
        <!-- <div class="col"> 
          <span class="tl"><span style="color:red">*</span>奖励梅花数量</span>
          <el-input-number :precision="0" v-model="formData.paymentAmount" controls-position="right"  :min="1" :max="10000"></el-input-number>
        </div> -->
        
         <!-- <div class="col">
           <span class="tl">外向链接</span>
           <el-input
             style="width:40%"
             v-model="formData.linkOutUrl"
           ></el-input>
         </div>
         <div class="col">
           <span class="tl"><span style="color:red">*</span>内容</span>
           <ms-editor class="editor" v-model="formData.content"></ms-editor>
         </div>
         <div class="col">
           <span class="tl"><span style="color:red">*</span>摘要</span>
           <el-input
             style="width:80%"
             type="textarea"
             :rows="3"
             @keyup.enter="summaryKeyUp = true"
             maxlength="100"
             show-word-limit
             placeholder="请输入内容"
             v-model="formData.summary"
           >
           </el-input>
         </div> -->
         <div class="col" v-if="formData.taskProperty=='2'||formData.taskProperty=='1'&&(objectType==='article'||objectType==='course'||objectType==='exam_paper'||objectType==='ugc')">
            <span class="tl">任务有效期</span>
            <el-date-picker
              v-model="lineTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
        </div>
        <el-form-item class="col" v-show="objectType==='exam_paper'&&formData.taskType==6||objectType==='ugc'&&(formData.taskType==8||formData.taskType==9)">
           <span class="tl"><span style="color:red">*</span>发放频次</span>
           <el-radio-group v-model="formData.times">
            <!-- <el-tooltip class="item" :hide-after="0" effect="dark" content="Bottom Left 提示文字" placement="bottom-start">
              <el-button>下左</el-button>
            </el-tooltip> -->
              <el-radio :disabled="disable" :label="1">只发一次</el-radio>
              <el-radio  :disabled="disable" :label="-1" v-if="formData.taskType==6">每次提交都发放</el-radio>
              <el-radio  :disabled="disable" :label="-1" v-if="formData.taskType==8">每次提交都发放</el-radio>
              <el-radio  :disabled="disable" :label="-1" v-if="formData.taskType==9">每次审核通过都发放</el-radio>
           </el-radio-group>
        </el-form-item>
         <div class="col" v-if="formData.taskType!=6"> 
          <span class="tl"><span style="color:red">*</span>奖励梅花数量</span>
          <el-input-number :precision="0" v-model="formData.paymentAmount" controls-position="right"  :min="1" :max="10000"></el-input-number>
        </div>
        </el-form>
       </div>
      <div class="footer">
        <!-- <el-button @click="save('draft')">存为草稿</el-button> -->
        <el-button @click="goBack">取消</el-button>
        <el-button @click="save('real')" type="primary">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
// import msEditor from "@/components/ms-editor/index";
// import { escapeMap, unescapeMap } from "@/utils/constant.js";
import {
  getIntegralTask,
  addTask,
  updateIntegralTask,
  liveQuery,
  getAriticlePage,
  listCourse,
  examList
} from "@/request/service.js";
// import htmlMethods from "@/utils/htmlFunction.js";
// import { getEditContent } from "@/utils/edit.js";
// import { ElMessage } from "element-plus";
import initScroll from "@/mixins/initScroll.js";
import { parseTime } from "@/utils"
import { number } from 'echarts';
export default {
  name: "task",
  mixins: [initScroll],
  // components: {
  //   msEditor,
  // },
  props: {
    edit: {
      type: Object,
      default: ()=>({}),
    },
  },
  data() {
    return {
      // activeName: "first",
      formData: {
        taskName:'',
        taskProperty:2,
        // taskValidType:'',
        // objectType:'',
        taskType:2,
        paymentAmount:1,
        // objectIds:[],
        taskStartTime:'',
        taskEndTime:'',
        taskDuration:1,
        objectList:[],
        times:""
      },
      // classGroup: [],
      // summaryKeyUp: false,
      lineTime:'',
      // region:'new',
      disable:false,
      taskValidType:'',
      objectType:'article',
      showSearch:false,
      objectIds:'',
      objectTitle:'',
      objectContent:'',
      searchList:[],
      taskProperty:'',
      // visible :false,
      antiShake:true,
      taskInputId:[]
    };
  },
  watch: {
    "formData.taskProperty":function(value){
       if(!Object.keys(this.edit).length){
         this.formData.taskType=2
         this.formData.paymentAmount=1
         this.lineTime=''
         this.objectType='article'
         this.objectIds=''
         this.objectTitle=''
         this.objectContent=''
         this.taskValidType=''
         this.formData.taskDuration=1
       }
    },
    "formData.taskType":function(value){
      if(!Object.keys(this.edit).length){
        this.formData.taskDuration=1
        if(this.formData.taskProperty=='1'){
          this.formData.paymentAmount=1
          if(this.objectType!=="exam_paper"){
            this.lineTime=''
          }
          this.taskValidType=''
        }
      }
    },
    "objectType":function(value){
      if(!Object.keys(this.edit).length){
        this.formData.taskDuration=1                                          
        if(this.formData.taskProperty=='1'){
          this.taskValidType=''
          this.formData.taskType=''
          this.formData.paymentAmount=1
          this.lineTime=''
          this.objectContent=''
          this.searchList=[]
          let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader?.clearValue(obj)
            }catch(err){
                this.$refs.cascader?.handleClear(obj)
            }
            
        }
      }
    },
    taskInputId:{
      handler(newValue,oldValue){
          if(newValue.length>10){
            this.$message.warning("最多只支持选择10项");
            this.$nextTick(()=>{
              this.taskInputId = oldValue
            })
          }
      }
    }
  },
  async mounted() {
    this.initScroll();
    if (Object.keys(this.edit).length !== 0) {
      await this.initTag();
      // 按钮禁用
      this.disable=true
    }
  },
  methods: {
    changeTasktype(){
      if(this.formData.taskType==7){
          this.formData.paymentAmount=""
        }
        if(this.formData.taskType==6){
          this.formData.times=""
        }
    },
    
    initTag() {
      let params = {
        id:this.edit.id
      };
      getIntegralTask(params).then((res) => {
        if (res.status == 200) {
          this.formData=res.data
          if(res.data.taskProperty=='1'){
            if(res.data.objectList[0].objectId===-1){
              this.searchList=[]
            }else{
              this.searchList = res.data.objectList.map(item=>{
              return {
                id:item.objectId,
                name:item.objectName,
                objectContent:item.objectId+" "+item.objectName
              }
              });
              res.data.objectList.map((item)=>{
                  this.taskInputId.push(item.objectId)
              })
            }
            this.objectType=res.data.objectList[0].objectType
            if(res.data.objectList[0].objectType==='article'||res.data.objectList[0].objectType==='course'||res.data.objectList[0].objectType==='exam_paper'||res.data.objectList[0].objectType==='ugc'){
              if(res.data.taskStartTime===1640966400000&&res.data.taskEndTime===253370736000000){

              this.lineTime=''
             }else{

              this.lineTime=[
              // 时间戳转换
               parseTime(res.data.taskStartTime, '{y}-{m}-{d} {h}:{i}:{s}'),parseTime(res.data.taskEndTime, '{y}-{m}-{d} {h}:{i}:{s}')
             ]
             }
            }
          }else{
            if(res.data.taskStartTime===1640966400000&&res.data.taskEndTime===253370736000000){
              this.lineTime=''
            }else{
              this.lineTime=[
              // 时间戳转换
               parseTime(res.data.taskStartTime, '{y}-{m}-{d} {h}:{i}:{s}'),parseTime(res.data.taskEndTime, '{y}-{m}-{d} {h}:{i}:{s}')
            ]
            }
          }
          if(res.data.taskValidType===0||res.data.taskValidType===1||res.data.taskValidType===2||res.data.taskValidType===3||res.data.taskValidType===4){
             this.taskValidType=res.data.taskValidType
          }else{
             this.taskValidType=''
          }
          // this.classGroup = res.data;
        }
      });
    },
    save() {
      if(this.formData.taskName.length===0||this.formData.taskName.length>30){
         this.$message.error("任务名称应为1~30个字符");
         return
      } else if(this.formData.taskType===''){
         this.$message.error("发放对象或任务类型不能为空");
         return
      } else if(this.formData.taskProperty=='1'&&this.objectType===''){
         this.$message.error("模块类型不能为空");
         return
      } else if(this.objectType==='live_info'&&this.taskValidType===''){
         this.$message.error("任务有效期不能为空");
         return
      } else if(this.formData.taskProperty=='2'&&this.taskValidType===''){
         this.$message.error("任务不能为空");
         return
      }else if(this.formData.times===''&&this.formData.taskType==6){
        this.$message.error("发放频次不能为空");
        return
      }else if(this.objectType==='exam_paper'&&this.taskInputId.length==0){
        this.$message.error("考试任务不能为空");
        return
      }

      let publishedStartTime='2022-01-01 00:00:00';
      let publishedEndTime='9999-01-01 00:00:00';
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
        this.formData.taskStartTime=publishedStartTime
        this.formData.taskEndTime=publishedEndTime
        if(this.formData.taskProperty=='1'){
          // if(this.objectContent){
          //   if(this.objectIds&&this.objectTitle){
          //     this.formData.objectList=[{
          //       objectId:this.objectIds,
          //       objectName:this.objectTitle
          //       }]
          //   }else{
          //     this.formData.objectList=[{
          //       objectId:-1
          //     }]
          //   }
          // }else{
          //    this.formData.objectList=[{
          //     objectId:-1
          //   }]
          // }
          if(this.formData.objectList.length===0){
            this.formData.objectList=[{
              objectId:-1
            }]
          }
          if(this.objectType){
          this.formData.objectType=this.objectType
          }
        }
        if(this.taskValidType!==''){
          this.formData.taskValidType=this.taskValidType
        }
        
        
      if (!Object.keys(this.edit).length) {
        addTask(this.formData).then((res) => {
          if(res.message==='同属性任务已存在！'){
            this.goBack();
          }
          if (res.status == 200) {
            this.$message.success("添加任务成功");
            this.goBack();
          }
        });
      } else {
        // let params = {
        //   taskName:this.formData.taskName,
        //   taskValidType:this.formData.taskValidType,
        //   paymentAmount:this.formData.paymentAmount,
        //   taskStartTime:this.formData.taskStartTime,
        //   taskEndTime:this.formData.taskEndTime,
        //   taskProperty:this.formData.taskProperty,
        // }
        updateIntegralTask(this.formData).then((res) => {
          if (res.status == 200) {
            this.$message.success("修改任务成功");
            this.goBack();
          }
        });
      }
    },
    goBack() {
      this.$emit("back");
    },
    idChange() {
      if(this.objectContent){
        let value1 =this.objectContent.split(' ')[0]
        let value2 =this.objectContent.split(' ')[1]
       if(!isNaN(Number(value1))&&isNaN(Number(value2))){
           this.objectTitle=value2
        }else{
         this.objectTitle=this.objectContent
        }
      }else{
         this.objectTitle=this.objectContent
      }
      if(this.objectType==='article'){
        let params = {
          pageIndex: 1,
          pageSize:50,
          value:this.objectTitle,
          approvalStatus:1,
          projectId: this.$store.state.projectInfo.id,
        };
        getAriticlePage(params).then((res) => {
          if (res.status == 200) {
            this.searchList = res.data.map(item=>{
              return {
                id:item.id,
                name:item.title,
                objectContent:item.id+" "+item.title
              }
            });
          }
        });
      }else if(this.objectType==='live_info'){
        let params = {
          pageIndex: 1,
          pageSize:50,
          searchTitle:this.objectTitle,
          projectId: this.$store.state.projectInfo.id,
        };
        liveQuery(params).then((res) => {
          if (res.status == 200) {
            this.searchList = res.data.map(item=>{
              return {
                id:item.id,
                name:item.name,
                objectContent:item.id+" "+item.name
              }
            });
          }
        });
      }else if(this.objectType==='course'){
        let params = {
          pageIndex: 1,
          pageSize:50,
          title:this.objectTitle,
          projectId: this.$store.state.projectInfo.id,
        };
        listCourse(params).then((res) => {
          if (res.status == 200) {
            this.searchList = res.data.map(item=>{
              return {
                id:item.id,
                name:item.title,
                objectContent:item.id+" "+item.title
              }
            });
          }
        });
      }else if(this.objectType==='exam_paper'){
        let params = {
        pageIndex: 1,
        pageSize: 50,
        title: this.title,
        filterOver:true
      };
      examList(params).then((res) => {
        this.total = res.totalSize;
        if (res.status == 200) {
            this.searchList = res.data.map(item=>{
              console.log(item)
              return {
                id:item.id,
                name:item.title,
                objectContent:item.id+" "+item.title,
                lineTime:[parseTime(item.startTime, '{y}-{m}-{d} {h}:{i}:{s}'),parseTime(item.endTime, '{y}-{m}-{d} {h}:{i}:{s}')
]
              }
            });
          }
      });
      }
      this.showSearch = true;
    },
    //   chose(item) {
    //   this.searchList = [];
    //   this.objectIds = item.id
    //   this.objectTitle = item.name;
    //   this.objectContent = item.id+' '+item.name
    //   this.showSearch = false;
    // },
    NameSearch(){
      let time 
      if(this.antiShake){
        this.antiShake = false
        this.idChange()
        time = setTimeout(()=>{
         this.antiShake=true
      },1000)
      }
    },
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.formData.objectList=[]
       nodesObj.forEach((item)=>{
         this.formData.objectList.push({objectId:item.data.id,objectName:item.data.name})
       })
      
    }, 
    // openPopover(){
    //   this.$refs['a'].$el.click()
    //   this.visible = true
     
    // },
    // closePopover(){
    //   this.$refs['a'].$el.mouseleave()
    //   this.visible = false
    // }
    
  },
  computed:{
      objectTypeName:function () {
         if(this.objectType==='article'){
          return  '资讯'
          }else if(this.objectType==='live_info'){
          return  '直播'
          }else if(this.objectType==='course'){
          return  '课程'
          }else if(this.objectType==='exam_paper'){
          return  '考试'
          }else if(this.objectType==='ugc'){
          return  'ugc'
          }
          return  ''
      },
  },
  filters:{
    aaa:function(value){
      if(value==='article'){
        return  '资讯'
      }
    }
  }
};
</script>
<style scoped lang="scss">
.el-select {
  width: 20% !important;
}
.yxd_information-create {
  #container{
    height: 100vh;
    padding-bottom: 200px;
    .dialogContent { 
      margin: 0 auto;
      width: 900px;
    .col {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      .tl {
        display: inline-block;
        width: 100px;
        opacity: 1;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-right: 20px;
        white-space: nowrap;
        text-align: right;
      }
      ::v-deep{
       .el-date-editor--datetimerange{
        width: 58.5% !important;
      }
      }
      .task{
        width: 58.5% !important;
      }
    }
    ::v-deep{
      .live>div{
        display: flex;
        align-items: center;
      }
    }
    .search{
      position: relative;
      .list{
       
        background-color: #999;
        position: absolute;
        top: 40px;
        left: 25px;
        right: 0;
      }
    }
    // .textBox {
    //       position: relative;
    //       ul {
    //         width: 100%;
    //         position: absolute;
    //         top: 50px;
    //         left: 20px;
    //         z-index: 999;
    //         overflow: auto;
    //         border-radius: 4px;
    //         box-sizing: border-box;
    //         background: #fff;
    //         border: 1px solid #e4e7ed;
    //         box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    //         word-wrap: break-word;
    //         font-size: 12px;
    //         line-height: 1.2;
    //         min-width: 10px;
    //         padding: 6px 0;
    //         li {
    //           font-size: 14px;
    //           padding: 0 32px 0 20px;
    //           position: relative;
    //           white-space: nowrap;
    //           overflow: hidden;
    //           text-overflow: ellipsis;
    //           color: #606266;
    //           height: 34px;
    //           line-height: 34px;
    //           box-sizing: border-box;
    //           cursor: pointer;
    //         }
    //         li:hover {
    //           background-color: #f5f7fa;
    //         }
    //       }
    //       ::v-deep {
    //         .el-input-group__append{
    //           display: table-cell;
    //         }
    //       }
    //     }
    
        .textBox {
          width: 220px;
          margin-left: 20px;
          div{
          width: 100%;
          }
          ::v-deep{
            .el-input--suffix{
              display: block;
            }
            .el-cascader__tags{
              display: block;
              span{
                width: 100%;
              }
              input{
                width: 90%;
              }
            }
         }
        }
        .setDuration{
          margin-left: 120px;
          margin-top: -10px;
          height: 40px;
          font-size: 14px;
          
          // .hint{
          //   width: 100px;
          //   height: 100px;
          // }
        }
        .hint{
          display:inline-block;
          width: 330px;
          margin-left: 10px; 
          color: #cccccc;

        }
  }
  // .setting {
  //   .col {
  //     display: flex;
  //     align-items: center;
  //     margin-right: 20px;
  //     margin-top: 20px;
  //     margin-bottom: 20px;
  //     .tl {
  //       width: 70px;
  //       opacity: 1;
  //       font-size: 14px;
  //       font-family: PingFangSC, PingFangSC-Regular;
  //       font-weight: 400;
  //       color: rgba(0, 0, 0, 0.85);
  //       line-height: 22px;
  //       margin-right: 20px;
  //       white-space: nowrap;
  //       text-align: right;
  //     }
  //     .upload {
  //       ::v-deep .avatar-uploader {
  //         .el-upload--text {
  //           width: 120px;
  //           height: 120px;
  //           line-height: 120px;
  //           background-color: #fff;
  //           border: 1px dashed #d9d9d9;
  //           border-radius: 6px;
  //           box-sizing: border-box;
  //           text-align: center;
  //           cursor: pointer;
  //           position: relative;
  //           overflow: hidden;
  //         }
  //       }
  //       .warn {
  //         font-size: 14px;
  //         font-family: PingFangSC, PingFangSC-Regular;
  //         font-weight: 400;
  //         color: rgba(0, 0, 0, 0.85);
  //         line-height: 22px;
  //         margin-right: 20px;
  //         white-space: nowrap;
  //       }
  //     }
  //     ::v-deep {
  //       .el-select{
  //          width: 100%;
  //       }
  //     }
      
  //   }
  // }
  .footer {
    text-align: right;
    margin: 0 auto;
    width: 370px;
  }
  }
  @media only screen and (max-width: 1200px) {
    #container {
        height: calc(100vh + 180px);
    }
  }
}
</style>

<style  lang="scss">
.tooltip-base-box {
  display: inline-block;
  width: 60px;
}
.tooltip-base-box .row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tooltip-base-box .center {
  justify-content: center;
}
.tooltip-base-box .box-item {
  width: 110px;
  margin-top: 10px;
}
</style>