// 分类类别
export default {
    classifyType:[
        {
            name:'article',
            value:'资讯'
        },
        {
            name:'course',
            value:'课程'
        },
        {
            name:'live_info',
            value:'直播'
        },
        {  
            name: "case",
            value: "病例",
        },
        {
            name: "survey",
            value: "调研",
        },
        {
            name:'eda',
            value:'医讯达'
        },
        {
            name:'guider',
            value:'指南'
        },
        {
            name:'tool_impact_factor',
            value:'期刊'
        },
        {
            name:'exam_paper',
            value:'考试'
        },
        {
            name:'ugc',
            value:'UGC'
        },
    ],
    taskProperty:[
        {
            name:'1',
            value:'日常任务'   
        },
        {
            name:'2',
            value:'新手任务'   
        }
    ],
    // resourceId:[
    //     {
    //         name:'article',
    //         value:'资讯'
    //     },
    //     {
    //         name:'guider',
    //         value:'指南'
    //     },
    //     {
    //         name:'tool_impact_factor',
    //         value:'期刊'
    //     },
    //     {
    //         name:'course',
    //         value:'课程'
    //     },
    //     {
    //         name:'live_info',
    //         value:'直播'
    //     },
    // ]
}
