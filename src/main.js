import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import installElementPlus from "./plugins/element";
import installComponent from "./components/index";
import "./assets/css/icon.css";
import AvueFormDesign from 'medsci-avue-form-design'
// import { registerMicroApps } from 'qiankun'
// registerMicroApps([
//     {
//         name: 'vueApp',
//         entry: process.env.NODE_ENV == "production" ? `//${window.location.host}/pifu/`: '//localhost:7105',
//         container: '#qiankun_container',
//         activeRule: process.env.NODE_ENV == "production" ? '/admin/#/tob/' : '/#/tob/'
//     }
// ])


const app = createApp(App);
// 在 Vue 的原型上添加全局字段
app.config.globalProperties.cosUrl = 'img.medsci.cn'
app.component("avue-form-design", AvueFormDesign);
installElementPlus(app);
installComponent(app);

app.use(store).use(router).mount("#app");
