export default {
    data() {
      return {
        filterData:[],
        btnStatus:false
      };
    },
    watch: {},
    created() {},
    mounted() {
 
    },
    methods: {
        filterList(){
            console.log(this.filterData)
            if(this.btnStatus){
              this.tableData=this.filterData
              this.btnStatus=false
            }else{
              this.tableData=this.tableData.filter((e)=> {return !e.unClassify})
              this.btnStatus=true
            }
          },
    },
  };
  