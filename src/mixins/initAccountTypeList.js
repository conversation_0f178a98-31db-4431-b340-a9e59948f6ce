export default {
  data() {
    return {
      accountTypeList: [],
    };
  },
  watch: {},
  computed: {
    accountType() {
      return this.$store.state.projectInfo.accountType;
    }
  },
  created() {},
  mounted() {
    this.getAccountTypeList()
  },
  methods: {
    getAccountTypeList() {
      // 0医生1患者2两者都存在
      if(this.accountType == 0){
        this.accountTypeList = [
          {
            value: 0,
            label:'医生'
          }
        ]
      }else if(this.accountType == 1){
        this.accountTypeList = [
          {
            value: 1,
            label:'患者'
          }
        ]
      }else if(this.accountType == 2){
        this.accountTypeList = [
          {
            value: 0,
            label:'医生'
          },
          {
            value: 1,
            label:'患者'
          }
        ]
      }
    }
  }
};
