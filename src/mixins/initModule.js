export default {
  data() {
    return {
      btnList: {},
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.btnShow();
  },
  methods: {
    findInnerRoutes(menuw, result) {
      menuw.forEach(menu => {
        if (menu.childMenu && menu.childMenu.length > 0) {
          for (const child of menu.childMenu) {
            if (child.route == this.$route.name) {
              if(child.childMenu && child.childMenu.length > 0){
                child.childMenu.forEach(i => {
                  if(i.menuType == 2){
                    result.push(i.route)
                    if(i.childMenu && i.childMenu.length > 0){
                      i.childMenu.forEach(j => {
                        result.push(j.route)
                        if(j.childMenu && j.childMenu.length > 0){
                          j.childMenu.forEach(q => {
                            result.push(q.route)
                          })
                        }
                      })
                    }
                  }
                })
              }
            }
            this.findInnerRoutes(child.childMenu, result);
          }
        }

      });
    },
    btnShow() {
      const innerRoutes = [];
      this.findInnerRoutes(this.$store.state.menuInfo, innerRoutes);
      innerRoutes.forEach((c) => {
        this.btnList[c] = true;
      });
      // this.$store.state.menuInfo.forEach((val) => {

      //   if (val.childMenu.length > 0) {
      //     val.childMenu.forEach((d) => {
      //       if (d.route == this.$route.name) {
      //         d.childMenu.forEach((c) => {
      //           this.btnList[c.route] = true;
      //         });
      //       }
      //     });
      //   }
      // });
    },
  },
};
