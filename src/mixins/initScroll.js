export default {
  data() {
    return {};
  },
  watch: {},
  created() {},
  mounted() {},
  computed: {
    isScroll() {
      return this.$store.state.isScroll;
    },
  },
  methods: {
    initScroll() {
      this.$nextTick(() => {
        let scroll = this.$refs.container.scrollHeight;
        let client = this.$refs.container.clientHeight;
        if (scroll > client) {
          this.$store.commit("setIsScroll", true);
        } else {
          this.$store.commit("setIsScroll", false);
        }
        document.getElementById("container").addEventListener("scroll", () => {
          let scrollHeight = this.$refs.container.scrollHeight;
          let clientHeight = this.$refs.container.clientHeight;
          if (scrollHeight > clientHeight) {
            this.$store.commit("setIsScroll", true);
          }
          // 滚动条顶部到浏览器顶部高度
          let scrollTop = this.$refs.container.scrollTop;
          if (clientHeight + scrollTop + 20 == scrollHeight) {
            this.$store.commit("setIsScroll", false);
          }
        });
      });
    },
  },
};
