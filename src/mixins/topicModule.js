import { provide } from "vue";

export default {
  data() {
    return {
      btnList: {},//题库list
    };
  },
  watch: {},
  created() { },
  mounted() {
    this.btnShow();
  },
  methods: {
    btnShow() {
      this.$store.state.menuInfo.forEach((val) => {
        if (val.childMenu.length > 0) {
          val.childMenu.forEach((d) => {
            if (d.route == "exam"|| d.route == "backstage_user_admin" ) {
              d.childMenu.forEach((e)=>{
                this.btnList[e.route] =true
                e.childMenu.forEach(m => {
                  this.btnList[m.route] =true
                  if(m.childMenu.length>0){
                    m.childMenu.forEach(s => {
                      this.btnList[s.route]=true
                    });
                  }
                });
              })
            }
            if (d.route == "course") {
              d.childMenu.forEach((e)=>{
                this.btnList[e.route] =true
                e.childMenu.forEach(m => {
                  this.btnList[m.route] =true
                  if(m.childMenu.length>0){
                    m.childMenu.forEach(s => {
                      this.btnList[s.route]=true
                    });
                  }
                });
              })
            }
          });
        }
      });
    },
  },
};
