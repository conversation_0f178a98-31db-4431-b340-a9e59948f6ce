import ECharts from "echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "echarts/charts";

use([
  <PERSON>vas<PERSON><PERSON>er,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,

  // 增加需要的图表
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  LineChart,
  Scatter<PERSON>hart,
  LinesChart,
]);

// register globally
export default (app) => {
  app.component("v-chart", ECharts);
};
