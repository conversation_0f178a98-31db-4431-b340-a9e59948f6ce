export default {
  getYxdProjectDetail: `/medsci-yxd-project/getYxdProjectDetail`,
  getYxdProjectDetailByOrigin: `/medsci-yxd-project/getYxdProjectDetailByOrigin`,
  login: `/sys/login`,
  autoLogin: `/sys/autoLogin`,
  sendSms: `/sys/login/sendSms`,
  logout: `/sys/logout`,
  saveSysMenu: `/menu/saveSysMenu`,
  sysMenuTree: `/menu/sysMenuTree`,
  sysAdminMenuTree:`/admin/menu/sysMenuTree`,
  bundleMenu: `/role/bundleMenu`,
  bundleAdminMenu: `/admin/role/bundleMenu`,
  bundleUser: `/role/bundleUser`,
  bundleAdminUser: `/admin/role/bundleUser`,
  joinAdmin: `/role/joinAdmin`,
  menu: `/role/menus`,
  adminMenu: `/admin/role/menus`,
  query: `/role/query`,
  adminQuery: `/admin/role/query`,
  removeAdmin: `/role/removeAdmin`,
  removeSysRole: `/role/removeSysRole`,
  removeAdminSysRole: `/admin/role/removeSysRole`,
  removeUser: `/role/removeUser`,
  removeAdminUser: `/admin/role/removeUser`,
  saveSysRole: `/role/saveSysRole`,
  saveAdminSysRole: `/admin/role/saveSysRole`,
  updateSysRole: `/role/updateSysRole`,
  updateAdminSysRole: `/admin/role/updateSysRole`,
  users: `/role/users`,
  adminUsers: `/admin/role/users`,
  addUser: `/medsci-yxd-user/add/user`,
  addAdminUser:`/medsci-yxd-admin-user/add/user`,
  importUser: `/medsci-yxd-user/batch/import/user`,
  importPatientUser:`/medsci-yxd-user/batch/import/patientUser`,
  importAdminUser:`/medsci-yxd-admin-user/batch/import/user`,
  deleteUser: (userId,accountType) => `/medsci-yxd-user/delete/user?userId=${userId}&accountType=${accountType}`,
  deleteAdminUser:(userId) => `/medsci-yxd-admin-user/delete/user?userId=${userId}`,
  downLoad: `/medsci-yxd-user/download/template`,
  downloadPatientTemplate: `/medsci-yxd-user/download/patientTemplate`,
  getUserList: `/medsci-yxd-user/get/list`,
  getUserAdminList: `medsci-yxd-admin-user/get/list`,
  getDetail: `/medsci-yxd-user/get/user`,
  approveUser: `/medsci-yxd-user/approveAuthenticationFile`,
  approvePerfectInfo: `/medsci-yxd-user/approvePerfectInfo`,
  isUserInfoPerfect: (userId) => `/medsci-yxd-user/isUserInfoPerfect/${userId}`,
  updateUser: `/medsci-yxd-user/update/user`,
  updateAdminUser: `/medsci-yxd-admin-user/update/user`,
  uploadAvatar: `/medsci-yxd-user/upload/avatar`,
  uploadAdminAvatar: `/medsci-yxd-user/upload/avatar`,
  themeColorList: `/medsciThemeColor/getPage`,
  openStatus: `/medsciThemeColor/openStatus`,
  saveCustomColor: `/medsciThemeColor/saveCustomColor`,
  addTool: `/tool/addTool`,
  batchDealTools: `/tool/batchDealTools`,
  detailTool: `/applicationTool/detailTool`,
  getToolPage: `/tool/getToolPage`,
  updateTool: `/tool/updateTool`,
  setSort: `/tool/setSort`,
  dealAdvertisementByBatchId: `/advertisement/dealAdvertisementByBatchId`,
  getAdvertisementPage: `/advertisement/getAdvertisementPage`,
  getAdvertisementSpaceListByName: `/advertisement/getAdvertisementSpaceListByName`,
  saveAdvertisement: `/advertisement/saveAdvertisement`,
  updateAdvertisementById: `/advertisement/updateAdvertisementById`,
  batchDealComment: `/comment/batchDeal`,
  getCommentsPage: `/comment/getCommentsPage`,
  addAriticle: `/article/addAriticle`,
  batchEdit: `/article/batchEdit`,
  impactFactorDelete:'impactFactor/delete_data',
  formBatchDelete:'form/batch/delete',
  guiderBatchDelete: `/guider/batchDelete`,
  batchLineModule:`/common/batchOnOrOfflineModule`,
  formBatchAudit:`form/batch/audit`,
  getAriticleById: `/article/getAriticleById`,
  getAriticlePage: `/article/getAriticlePage`,
  getImpactFactorPage: `impactFactor/page_list`,
  getGuiderPage: `/guider/list`,
  updateAriticle: `/article/updateAriticle`,
  surveyGetUvRecord:'medsciSurvey/getUvRecord',
  surveyGetRecord:'medsciSurvey/getRecord',
  deleteCourse: `/course/delete`,
  listCourse: `/course/list`,
  offlineCourse: `/course/offline`,
  onlineCourse: `/course/online`,
  liveQuery: `/live/query`,
  batchDelete: `/leaveMessage/batchDelete`,
  leaveQuery: `/leaveMessage/query`,
  replyMessage: `/leaveMessage/replyMessage`,
  dealSinglePage: `/singlePage/dealSinglePage`,
  getCompanyByProjectId: (id) =>
    `/singlePage/getCompanyByProjectId?projectId=${id}`,
  getPageForSinglePage: `/singlePage/getPageForSinglePage`,
  getSinglePageById: `/singlePage/getSinglePageById`,
  insertSinglePage: `/singlePage/insertSinglePage`,
  updateSinglePage: `/singlePage/updateSinglePage`,
  deleteBeOfflineLive:`/live/delete/live`,
  deleteGuiderTag:`/guider/batchDelete`,
  addTag: `/medsci-yxd-tag/add/tag`,
  deleteTag: `/medsci-yxd-tag/delete/tag`,
  getTag: `/medsci-yxd-tag/get/tag`,
  listTag: `medsci-yxd-tag/list/tag/by/tagType`,
  updateTag: `/medsci-yxd-tag/update/tag`,
  updateSort:(ids)=> `/medsci-yxd-tag/update/tag/sort?ids=${ids}`,
  getCourseTag: `/course/get/course/tag`,
  updateCourseTag: `/course/update/course/tag`,
  deleteCourseTag: `/course/delete/course/tag`,
  tagTypeList: `/medsci-yxd-tag/list/tag/by/tagType`,
  perfectInfo:(accountType) => `/config/perfectInfo?accountType=${accountType}`,
  updatePerfectInfo: `/config/updatePerfectInfo`,
  authentication: `/config/authentication`,
  accessAfterMandatoryCertification: `/config/accessAfterMandatoryCertification`,
  perfectInfoApproval: `/config/perfectInfoApproval`,
  getCountByYxd: (id, projectId) =>
    `/advertisement/getCountByYxd?spaceId=${id}&projectId=${projectId}`,
  countHomeAccount: `/home/<USER>
  countHomeArticle: `/home/<USER>
  countHomeCourse: `/home/<USER>
  countHomeLive: `/home/<USER>
  exportUserInfo: (id) => `/live/export/userInfo?liveId=${id}`,
  getStatistics: `/home/<USER>
  getUserUvByDateType: `/home/<USER>
  userRegisterData: `/home/<USER>
  getMeetingList: `/medsci-meeting/getMeetingList`,
  getMeetingAttachments: `/medsci-meeting/getMeetingAttachments`,
  verifyMeeting: `/medsci-meeting/verifyMeeting`,
  setCourseLevel: `/course/setLevel`,
  getMedsciSurveyPage: `/medsciSurvey/getMedsciSurveyPage`,
  surveyOnOffLine: `/medsciSurvey/onOffLine`,
  getRoleList: `/common/projectRoleList`,
  getRoleQuery:`/common/moduleRoleById`,
  getModuleRoles:`/common/moduleRolesByIds`,
  getModuleTags:`/common/moduleTagsByIds`,
  delCancelRole:`/common/cancelRole`,
  delCancelTag:`/common/cancelTag`,
  setDelePower: `/common/setCancelRole`,
  setCancelTag: `/common/setCancelTag`,
  setcoursePower: `/course/setCancelRole`,
  setDirectional: `/config/setDirectional`,
  setRecommendStatus: `/config/setRecommendStatus`,
  setIntegralMallStatus: `/config/setIntegralMallStatus`,
  getIntegralMallStatus: `/config/getIntegralMallStatus`,
  setIntegralMenuStatus: `/config/setIntegralMenuStatus`,
  setViewCountShow: `/config/setViewCountShow`,
  exportUserBehavior:(articleId)=> `/article/export/article?id=${articleId}`,
  exportUserCase:(caseId)=> `/medsciCaseAdmin/export/case?id=${caseId}`,
  exportUserEda:(edaId)=> `/medsciYxdEdaArticle/export/eda?id=${edaId}`,
  exportUserGuider:(id)=> `/guider/export/userinfo?id=${id}`,
  Directional: `/config/getDirectional`,
  RecommendStatus: `/config/getRecommendStatus`,
  IntegralMenuStatus: `/config/getIntegralMenuStatus`,
  wxConfigGetWx: `/wxConfig/getWx`,
  IntegralStatus: `/config/integralStatus`,
  ViewCountShow: `/config/getViewCountShow`,
  directionalMenuStatus:(projectId) => `/config/directionalMenuStatus/${projectId}`,
  // 后台系统 - 系统配置
  background: '/config/project/background',
  getBackground: '/config/getBackground',
  loginType: '/config/loginType',
  getLoginType: '/config/getLoginType',
  addCustomFiled: '/config/addCustomFiled',
  customFiledList: '/config/customFiledList',
  // /definedField/selectedUserDefinedFieldList/{projectId}
  getDefinedFieldList: (projectId, accountType) => `definedField/userDefinedFieldList/${projectId}?accountType=${accountType}`,
  getSelectedUser: (projectId) => `definedField/selectedUserDefinedFieldList/${projectId}`,
  deleteCustomFiled: '/config/deleteCustomFiled',
  getTokenExpire: (id) => `/config/getTokenExpire?projectId=${id}`,
  setTokenExpire: '/config/setTokenExpire',
  exportForm: `/medsciSurvey/excelExport`,
  getCaseList: '/medsciSurvey/caseList',
  getSubmitCaseInfo: '/medsciSurvey/getSubmitCaseInfo',
  updateCaseById: '/medsciSurvey/updateCaseById',
  pageListCase: '/medsciSurvey/pageListCase',
  getPostsPage: '/medsciPosts/getPostsPage',
  banchDealPosts: '/medsciPosts/banchDealPosts',
  recommendOrStickyPosts: '/medsciPosts/recommendOrStickyPosts',
  getMedsciCaseAdminPage: '/medsciCaseAdmin/getMedsciCaseAdminPage',
  caseOnOffLine: '/medsciCaseAdmin/onOffLine',
  edaGetAriticlePage: '/medsciYxdEdaArticle/getAriticlePage',
  edaDeleteTag: '/medsciYxdEdaArticle/delete/eda/tag',
  edaBatchEdit: '/medsciYxdEdaArticle/batchEdit',
  listUserDefinedFieldByFieldName:'/definedField/listUserDefinedFieldByFieldName',
  updateUserDefinedFieldById: '/definedField/updateUserDefinedFieldById',
  getUserDefinedFieldById: '/definedField/getUserDefinedFieldById',
  deleteUserDefinedFieldById: '/definedField/deleteUserDefinedFieldById',
  addUserDefinedField: '/definedField/addUserDefinedField',
  userSort:'/definedField/sort',
  courseSendNotice: '/course/sendNotice',
  exportViewLog: (id) => `/course/exportViewLog/${id}`,
  yxdExportUserInfo: '/medsci-yxd-user/export/userInfo',
  yxdExportAdminUserInfo: '/medsci-yxd-admin-user/export/userInfo',
  exportImpactFactor:'impactFactor/export_userinfo',
  exportPatientUserInfo: '/medsci-yxd-user/export/patientUserInfo',
  getDefinedField:'definedField/list',
  getAdminDefinedField:'definedField/list',
  editDefinedField:'medsci-yxd-user/details',
  editAdminDefinedField:'medsci-yxd-admin-user/details',
  addUserDefined:'/definedField/userDefinedFieldSelect',
  delUserDefined:'/definedField/operateUserDefinedField',
  formDetail: (formId,projectId) => `/form/detail/${formId}/${projectId}`,
  formMaterial:'/form/page',
  getCustomFormByIdApo:'/medsciSurvey/getCustomFormByIdApo',
  saveCustomFormByApo:'/medsciSurvey/saveCustomFormByApo',
  updateCustomFormByApo: '/medsciSurvey/updateCustomFormByApo',
  getFormPage:'/form/page',
  makeRuleCheck:'/medsciSurvey/rule/check',
  getMedsciAddress:'/medsci-yxd-user/getMedsciAddress',
  getMedsciTreeAddress:'/medsci-yxd-user/getMedsciTreeAddress',
  saveForm:'form/create',
  updateForm:'form/update',
  // deleteBeOfflineSurvey:'/medsciSurvey/batchDelete',
  deleteBeOfflineSurvey:'/medsciSurvey/delete/survey',
  impactFactorExportSearch:'impactFactor/export_search',
  integralExport:'integral_log/export',
  addTask: `/integralTask/add/task`,
  IntegralTask: `/integralTask/list/task`,
  getIntegralTask: `/integralTask/get/task`,
  updateIntegralTask: `/integralTask/update/task`,
  offIntegralTask: `/integralTask/off/task`,   
  setIntegral: `/config/setIntegral`,  
  wxConfigAddWx: `/wxConfig/addWx`, 
  wxConfigDelWx: `/wxConfig/delWx`, 
  integralList: `/integral_log/list`,   
  deleteTask: `/integralTask/delete/task`,
  artificialSend: `/integral_log/artificialSend`,   
  addQuestionBank: `/question/bank/add`,   
  deleteQuestionBank: `/question/bank/delete`,   
  QuestionBank: `/question/bank/list`,   
  editQuestionBank: `/question/bank/edit`,   
  Question: `/question/list`, 
  addQuestion: `/question/add`, 
  editQuestion: `/question/edit`, 
  deleteQuestion: `/question/batchDelete`, 
  detailQuestion:(id)=> `/question/detail?questionId=${id}`, 
  detailBankQuestion:(id)=> `/question/bank/detail?questionBankId=${id}`, 
  medsciLabelList:`/medsciLabel/list`, 

  batchAddQuestionsByText: `/question/batchAddQuestionsByText`, 
  batchAddQuestionsByExcel: `/question/batchAddQuestionsByExcel`, 
  // 量表批量导入
  importScaleQuestionsByExcel:"question/importScaleQuestionsByExcel",
  // 量表批量导出
  exportScaleQuestions:"/question/exportScaleQuestions",
  // 复制量表考试
  copy:(id)=>`/examination/copy?examId=${id}`,
  exportQuestions: `/question/exportQuestions`, 

  // 病例全部导出
  exportCaseList:'/medsciCaseAdmin/export/caseList',
  // 咨询全部导出
  exportArticleList:"/article/export/articleList",
  // 直播全部导出
 exportLive: "/live/export/live",
//  课程全部导出
// exportViewList:"/course/exportViewList",
   exportViewList:"course/batchExportViewList",
//  医迅达全部导出
edaList:"/medsciYxdEdaArticle/export/edaList",

  // wml题库
  batchDelete:"/question/batchDelete",
  //试卷列表
  paperList:"/examinationPaper/list",
  // 考试结果分析列表
  settingInitList:(paperId)=>`/examination/result/settingInitList?paperId=${paperId}`,
  // 题库列表
  questionBankList:(mode=0)=>`/examinationPaper/questionBankList?mode=${mode}`,
  questionList:(bankId,mode)=>`/examinationPaper/questionList?bankId=${bankId}&mode=${mode}` ,
  // 提交试卷
  addPaper:"/examinationPaper/add",
  // 试卷详情
 paperDetail:(paperId)=>`/examinationPaper/detail?paperId=${paperId}` ,
//  编辑试卷
editPaper:"/examinationPaper/edit",
// 删除试卷
deletePaper:"/examinationPaper/delete",


// 考试
// 考试列表
examList:'/examination/list',
// 试卷列表
paperExamList:'/examination/paper/list',
// 预览
preDetail:(id)=>`/web/examinationPaper/detail?paperId=${id}`,
// 用户组列表
roleList:"/examination/role/list",
//保存草稿
draft:"/examination/draft",
//添加考试
addexam:"/examination/add",
// 考试标签结果设置初始数据
initListByData:"/examination/result/initListByData",
// 用户列表
userList:(userName)=>`/examination/get/list?userName=${userName}`,
// 删除
deleteExam:`/examination/delete`,
// 考试详情
examDetail:(id)=>`/examination/detail?examId=${id}`,
// 编辑
examEdit:'/examination/edit',
// 查看考卷
examRecordList:'/examination/record/list',
// 导出
examExport:(id)=>`/examination/export?examId=${id}`,

// 课程一级分类
tagList:'/medsci-yxd-tag/list/tag',
// 根据id查分类
getObjectIdsByTagId:(id)=>`/medsciTagModuleSort/getObjectIdsByTagId?tagId=${id}`,
// 更新排序
batchUpdateSort:"/medsciTagModuleSort/batchUpdateSort",

// 任务列表
tasklist:`/course/task/list`,
// 任务-课程列表
courseList:'/course/task/courseList',
taskExamList:'/course/task/examList',
taskAdd:'/course/task/add',
// 课程任务详情
taskDetail:(id)=>`/course/task/detail?courseTaskId=${id}`, 
// 修改课程任务
edittask:'/course/task/edit' ,
setTask:'/course/task/set',
taskdelete:'/course/task/delete',
// 课程任务导出
taskExport:(id)=>`/course/task/export?courseTaskId=${id}`,

// 调研统计详情
statisticsDetail:'/medsciSurvey/statisticsDetail',
// 调研主观题分页列表
subjectiveList:'/medsciSurvey/subjectiveList',
// 省份
provinceList:`/medsciSurvey/provinceList`,
// 导出主观题
exportSubjectiveList:'/medsciSurvey/subjectiveList/export',
// 
yxdlog:'/medsci-yxd-log/list',
// 一级分类添加或更换ui模板
setTagUi:"medsci-yxd-tag/setTagUi",
// 获取项目来源展示开关状态
getSourceShow:()=>"config/getSourceShow",
// 设置是否展示列表及详情页来源展示(资讯模块)
setSourceShow:"/config/setSourceShow",
// ugc列表
ugc:"/ugc/page",
// ugc详情
ugcDetail:(id)=>`ugc/detail/${id}`,
// ugc更新
ugcUpdate:"/ugc/update",
// 审核
ugcBatchAudit:"ugc/batchAudit",
// 设置UGC阅读量随机范围
setRandomRange:(min, max) => `/ugc/setRandomRange/${min}/${max}`,
// UGC导出
ugcExport:"/ugc/export",
ossToken:(data)=>`/web/common/ossToken/${data.type}`,
// 添加imsl配置
addImsl:"/medsciImsl/add",
// imsl配置详情
imslDetail:(data)=>"/medsciImsl/detail",
// imsl 会话列表
chatLogPage:"/medsciImsl/chatLogPage",
// imsl会话详情
chatLogDetail:"/medsciImsl/chatLogDetail",
// 知识库上传文件
docsRecordPage:"/medsciImsl/docsRecordPage",
// 知识库审核文件
docsAudit:"/medsciImsl/docsAudit",
// 知识库删除
docsDelete:"/medsciImsl/docsDelete",
// 知识库上传
docsUpload:"/medsciImsl/docsUpload",
// 全集管理列表
medsciSpecialTopic:(data)=>`/medsciSpecialTopic/page?pageIndex=${data.pageIndex}&pageSize=${data.pageSize}&title=${data.title}`,
// 创建全集
addMedsciSpecialTopic:"/medsciSpecialTopic",
// 合集搜索标题
medsciSpecialTopicSearch:'/medsciSpecialTopic/moduleContent',
// 合集批量删除
batchDeal:'/medsciSpecialTopic/batchDelete',
// 合集审核
medsciSpecialTopicAudit:"/medsciSpecialTopic/audit",
// 合集去审
medsciSpecialTopicRemoveAudit:"/medsciSpecialTopic/removeAudit",

// 合集详情
medsciSpecialTopicDetail:(id)=>'/medsciSpecialTopic/detail?id='+id,
// 合集删除
medsciSpecialTopicDelete: `/medsciSpecialTopic/delete`,
// 编辑合集
medsciSpecialTopicUpload:"/medsciSpecialTopic/update",
// 个人中心列表
projectList:"/projectConfig/list",
// 个人中心列表
projectSave:"/projectConfig/save",

// 分销员行为
salesmanAccounts:(data)=>`/web/youzan/salesmanAccounts?pageNo=${data.pageNo}&pageSize=${data.pageSize}&nameOrMobile=${data.nameOrMobile}&startTime=${data.startTime}&endTime=${data.endTime}&sortField=${data.sortField}&isAsc=${data.isAsc}`,
// 设置项目一项分类展示首页开关状态
setHomeSettingStatus:"/config/setHomeSettingStatus",
// 回显项目一项分类展示首页开关状态
getHomeSettingStatus:() => "/config/getHomeSettingStatus",
// 回显项目一项分类展示首页开关状态
getToolBarStatus: ()=>"/config/getToolBarStatus",
setToolbarStatus: "/config/setToolbarStatus",
salesmanExport: (data) => `/web/youzan/salesmanAccounts/export?nameOrMobile=${data.nameOrMobile}&startTime=${data.startTime}&endTime=${data.endTime}&sortField=${data.sortField}&isAsc=${data.isAsc}`,
// 客户行为
customersAccounts:(data) => `/web/youzan/customers?pageNo=${data.pageNo}&pageSize=${data.pageSize}&nameOrMobile=${data.nameOrMobile}&startTime=${data.startTime}&endTime=${data.endTime}&sortField=${data.sortField}&isAsc=${data.isAsc}`,
// 导出客户行为
customersExport:(data) => `/web/youzan/customers/export?nameOrMobile=${data.nameOrMobile}&startTime=${data.startTime}&endTime=${data.endTime}&sortField=${data.sortField}&isAsc=${data.isAsc}`,
// 批量删除分类
tagBatch:`/medsci-yxd-tag/delete/tag/batch`,
//考试结果
recordView:`/examination/record/view`,
//商品列表
goodsCategoryList:`/goodsCategory/list`,
// 新增商品分类
addGoodsCategory:`/goodsCategory/add`,
// 分类详情
goodsCategoryget:(data) =>`/goodsCategory/get?id=${data.id}`,
// 禁用启用商品分类
goodsCategoryOperate:`/goodsCategory/operate`,
// 删除商品分类
goodsCategoryDelete:`/goodsCategory/delete`,
// 编辑商品分类
goodsCategoryUpdate:`/goodsCategory/update`,
// 编辑商品分类
goodsCategorySortUpdate:`/goodsCategory/sort/update`,
// 虚拟券码列表
voucherList:`/voucher/list`,
// 添加券码
voucherAdd:`/voucher/add`,
// 编辑券码
voucherEdit:`voucher/edit`,
// 券码详情
voucherDetail:(data) =>`voucher/get?id=${data.id}`,
// 券码启用禁用
voucherOperate:`voucher/operate`,
// 商品列表
goodsList:`/goods/list`,
// 兑换订单列表
exChangeLog:`/exchange/log/list`,
// 删除商品
goodsDelete:(data) =>`/goods/delete?id=${data.id}`,
// 批量设置分类
goodsCategoryBind:`/goodsCategory/bind`,
// 已设置商品分类绑定
goodsCategoryBindList:`/goodsCategory/bind/list`,
// 批量设置分类单条取消
goodsCategorySortCancerl:`/goodsCategory/sort/cancel`,
// 用户身份职称列表
professionalCategories:`/medsci-yxd-user/professionalCategories`,
// 员工分析明细分页列表   
analysisDetailList:`/medsci-yxd-user/analysisDetailList`,        
//导出员工分析明细分页列表   
analysisDetailExport:`/medsci-yxd-user/analysisDetailExport`,    
// 用户科室统计图
userDepartmentStatistic:`/medsci-yxd-user/userDepartmentStatistic`,   
// 直播流量分析
liveAnalyse:(data)=>`/moduleStatistics/liveAnalyse?liveId=${data.liveId}`,   
// 昨日分析
yesterdayData:(data)=>`/moduleStatistics/yesterdayData`,   
// 资讯分析
articleAnalyse:`/moduleStatistics/articleAnalyse`,   

}
