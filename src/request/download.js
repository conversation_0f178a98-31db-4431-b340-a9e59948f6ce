import axios from 'axios'
import api from './api'
import store from "../store";
const baseurl = `/apis/yxd` // 代理环境
const download = axios.create({
  baseURL: baseurl
})
download.defaults.timeout = 180000
download.defaults.baseURL = baseurl
// 请求是否带上cookie
download.defaults.withCredentials = true
download.defaults.headers.post['Content-Type'] = 'application/json'

// 请求拦截
download.interceptors.request.use(
  (config) => {
    const user = sessionStorage.getItem('yxd_backstage_info')
    const auto = localStorage.getItem('isChoseAuto')
    if(process.env.VUE_APP_TBA === 'dev'){
      // 开发环境
      // config.headers.domain = window.location.host
      // config.headers.domain = "acces.medon.com.cn"
      config.headers.domain = "ebwonc.medon.com.cn"
    }else{
      // 线上环境
      config.headers.domain =  window.location.host
    }
    
    let routeActive = window.location.href.split('#/')[1]
    let routeWhiteList = ['admin_permission']
    // let courseList = ['course']
    let routeId = 1
    if(store.state.menuInfo && store.state.menuInfo.length) {
      store.state.menuInfo.forEach((item) => {
        if(item.route == routeActive) {
          routeId = item.id
        }
        if (item.childMenu.length > 0) {
          item.childMenu.forEach((row) => {
            if (row.route == routeActive) {
              // 权限特殊处理
              if(routeActive.includes(routeWhiteList)) {
                routeId = row.parentId
              }else {
                routeId = row.id
              }
            }
            // /role/query权限接口统一获取admin_permission的parentId
            if(config.url == '/role/query' && row.route.includes(routeWhiteList)) {
              routeId = row.parentId
            }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_list') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/task/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_task') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url.includes('medsciTagModuleSort/getObjectIdsByTagId') && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_sort') {
            //       routeId = n.id
            //     }
            //   })
            // }
          });
        }
      });
    }

    config.headers.routeId = routeId
    
    if (auto) {
      const autoData = JSON.parse(auto)
      if (autoData) {
        const token = autoData
        config.headers.Authorization = token
        return config
      }
    }
    if (user) {
      const userData = JSON.parse(user)
      if (userData) {
        const token = userData.token.accessToken
        config.headers.Authorization = token
      }
    }

    return config
  },
  (err) => {
    return Promise.reject(err)
  }
)
// return intercept
download.interceptors.response.use(
  function (res) {
    console.log(res)

    if (res.status == '200') {
      return Promise.resolve(res.data)
    }
  },
  function (error) {
    return Promise.reject(error)
  }
)
export default download
// 导出直播观看用户信息
export function exportUserInfo(id) {
  return download.get(api.exportUserInfo(id), {
    responseType: 'blob'
  })
}

// 后台-导出
export function exportUserBehavior(articleId) {
  return download.get(api.exportUserBehavior(articleId),{
    responseType: 'blob'
  })
}

// 后台-全部导出
export function exportsAll(start,end) {
  return download.post(api.exportCaseList,{createdStartTime:start,createdEndTime:end},{
    responseType: 'blob'
  })
}
// 后台-咨询全部导出
export function exportArticleList(start,end,projectId) {
  return download.post(api.exportArticleList,{publishedStartTime:start,publishedEndTime:end,approvalStatus:1,projectId},{
    responseType: 'blob'
  })
}
// 后台-直播全部导出
export function exportLive(start,end) {
  return download.post(api.exportLive,{liveStartTime:start,liveEndTime:end},{
    responseType: 'blob'
  })
}
// 后台-课程全部导出
export function exportViewList(start,end,projectId,courseIds) {
  return download.post(api.exportViewList,{pushStartTime:start,pushEndTime:end,projectId,pageIndex:1,pageSize:10,courseIds:courseIds},{
    responseType: 'blob'
  })
}
// export function exportViewList(start,end,projectId) {
//   return download.post(api.exportViewList,{pushStartTime:start,pushEndTime:end,projectId,pageIndex:1,pageSize:10},{
//     responseType: 'blob'
//   })
// }
// 后台-医迅达全部导出
export function edaList(start,end,projectId) {
  return download.post(api.edaList,{createdStartTime:start,createdEndTime:end,projectId,pageIndex:1,pageSize:10},{
    responseType: 'blob'
  })
}
// 病例导出
export function exportUserCase(caseId) {
  return download.get(api.exportUserCase(caseId),{
    responseType: 'blob'
  })
}

// 医迅达导出
export function exportUserEda(edaId) {
  return download.get(api.exportUserEda(edaId),{
    responseType: 'blob'
  })
}
// 指南导出
export function exportUserGuider(Id) {
  return download.get(api.exportUserGuider(Id),{
    responseType: 'blob'
  })
}

// 导出
export function examExport(params){
  return download.get(api.examExport(params),{
    responseType: 'blob'
  })
}

// 导出
export function taskExport(params){
  return download.get(api.taskExport(params),{
    responseType: 'blob'
  })
}

export function exportSubjectiveList(params){
  return download.post(api.exportSubjectiveList,params,{
    responseType: 'blob'
  })
}
// 量表批量导出
export function exportScaleQuestions(params) {
  return download.post(api.exportScaleQuestions,params,{
    responseType: 'blob'
  })
}
// 导出员工分析明细分页列表
export function analysisDetailExport(params) {
  return download.post(api.analysisDetailExport,params,{
    responseType: 'blob'
  })
}
