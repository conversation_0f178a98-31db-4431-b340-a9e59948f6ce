import axios from 'axios'
import api from './api'
import qs from  'qs'
import store from "../store";
const baseurl = `/apis/yxd` // 代理环境
const download = axios.create({
  baseURL: baseurl
})
download.defaults.timeout = 180000
download.defaults.baseURL = baseurl
// 请求是否带上cookie
download.defaults.withCredentials = true
download.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'

// 请求拦截
download.interceptors.request.use(
  (config) => {
    const user = sessionStorage.getItem('yxd_backstage_info')
    const auto = localStorage.getItem('isChoseAuto')
    if(process.env.VUE_APP_TBA === 'dev'){
      // 开发环境
      // config.headers.domain = window.location.host
      // config.headers.domain = "acces.medon.com.cn"
      config.headers.domain = "ebwonc.medon.com.cn"
    }else{
      // 线上环境
      config.headers.domain =  window.location.host
    }

    let routeActive = window.location.href.split('#/')[1]
    let routeWhiteList = ['admin_permission']
    // let courseList = ['course']
    let routeId = 1
    if(store.state.menuInfo && store.state.menuInfo.length) {
      store.state.menuInfo.forEach((item) => {
        if(item.route == routeActive) {
          routeId = item.id
        }
        if (item.childMenu.length > 0) {
          item.childMenu.forEach((row) => {
            if (row.route == routeActive) {
              // 权限特殊处理
              if(routeActive.includes(routeWhiteList)) {
                routeId = row.parentId
              }else {
                routeId = row.id
              }
            }
            // /role/query权限接口统一获取admin_permission的parentId
            if(config.url == '/role/query' && row.route.includes(routeWhiteList)) {
              routeId = row.parentId
            }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_list') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/task/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_task') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url.includes('medsciTagModuleSort/getObjectIdsByTagId') && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_sort') {
            //       routeId = n.id
            //     }
            //   })
            // }
          });
        }
      });
    }

    config.headers.routeId = routeId
    
    if (auto) {
      const autoData = JSON.parse(auto)
      if (autoData) {
        const token = autoData
        config.headers.Authorization = token
        return config
      }
    }
    if (user) {
      const userData = JSON.parse(user)
      if (userData) {
        const token = userData.token.accessToken
        config.headers.Authorization = token
      }
    }
    return config
  },
  (err) => {
    return Promise.reject(err)
  }
)
// return intercept
download.interceptors.response.use(
  function (res) {
    if (res.status == '200') {
      return Promise.resolve(res.data)
    }
  },
  function (error) {
    return Promise.reject(error)
  }
)
export default download
// 期刊后台-用户管理-导出用户信息
export function exportImpactFactor(params) {
  return download.post(api.exportImpactFactor,
    qs.stringify({id:params}),
    {responseType: 'blob'}
    )
}
// 后台导出积分流水
export function integralExport(params) {
  return download.post(api.integralExport,
    params,
    {responseType: 'blob'}
    )
}

