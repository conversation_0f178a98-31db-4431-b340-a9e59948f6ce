import axios from 'axios'
import api from './api'
import store from "../store";
import { ElMessage, ElLoading } from 'element-plus'
const baseurl = `/apis/yxd` // 代理环境
const service = axios.create({
  baseURL: baseurl
})
service.defaults.timeout = 180000
service.defaults.baseURL = baseurl
// 请求是否带上cookie
service.defaults.withCredentials = true
service.defaults.headers.post['Content-Type'] = 'application/json'

let loading
function startLoading() {
  loading = ElLoading.service({
    lock: true,
    text: '加载中......',
    background: 'rgba(0, 0, 0, 0.1)'
  })
}
function endLoading() {
  loading.close()
}

// 请求拦截
service.interceptors.request.use(
  (config) => {
    startLoading()
    const user = sessionStorage.getItem('yxd_backstage_info')
    const auto = localStorage.getItem('isChoseAuto')
    // localStorage.setItem

    if (process.env.NODE_ENV === "development") {
      // 开发环境
      config.headers.domain = 'ebwonc.medon.com.cn'
      // config.headers.domain = 'acces.medon.com.cn'

    } else {
      // 线上环境
      config.headers.domain = window.location.host
    }
    let routeActive = window.location.href.split('#/')[1]
    let routeWhiteList = ['admin_permission']
    // let courseList = ['course']
    let routeId = 1
    if(store.state.menuInfo && store.state.menuInfo.length) {
      store.state.menuInfo.forEach((item) => {
        if(item.route == routeActive) {
          routeId = item.id
        }
        if (item.childMenu.length > 0) {
          item.childMenu.forEach((row) => {
            if (row.route == routeActive) {
              // 权限特殊处理
              if(routeActive.includes(routeWhiteList)) {
                routeId = row.parentId
              }else {
                routeId = row.id
              }
            }
            // /role/query权限接口统一获取admin_permission的parentId
            if(config.url == '/role/query' && row.route.includes(routeWhiteList)) {
              routeId = row.parentId
            }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_list') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/task/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_task') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url.includes('medsciTagModuleSort/getObjectIdsByTagId') && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_sort') {
            //       routeId = n.id
            //     }
            //   })
            // }
          });
        }
      });
    }

    config.headers.routeId = routeId

    if (auto) {
      const autoData = JSON.parse(auto)
      if (autoData) {
        const token = autoData
        config.headers.Authorization = token
        return config
      }
    }
    if (user) {
      const userData = JSON.parse(user)
      if (userData) {
        const token = userData.token.accessToken
        config.headers.Authorization = token
      }
    }
    return config
  },
  (err) => {
    setTimeout(() => {
      endLoading()
    }, 3000)
    return Promise.reject(err)
  }
)
// return intercept
service.interceptors.response.use(
  function (res) {
    endLoading()

    if (res.data?.status == '1001') {
      ElMessage.warning(res.data.message)
      sessionStorage.removeItem('yxd_backstage_info')
      window.localStorage.removeItem('isChoseAuto')
      setTimeout(() => {
        window.location.reload()
      }, 2000)
    } else if (res.data?.status && res.data.status != '200' && res.data.status != '905') {
      ElMessage.warning(res.data.message)
      return Promise.resolve(res.data)
    } else {
      return Promise.resolve(res.data)
    }
  },
  function (error) {
    setTimeout(() => {
      endLoading()
    }, 1000)
    return Promise.reject(error)
  }
)
export default service
// 根据域名获取项目信息
export function getYxdProjectDetailByOrigin(params) {
  return service.post(api.getYxdProjectDetailByOrigin, params)
}
// 获取项目信息
export function getYxdProjectDetail(params) {
  return service.post(api.getYxdProjectDetail, params)
}
// 登录
export function login(params) {
  return service.post(api.login, params)
}
// 自动登录
export function autoLogin(params) {
  return service.post(api.autoLogin, params)
}
// 发送短信
export function sendSms(params) {
  return service.post(api.sendSms, params)
}
// 退出登录
export function logout(params) {
  return service.post(api.logout, params)
}
// 添加菜单
export function saveSysMenu(params) {
  return service.post(api.saveSysMenu, params)
}
// 树形菜单
export function sysMenuTree(params) {
  return service.post(api.sysMenuTree, params)
}
// 树形菜单
export function sysAdminMenuTree(params) {
  return service.post(api.sysAdminMenuTree, params)
}

// 绑定菜单
export function bundleMenu(params) {
  return service.post(api.bundleMenu, params)
}
// 绑定菜单
export function bundleAdminMenu(params) {
  return service.post(api.bundleAdminMenu, params)
}

// 绑定用户
export function bundleUser(params) {
  return service.post(api.bundleUser, params)
}
// 绑定用户
export function bundleAdminUser(params) {
  return service.post(api.bundleAdminUser, params)
}
// 加入后台权限
export function joinAdmin(params) {
  return service.post(api.joinAdmin, params)
}
// 角色菜单列表
export function menu(params) {
  return service.post(api.menu, params)
}
// 角色菜单列表
export function adminMenu(params) {
  return service.post(api.adminMenu, params)
}

// 角色查询
export function query(params) {
  return service.post(api.query, params)
}
// 角色查询
export function adminQuery(params) {
  return service.post(api.adminQuery, params)
}
// 移除后台权限
export function removeAdmin(params) {
  return service.post(api.removeAdmin, params)
}
// 移除系统角色
export function removeSysRole(params) {
  return service.post(api.removeSysRole, params)
}
// 移除系统角色
export function removeAdminSysRole(params) {
  return service.post(api.removeAdminSysRole, params)
}

// 移除用户
export function removeUser(params) {
  return service.post(api.removeUser, params)
}
// 移除用户
export function removeAdminUser(params) {
  return service.post(api.removeAdminUser, params)
}
// 添加系统角色
export function saveSysRole(params) {
  return service.post(api.saveSysRole, params)
}
// 添加系统角色
export function saveAdminSysRole(params) {
  return service.post(api.saveAdminSysRole, params)
}

// 修改系统角色
export function updateSysRole(params) {
  return service.post(api.updateSysRole, params)
}
// 修改系统角色
export function updateAdminSysRole(params) {
  return service.post(api.updateAdminSysRole, params)
}
// 角色用户列表
export function users(params) {
  return service.post(api.users, params)
}
// 角色用户列表
export function adminUsers(params) {
  return service.post(api.adminUsers, params)
}



// 用户添加
export function addUser(params) {
  return service.post(api.addUser, params)
}
// 用户添加
export function addAdminUser(params) {
  return service.post(api.addAdminUser, params)
}

// 批量导入用户
export function importUser(params) {
  return service.post(api.importUser, params)
}
// 批量导入患者
export function importPatientUser(params) {
  return service.post(api.importPatientUser, params)
}
// 批量导入用户
export function importAdminUser(params) {
  return service.post(api.importAdminUser, params)
}
// 根据userId删除用户
export function deleteUser(userId, accountType) {
  return service.post(api.deleteUser(userId, accountType))
}
// 根据userId删除用户
export function deleteAdminUser(userId) {
  return service.post(api.deleteAdminUser(userId))
}

// 用户导入模板下载
export function downLoad(params) {
  return service.post(api.downLoad, params)
}
// 患者导入模板下载
export function downloadPatientTemplate(params) {
  return service.post(api.downloadPatientTemplate, params)
}
// 用户列表-根据用户姓名和手机号获取
export function getUserList(params) {
  return service.post(api.getUserList, params)
}
// 用户列表-根据用户姓名和手机号获取
export function getUserAdminList(params) {
  return service.post(api.getUserAdminList, params)
}
// 用户详情-根据userId和手机号获取
export function getDetail(params) {
  return service.post(api.getDetail, params)
}
// 用户列表-认证审核
export function approveUser(params) {
  return service.post(api.approveUser, params)
}
// 审核完善信息
export function approvePerfectInfo(params) {
  return service.post(api.approvePerfectInfo, params)
}
// 用户信息是否完善-true已完善 false-未完善
export function isUserInfoPerfect(userId) {
  return service.get(api.isUserInfoPerfect(userId))
}

// 完善用户信息
export function updateUser(params) {
  return service.post(api.updateUser, params)
}
// 完善用户信息
export function updateAdminUser(params) {
  return service.post(api.updateAdminUser, params)
}

// base64头像上传
export function uploadAvatar(params) {
  return service.post(api.uploadAvatar, params)
}
// base64头像上传
export function uploadAdminAvatar(params) {
  return service.post(api.uploadAdminAvatar, params)
}
// 主题列表
export function themeColorList() {
  return service.get(api.themeColorList)
}
// 开启主题状态
export function openStatus(params) {
  return service.post(api.openStatus, params)
}
// 修改主体颜色
export function saveCustomColor(params) {
  return service.post(api.saveCustomColor, params)
}
// 新增首页icon
export function addTool(params) {
  return service.post(api.addTool, params)
}
// 首页icon批量处理-审核/去审/删除
export function batchDealTools(params) {
  return service.post(api.batchDealTools, params)
}
// 首页icon详情
export function detailTool(params) {
  return service.post(api.detailTool, params)
}
// 首页icon列表
export function getToolPage(params) {
  return service.post(api.getToolPage, params)
}
// 首页icon修改
export function updateTool(params) {
  return service.post(api.updateTool, params)
}
// 广告管理批量处理审核去审
export function dealAdvertisementByBatchId(params) {
  return service.post(api.dealAdvertisementByBatchId, params)
}

// 广告分页
export function getAdvertisementPage(params) {
  return service.post(api.getAdvertisementPage, params)
}
// 根据广告位名模糊匹配广告位列表
export function getAdvertisementSpaceListByName(params) {
  return service.post(api.getAdvertisementSpaceListByName, params)
}
// 添加广告信息
export function saveAdvertisement(params) {
  return service.post(api.saveAdvertisement, params)
}
// 根据ID修改广告信息
export function updateAdvertisementById(params) {
  return service.post(api.updateAdvertisementById, params)
}
// 评论批量处理
export function batchDealComment(params) {
  return service.post(api.batchDealComment, params)
}
// 评论列表
export function getCommentsPage(params) {
  return service.post(api.getCommentsPage, params)
}
// 后台-表单删除
export function formBatchDelete(params) {
  return service.post(api.formBatchDelete, params)
}
// 后台-资讯新增
export function addAriticle(params) {
  return service.post(api.addAriticle, params)
}
// 后台-资讯批量操作-删除,上线下,设置分类
export function batchEdit(params) {
  return service.post(api.batchEdit, params)
}
// 后台-期刊删除
export function impactFactorDelete(params) {
  return service.post(api.impactFactorDelete, params)
}
// 后台-指南批量删除
export function guiderBatchDelete(params) {
  return service.post(api.guiderBatchDelete, params)
}
// 后台-批量操作-删除,上下线
export function batchLineModule(params) {
  return service.post(api.batchLineModule, params)
}
// 后台表单-批量操作-删除,上下线
export function formBatchAudit(params) {
  return service.post(api.formBatchAudit, params)
}
// 后台-咨询批量设置删除权限下拉列表
export function getRoleList() {
  return service.get(api.getRoleList)
}
// 后台-用户组单条树形菜单
export function getRoleQuery(params) {
  return service.post(api.getRoleQuery,params)
}
// 后台-批量删除权限回显表单
export function getModuleRoles(params) {
  return service.post(api.getModuleRoles,params)
}
// 后台-批量删除分类回显表单
export function getModuleTags(params) {
  return service.post(api.getModuleTags,params)
}
// 后台-单条删除权限按钮
export function delCancelRole(params) {
  return service.post(api.delCancelRole,params)
}
// 后台-单条删除分类按钮
export function delCancelTag(params) {
  return service.post(api.delCancelTag,params)
}
// 后台-资讯批量设置权限
export function setDelePower(params) {
  return service.post(api.setDelePower, params)
}
// // 后台-资讯批量设置分类
export function setCancelTag(params) {
  return service.post(api.setCancelTag, params)
}
// 后台-资讯详情
export function getAriticleById(params) {
  return service.post(api.getAriticleById, params)
}
// 后台-资讯分页
export function getAriticlePage(params) {
  return service.post(api.getAriticlePage, params)
}
// 后台-期刊分页
export function getImpactFactorPage(params) {
  return service.post(api.getImpactFactorPage, params)
}
// 后台-指南分页
export function getGuiderPage(params) {
  return service.post(api.getGuiderPage, params)
}
// 后台-咨询修改
export function updateAriticle(params) {
  return service.post(api.updateAriticle, params)
}
// 获取调研的UV详情分页
export function surveyGetUvRecord(params) {
  return service.post(api.surveyGetUvRecord, params)
}
// 获取调研的浏览记录分页
export function surveyGetRecord(params) {
  return service.post(api.surveyGetRecord, params)
}
//后台-表单详情
export function formDetail(formId,projectId) {
  return service.get(api.formDetail(formId,projectId))
}
//后台-表单分页查询
export function formMaterial(params) {
  return service.post(api.formMaterial, params)
}
// 医迅达课程删除(批量)
export function deleteCourse(params) {
  return service.post(api.deleteCourse, params)
}
// 医迅达课程列表
export function listCourse(params) {
  return service.post(api.listCourse, params)
}
// 医迅达课程下线(批量)
export function offlineCourse(params) {
  return service.post(api.offlineCourse, params)
}
// 医迅达课程上线(批量)
export function onlineCourse(params) {
  return service.post(api.onlineCourse, params)
}
// 后台-课程批量设置删除权限
export function setcoursePower(params) {
  return service.post(api.setcoursePower, params)
}
// 后台设置排序
export function setSort(params) {
  return service.post(api.setSort, params)
}
// 查询直播数据
export function liveQuery(params) {
  return service.post(api.liveQuery, params)
}
// 批量删除留言消息
export function batchDelete(params) {
  return service.post(api.batchDelete, params)
}
// 获取留言消息列表
export function leaveQuery(params) {
  return service.post(api.leaveQuery, params)
}
// 获取留言消息列表
export function replyMessage(params) {
  return service.post(api.replyMessage, params)
}
// 公告单页-处理上下线删除操作
export function dealSinglePage(params) {
  return service.post(api.dealSinglePage, params)
}
// 公告单页-分页查询
export function getPageForSinglePage(params) {
  return service.post(api.getPageForSinglePage, params)
}
// 公告单页-详情查询
export function getSinglePageById(params) {
  return service.post(api.getSinglePageById, params)
}
// 项目须知
export function getCompanyByProjectId(id) {
  return service.get(api.getCompanyByProjectId(id))
}
// 公告单页-添加
export function insertSinglePage(params) {
  return service.post(api.insertSinglePage, params)
}
// 公告单页-修改
export function updateSinglePage(params) {
  return service.post(api.updateSinglePage, params)
}
// 添加tag
export function addTag(params) {
  return service.post(api.addTag, params)
}

// 删除直播
export function deleteBeOfflineLive(params) {
  return service.post(api.deleteBeOfflineLive, params)
}
// 删除tag
export function deleteTag(params) {
  return service.post(api.deleteTag, params)
}
// 删除指南tag
export function deleteGuiderTag(params) {
  return service.post(api.deleteGuiderTag, params)
}
// 根据id获取tag
export function getTag(params) {
  return service.post(api.getTag, params)
}
// 根据tagName和tagType获取列表
export function listTag(params) {
  return service.post(api.listTag, params)
}
// 更新tag
export function updateTag(params) {
  return service.post(api.updateTag, params)
}
// 更新分类排序
export function updateSort(params) {
  return service.post(api.updateSort(params))
}
// 获取课程tag分类
export function getCourseTag(params) {
  return service.post(api.getCourseTag, params)
}
// 更新课程tag分类
export function updateCourseTag(params) {
  return service.post(api.updateCourseTag, params)
}
// 批量删除课程tag分类
export function deleteCourseTag(params) {
  return service.post(api.deleteCourseTag, params)
}
// 设置是否展示列表及详情页浏览量展示
export function setViewCountShow(params) {
  return service.post(api.setViewCountShow, params)
}
// 设置项目是否需要定向展示
export function setDirectional(params) {
  return service.post(api.setDirectional, params)
}
// 设置相关推荐
export function setRecommendStatus(params) {
  return service.post(api.setRecommendStatus, params)
}
// 设置是否隐藏积分商城
export function setIntegralMallStatus(params) {
  return service.post(api.setIntegralMallStatus, params)
}
// 获取项目权限开关状态
export function Directional() {
  return service.get(api.Directional)
}
// 获取相关推荐开关状态
export function RecommendStatus() {
  return service.get(api.RecommendStatus)
}
// 获取是否隐藏积分商城
export function getIntegralMallStatus() {
  return service.get(api.getIntegralMallStatus)
}
// 获取项目浏览量开关状态
export function ViewCountShow() {
  return service.get(api.ViewCountShow)
}
// 获取积分发放数量开关状态
export function IntegralStatus() {
  return service.get(api.IntegralStatus)
}
// 获取积分商城开关状态
export function IntegralMenuStatus() {
  return service.get(api.IntegralMenuStatus)
}
// 获取微信公众号id
export function wxConfigGetWx() {
  return service.post(api.wxConfigGetWx)
}
// 设置分用户设置查看权限显示开关
export function directionalMenuStatus(id) {
  return service.get(api.directionalMenuStatus(id))
}
// 设置项目积分发放数量限制
export function setIntegral(params) {
  return service.post(api.setIntegral, params)
}
// 设置项目公众号
export function wxConfigAddWx(params) {
  return service.post(api.wxConfigAddWx, params)
}
// 删除项目公众号
export function wxConfigDelWx(params) {
  return service.post(api.wxConfigDelWx, params)
}
// 设置积分商城开关
export function setIntegralMenuStatus(params) {
  return service.post(api.setIntegralMenuStatus, params)
}
// 根据tagType获取列表-没有分页
export function tagTypeList(params) {
  return service.post(api.tagTypeList, params)
}
// 完善信息配置
export function perfectInfo(accountType) {
  return service.get(api.perfectInfo(accountType))
}
// 更新信息配置
export function updatePerfectInfo(params) {
  return service.post(api.updatePerfectInfo, params)
}
// 用户认证配置
export function authentication() {
  return service.get(api.authentication)
}
// 获取强制认证后访问配置
export function getAccessAfterMandatoryCertification() {
  return service.get(api.accessAfterMandatoryCertification)
}
// 获取审核完善信息配置
export function getPerfectInfoApproval() {
  return service.get(api.perfectInfoApproval)
}
// 获取在线的广告
export function getCountByYxd(id, projectId) {
  return service.get(api.getCountByYxd(id, projectId))
}
// 统计后台首页账号整体情况
export function countHomeAccount(params) {
  return service.post(api.countHomeAccount, params)
}
// 统计后台首页资讯整体情况
export function countHomeArticle(params) {
  return service.post(api.countHomeArticle, params)
}
// 统计后台首页课程整体情况
export function countHomeCourse(params) {
  return service.post(api.countHomeCourse, params)
}
// 统计后台首页直播整体情况
export function countHomeLive(params) {
  return service.post(api.countHomeLive, params)
}
// 统计后台首页访问统计
export function getStatistics(params) {
  return service.post(api.getStatistics, params)
}
// 统计后台首页登录统计
export function getUserUvByDateType(params) {
  return service.post(api.getUserUvByDateType, params)
}
// 统计后台首页登录统计
export function userRegisterData(params) {
  return service.post(api.userRegisterData, params)
}
// 查询所有会议列表
export function getMeetingList(params) {
  return service.post(api.getMeetingList, params)
}
// 获取附件
export function getMeetingAttachments(params) {
  return service.post(api.getMeetingAttachments, params)
}
// 核销会议
export function verifyMeeting(params) {
  return service.post(api.verifyMeeting, params)
}
// 设置课程等级
export function setCourseLevel(params) {
  return service.post(api.setCourseLevel, params)
}
// 医调研模块 列表分页
export function getMedsciSurveyPage(params) {
  return service.post(api.getMedsciSurveyPage, params)
}
// 医调研模块 上线下线
export function surveyOnOffLine(params) {
  return service.post(api.surveyOnOffLine, params)
}
// 设置项目背景
export function uploadBackground(params) {
  return service.post(api.background, params)
}
// 回显项目背景
export function getBackground(params) {
  return service.post(api.getBackground, params)
}
// 二合一开关
export function loginType(params) {
  return service.post(api.loginType, params)
}
// 二合一开关回显
export function getLoginType(params) {
  return service.post(api.getLoginType, params)
}
// 新增自定义字段配置
export function addCustomFiled(params) {
  return service.post(api.addCustomFiled, params)
}
// 新增自定义字段配置
export function deleteCustomFiled(params) {
  return service.post(api.deleteCustomFiled, params)
}
// 读取自定义字段配置列表
export function customFiledList(params) {
  return service.post(api.customFiledList, params)
}
// 自定义字段下拉框
export function getSelectedUser(id) {
  return service.get(api.getSelectedUser(id))
}
// 自定义字段表格回显
export function getDefinedFieldList(id, accountType) {
  return service.get(api.getDefinedFieldList(id, accountType))
}

// 获取项目token过期时间的配置
export function getTokenExpire(id) {
  return service.get(api.getTokenExpire(id))
}
// 项目token过期时间配置
export function setTokenExpire(params) {
  return service.post(api.setTokenExpire, params)
}
// 医调研表单导出
export function excelExport(params) {
  return service.post(api.exportForm, params)
}
// 获取病例征集医调研
export function caseList() {
  return service.get(api.getCaseList)
}
// 病例征集获取表单提交记录
export function getSubmitCaseInfo(params) {
  return service.post(api.getSubmitCaseInfo, params)
}
// 根据表单分页获取病例提交记录
export function pageListCase(params) {
  return service.post(api.pageListCase, params)
}
// 修改病例征集状态
export function updateCaseById(params) {
  return service.post(api.updateCaseById, params)
}
// 帖子管理查询列表
export function getPostsPage(params) {
  return service.post(api.getPostsPage, params)
}
// 帖子管理批量审核,去审,删除接口
export function banchDealPosts(params) {
  return service.post(api.banchDealPosts, params)
}
// 帖子管理推荐,固顶接口
export function recommendOrStickyPosts(params) {
  return service.post(api.recommendOrStickyPosts, params)
}
// 病例管理列表分页
export function getMedsciCaseAdminPage(params) {
  return service.post(api.getMedsciCaseAdminPage, params)
}
// 病例管理批量上下线
export function caseOnOffLine(params) {
  return service.post(api.caseOnOffLine, params)
}
// 后台eda分页
export function edaGetAriticlePage(params) {
  return service.post(api.edaGetAriticlePage, params)
}
// 医迅达删除医讯达tag分类
export function edaDeleteTag(params) {
  return service.post(api.edaDeleteTag, params)
}
// 后台-医讯达批量操作-删除,上线下线,设置tag后台eda分页
export function edaBatchEdit(params) {
  return service.post(api.edaBatchEdit, params)
}
// 根据字段名获取用户自定义列表
export function listUserDefinedFieldByFieldName(params) {
  return service.post(api.listUserDefinedFieldByFieldName, params)
}
// 根据id更新用户定义字段
export function updateUserDefinedFieldById(params) {
  return service.post(api.updateUserDefinedFieldById, params)
}
// 根据id获取用户自定义详情
// export function getUserDefinedFieldById(params) {
//   return service.post(api.getUserDefinedFieldById, params)
// }
export function getDefinedField(params) {
  return service.post(api.getDefinedField, params)
}
export function getAdminDefinedField(params) {
  return service.post(api.getAdminDefinedField, params)
}
// 根据id删除用户定义字段
export function deleteUserDefinedFieldById(params) {
  return service.post(api.deleteUserDefinedFieldById, params)
}
// 添加用户定义字段
export function addUserDefinedField(params) {
  return service.post(api.addUserDefinedField, params)
}
// 添加用户定义字段
export function userSort(params) {
  return service.post(api.userSort, params)
}

// 修改/删除用户定义字段
export function delUserDefined(params) {
  return service.post(api.delUserDefined, params)
}
// 添加用户定义字段(身份信息)
export function addUserDefined(params) {
  return service.post(api.addUserDefined, params)
}
// 发送通知
export function courseSendNotice(params) {
  return service.post(api.courseSendNotice, params)
}
// 导出学习详情
export function exportViewLog(id) {
  return service.get(api.exportViewLog(id), {
    responseType: 'blob'
  })
}
// 导出学习详情
export function impactFactorExportSearch(params) {
  return service.post(api.impactFactorExportSearch, 
    params,
    {responseType: 'blob'}
  )
}
// 医迅达后台-用户管理-导出用户信息
export function yxdExportUserInfo(params) {
  return service.post(api.yxdExportUserInfo, params)
}
export function exportPatientUserInfo(params) {
  return service.post(api.exportPatientUserInfo, params)
}
// 医迅达后台-用户管理-导出用户信息
export function yxdExportAdminUserInfo(params) {
  return service.post(api.yxdExportAdminUserInfo, params)
}
// 医迅达后台-添加用户回显
export function editDefinedField(params) {
  return service.post(api.editDefinedField, params)
}
// 医迅达后台-添加用户回显
export function editAdminDefinedField(params) {
  return service.post(api.editAdminDefinedField, params)
}
// 删除调研
export function deleteBeOfflineSurvey(params) {
  return service.post(api.deleteBeOfflineSurvey, params)
}
// 获取调研详情
export function getCustomFormByIdApo(params) {
  return service.post(api.getCustomFormByIdApo, params)
}
// apo后台创建调研
export function saveCustomFormByApo(params) {
  return service.post(api.saveCustomFormByApo, params)
}
// apo后台修改调研
export function updateCustomFormByApo(params) {
  return service.post(api.updateCustomFormByApo, params)
}
// 获取表单信息
export function getFormPage(params) {
  return service.post(api.getFormPage, params)
}
// 生成规则校验
export function makeRuleCheck(params) {
  return service.post(api.makeRuleCheck, params)
}
// 获取省份
export function getMedsciAddress(params) {
  return service.post(api.getMedsciAddress, params)
}
// 获取城市
export function getMedsciTreeAddress(params) {
  return service.post(api.getMedsciTreeAddress, params)
}
// 创建表单
export function saveForm(params) {
  return service.post(api.saveForm, params)
}
// 编辑表单
export function updateForm(params) {
  return service.post(api.updateForm, params)
}
// 后台-任务新增
export function addTask(params) {
  return service.post(api.addTask, params)
}
// 任务列表
export function IntegralTask(params) {
  return service.post(api.IntegralTask, params)
}
// 根据id获取任务
export function getIntegralTask(params) {
  return service.post(api.getIntegralTask, params)
}
// 更新任务
export function updateIntegralTask(params) {
  return service.post(api.updateIntegralTask, params)
}
// 启用/禁用任务
export function offIntegralTask(params) {
  return service.post(api.offIntegralTask, params)
}
// 获取积分流水列表
export function integralList(params) {
  return service.post(api.integralList, params)
}
// 后台-任务删除,上线下,设置分类
export function deleteTask(params) {
  return service.post(api.deleteTask, params)
}
// 人工发放积分
export function artificialSend(params) {
  return service.post(api.artificialSend, params)
}
// 题库列表
export function QuestionBank(params) {
  return service.post(api.QuestionBank, params)
}
// 添加题库
export function addQuestionBank(params) {
  return service.post(api.addQuestionBank, params)
}
// 删除题库
export function deleteQuestionBank(params) {
  return service.post(api.deleteQuestionBank, params)
}
// 编辑题库名称
export function editQuestionBank(params) {
  return service.post(api.editQuestionBank, params)
}
// 题目列表
export function Question(params) {
  return service.post(api.Question, params)
}
// 添加题目
export function addQuestion(params) {
  return service.post(api.addQuestion, params)
}
// 编辑题目
export function editQuestion(params) {
  return service.post(api.editQuestion, params)
}
// 删除题目
export function deleteQuestion(params) {
  return service.post(api.deleteQuestion, params)
}
//获取题目详情
export function detailQuestion(Id) {
  return service.get(api.detailQuestion(Id))
}
//获取题目详情
export function medsciLabelList(params) {
  return service.post(api.medsciLabelList,params)
}

// 获取题库详情
export function detailBankQuestion(Id) {
  return service.get(api.detailBankQuestion(Id))
}

//文本-批量导入题目
export function batchAddQuestionsByText(params) {
  return service.post(api.batchAddQuestionsByText,params)
}
//批量导出题目
export function exportQuestions(params) {
  return service.post(api.exportQuestions, params)
}
//文本-批量导入题目
export function batchAddQuestionsByExcel(params) {
  return service.post(api.batchAddQuestionsByExcel,params)
}
// 量表导入
export function importScaleQuestionsByExcel(params) {
  return service.post(api.importScaleQuestionsByExcel,params)
}
// 复制考试
export function copy(params) {
  return service.get(api.copy(params))
}


// 题库-批量删除
export function questionBatchDelete(params){
  return service.post(api.batchDelete,params)
}
// 试卷-列表
export function paperList(params){
  return service.post(api.paperList,params)
}
// 题库列表
export function questionBankList(mode){
  return service.get(api.questionBankList(mode))
}

// 题库列表
export function questionList(bankId,mode){
  return service.get(api.questionList(bankId,mode))
}

// 添加试卷

export function addPaper(params){
  return service.post(api.addPaper,params)
}

// 试卷详情
export function paperDetail(paperId){
  return service.get(api.paperDetail(paperId))
}
// 编辑
export function editPaper(params){
  return service.post(api.editPaper,params)
}

// 删除试卷
export function deletePaper(params) {
  return service.post(api.deletePaper, params)
}

// 考试列表
export function examList(params){
  return service.post(api.examList,params)
}

// 考试结果分析列表
export function settingInitList(paperId){
  return service.get(api.settingInitList(paperId))
}
// 考试标签结果设置初始数据
export function initListByData(params){
  return service.post(api.initListByData,params)
}

//试卷预览
export function preDetail(params){
  return service.get(api.preDetail,params)
}
// 考试试卷列表

export function paperExamList(){
  return service.get(api.paperExamList)
}
// 保存草稿

export function draft(params){
  return service.post(api.draft,params)
}

// 用户组列表
export function roleList(){
  return service.get(api.roleList)
}
// 用户列表
export function userList(params){
  return service.get(api.userList(params))
}
// 添加考试
export function addexam(params){
  return service.post(api.addexam,params)
}
// 删除考试

export function deleteExam(params){
  return service.post(api.deleteExam,params)
}
// 考试详情
export function examDetail(params){
  return service.get(api.examDetail(params))
}
// 编辑考试
export function examEdit(params){
  return service.post(api.examEdit,params)
}

// 查看考卷

export function examRecordList(params){
  return service.post(api.examRecordList,params)
}

// 课程一级分类

export function tagList(params){
  return service.post(api.tagList,params)
}

// 根据id查分类
export function getObjectIdsByTagId(params){
  return service.get(api.getObjectIdsByTagId(params))
}

//更新课程分类

export function batchUpdateSort(params){
  return service.post(api.batchUpdateSort,params)
}

// 查询课程任务列表

export function tasklist(params){
  return service.post(api.tasklist,params)
}

export function courseList(params){
  return service.post(api.courseList,params)
}
// 添加课程任务
export function taskAdd(params){
  return service.post(api.taskAdd,params)
}
// 课程-考试
export function taskExamList(params){
  return service.post(api.taskExamList,params)
}
// 任务详情
export function taskDetail(params){
  return service.get(api.taskDetail(params))
}
// 编辑任务

export function edittask(params){
  return service.post(api.edittask,params)
}
// 启用禁用任务
export function setTask(params){
  return service.post(api.setTask,params)
}
// 启用禁用任务
export function taskdelete(params){
  return service.post(api.taskdelete,params)
}
// 调研统计详情
export function statisticsDetail(params){
  return service.post(api.statisticsDetail,params)
}
// 调研主观题分页列表
export function subjectiveList(params){
  return service.post(api.subjectiveList,params)
}
// 省份
export function provinceList(params){
  return service.post(api.provinceList,params)
}
// 日志
export function yxdlog(params){
  return service.post(api.yxdlog,params)
}
export function setTagUi(params){
  return service.post(api.setTagUi,params)
}
// 获取项目来源展示开关状态
export function getSourceShow(){
  return service.get(api.getSourceShow())
}
// 设置是否展示列表及详情页来源展示(资讯模块)

export function setSourceShow(params){
  return service.post(api.setSourceShow,params)
}

// ugc列表
export function ugc(params){
  return service.post(api.ugc,params)
}
// ugc详情
export function ugcDetail(params){
  return service.get(api.ugcDetail(params))
}
// 批量审核ugc
export function ugcBatchAudit(params) {
  return service.post(api.ugcBatchAudit, params)
}

// 更新ugc内容
export function updateUgc(params) {
  return service.post(api.ugcUpdate, params)
}

// 设置UGC阅读量随机范围
export function setRandomRange(min, max) {
  return service.post(api.setRandomRange(min, max))
}

// UGC导出
export function ugcExport(params) {
  return service.post(api.ugcExport, params, {
    responseType: 'blob'
  })
}

// 获取项目来源展示开关状态
export function ossToken(params){
  return service.get(api.ossToken(params))
}

// 添加imsl配置
export function addImsl(params){
  return service.post(api.addImsl,params)
}
// 添加imsl配置
export function imslDetail(params){
  return service.get(api.imslDetail(params))
}
// 查询会话列表
export function chatLogPage(params){
  return service.post(api.chatLogPage,params)
}
// 会话详情
export function chatLogDetail(params){
  return service.post(api.chatLogDetail,params)
}
// 知识库列表
export function docsRecordPage(params){
  return service.post(api.docsRecordPage,params)
}
// 知识库审核文件
export function docsAudit(params){
  return service.post(api.docsAudit,params)
}
// 知识库审核文件
export function docsDelete(params){
  return service.post(api.docsDelete,params)
}
// 知识库上传
export function docsUpload(params){
  return service.post(api.docsUpload,params)
}
// 合集列表查询
export function medsciSpecialTopic(params){
  return service.get(api.medsciSpecialTopic(params))
}
// 添加合集
export function addMedsciSpecialTopic(params){
  return service.post(api.addMedsciSpecialTopic,params)
}
// 搜索标题
export function medsciSpecialTopicSearch(params){
  return service.post(api.medsciSpecialTopicSearch,params)
}
// 批量删除

export function batchDeal(params){
  return service.post(api.batchDeal,params)
}
//合计详情
export function medsciSpecialTopicDetail(params){
  return service.get(api.medsciSpecialTopicDetail(params))
}
// 合集审核
export function medsciSpecialTopicAudit(params){
  return service.post(api.medsciSpecialTopicAudit,params)
}
// 合集审核
export function medsciSpecialTopicRemoveAudit(params){
  return service.post(api.medsciSpecialTopicRemoveAudit,params)
}

// 合集删除
export function medsciSpecialTopicDelete(params){
  return service.post(api.medsciSpecialTopicDelete,params)
}

// 合集编辑
export function medsciSpecialTopicUpload(params){
  return service.post(api.medsciSpecialTopicUpload,params)
}
// 个人中心列表
export function projectList(params){
  return service.post(api.projectList,params)
}
// 个人中心提交
export function projectSave(params){
  return service.post(api.projectSave,params)
}

// 分销员行为列表
export function salesmanAccounts(params) {
  return service.get(api.salesmanAccounts(params))
}
// 分销员导出
export function salesmanExport(params) {
  return service.get(api.salesmanExport(params))
}
// 分销员导出
export function setHomeSettingStatus(params) {
  return service.post(api.setHomeSettingStatus,params)
}
// 分销员导出
export function getHomeSettingStatus() {
  return service.get(api.getHomeSettingStatus())
}



// 客户
export function customersAccounts(params) {
  return service.get(api.customersAccounts(params))
}
// 导出客户行为
export function customersExport(params) {
  return service.get(api.customersExport(params))
}

// 批量删除分类
export function tagBatch(params) {
  return service.post(api.tagBatch,params)
}
// 导航栏状态回显
export function getToolBarStatus(params) {
  return service.get(api.getToolBarStatus(params))
}
// 设置导航栏状态
export function setToolbarStatus(params) {
  return service.post(api.setToolbarStatus,params)
}
// 设置导航栏状态
export function recordView(params) {
  return service.post(api.recordView,params)
}
// 商品分类列表
export function goodsCategoryList(params) {
  return service.post(api.goodsCategoryList,params)
}
//新增商品分类
export function addGoodsCategory(params) {
  return service.post(api.addGoodsCategory,params)
}
// 编辑商品分类
export function goodsCategoryget(params) {
  return service.get(api.goodsCategoryget(params))
}
// 启用禁用商品分类
export function goodsCategoryOperate(params) {
  return service.post(api.goodsCategoryOperate,params)
}
// 删除商品分类
export function goodsCategoryDelete(params) {
  return service.post(api.goodsCategoryDelete,params)
}
// 编辑商品分类
export function goodsCategoryUpdate(params) {
  return service.post(api.goodsCategoryUpdate,params)
}
// 商品分类排序
export function goodsCategorySortUpdate(params) {
  return service.post(api.goodsCategorySortUpdate,params)
}
// 虚拟券码列表
export function voucherList(params) {
  return service.post(api.voucherList,params)
}
// 虚拟券码新增
export function voucherAdd(params) {
  return service.post(api.voucherAdd,params)
}
// 虚拟券码编辑
export function voucherEdit(params) {
  return service.post(api.voucherEdit,params)
}
// 券码详情
export function voucherDetail(params) {
  return service.get(api.voucherDetail(params))
}
// 券码启用禁用
export function voucherOperate(params) {
  return service.post(api.voucherOperate,params)
}
// 商品列表
export function goodsList(params) {
  return service.post(api.goodsList,params)
}
// 兑换订单
export function exChangeLog(params) {
  return service.post(api.exChangeLog,params)
}
// 删除商品
export function goodsDelete(params) {
  return service.get(api.goodsDelete(params))
}
// 批量设置分类
export function goodsCategoryBind(params) {
  return service.post(api.goodsCategoryBind,params)
}
// 已设置商品分类绑定
export function goodsCategoryBindList(params) {
  return service.post(api.goodsCategoryBindList,params)
}
// 取消商品分类绑定
export function goodsCategorySortCancerl(params) {
  return service.post(api.goodsCategorySortCancerl,params)
}
// 用户职称身份列表
export function professionalCategories(params) {
  return service.post(api.professionalCategories,params)
}
// 员工分析明细分页列表  
export function analysisDetailList(params) {
  return service.post(api.analysisDetailList,params)
}
// 用户科室统计图
export function userDepartmentStatistic(params) {
  return service.post(api.userDepartmentStatistic,params)
}
// 直播流量分析
export function liveAnalyse(params) {
  return service.get(api.liveAnalyse(params))
}
// 昨日分析
export function yesterdayData(params) {
  return service.get(api.yesterdayData(params))
}
// 资讯流量分析
export function articleAnalyse(params) {
  return service.post(api.articleAnalyse,params)
}































