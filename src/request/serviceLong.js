import axios from 'axios'
import api from './api'
import store from "../store";
import { ElMessage, ElLoading } from 'element-plus'
const baseurl = `/apis/yxd` // 代理环境
const service = axios.create({
  baseURL: baseurl
})
service.defaults.timeout = 600000
service.defaults.baseURL = baseurl
// 请求是否带上cookie
service.defaults.withCredentials = true
service.defaults.headers.post['Content-Type'] = 'application/json'

let loading
function startLoading() {
  loading = ElLoading.service({
    lock: true,
    text: '审核需要时间，请稍等......',
    background: 'rgba(0, 0, 0, 0.1)'
  })
}
function endLoading() {
  loading.close()
}

// 请求拦截
service.interceptors.request.use(
  (config) => {
    startLoading()
    const user = sessionStorage.getItem('yxd_backstage_info')
    const auto = localStorage.getItem('isChoseAuto')
    // localStorage.setItem

    if (process.env.VUE_APP_TBA === 'dev') {
      // 开发环境
      config.headers.domain = 'ebwonc.medon.com.cn'
      // config.headers.domain = 'acces.medon.com.cn'

    } else {
      // 线上环境
      config.headers.domain = window.location.host
    }
    let routeActive = window.location.href.split('#/')[1]
    let routeWhiteList = ['admin_permission']
    // let courseList = ['course']
    let routeId = 1
    if(store.state.menuInfo && store.state.menuInfo.length) {
      store.state.menuInfo.forEach((item) => {
        if(item.route == routeActive) {
          routeId = item.id
        }
        if (item.childMenu.length > 0) {
          item.childMenu.forEach((row) => {
            if (row.route == routeActive) {
              // 权限特殊处理
              if(routeActive.includes(routeWhiteList)) {
                routeId = row.parentId
              }else {
                routeId = row.id
              }
            }
            // /role/query权限接口统一获取admin_permission的parentId
            if(config.url == '/role/query' && row.route.includes(routeWhiteList)) {
              routeId = row.parentId
            }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_list') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url == '/course/task/list' && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_task') {
            //       routeId = n.id
            //     }
            //   })
            // }
            // // 课程3个tab特殊处理
            // if(config.url.includes('medsciTagModuleSort/getObjectIdsByTagId') && row.route.includes(courseList)) {
            //   row.childMenu.forEach(n=>{
            //     if(n.route == 'course_sort') {
            //       routeId = n.id
            //     }
            //   })
            // }
          });
        }
      });
    }

    config.headers.routeId = routeId

    if (auto) {
      const autoData = JSON.parse(auto)
      if (autoData) {
        const token = autoData
        config.headers.Authorization = token
        return config
      }
    }
    if (user) {
      const userData = JSON.parse(user)
      if (userData) {
        const token = userData.token.accessToken
        config.headers.Authorization = token
      }
    }
    return config
  },
  (err) => {
    setTimeout(() => {
      endLoading()
    }, 3000)
    return Promise.reject(err)
  }
)
// return intercept
service.interceptors.response.use(
  function (res) {
    endLoading()

    if (res.data?.status == '1001') {
      ElMessage.warning(res.data.message)
      sessionStorage.removeItem('yxd_backstage_info')
      window.localStorage.removeItem('isChoseAuto')
      setTimeout(() => {
        window.location.reload()
      }, 2000)
    } else if (res.data?.status && res.data.status != '200' && res.data.status != '905') {
      ElMessage.warning(res.data.message)
      return Promise.resolve(res.data)
    } else {
      return Promise.resolve(res.data)
    }
  },
  function (error) {
    setTimeout(() => {
      endLoading()
    }, 1000)
    return Promise.reject(error)
  }
)
export default service

// 知识库审核文件
export function docsAudit(params){
  return service.post(api.docsAudit,params)
}













