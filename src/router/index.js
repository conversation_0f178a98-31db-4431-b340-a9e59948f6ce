import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Seo from '@/views/Seo.vue'
import Login from '@/views/Login'


const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/examResult',
    name: 'examResult',
    meta: {
      title: ''
    },
    component: () => import('@/views/examResult/index.vue')
  },
  {
    path: '/',
    name: 'Home',
    component: Home,
    children: [
      {
        path: '/home',
        name: 'home',
        meta: {
          title: '首页'
        },
        component: () => import('@/views/Dashboard.vue')
      },
     
      {
        path: '/tabs',
        name: 'tabs',
        meta: {
          title: 'tab标签'
        },
        component: () => import('@/views/Tabs')
      },
      {
        path: '/article',
        name: 'article',
        meta: {
          title: '资讯'
        },
        component: () => import('@/views/Information.vue')
      },
      {
        path: '/journal',
        name: 'journal',
        meta: {
          title: '期刊'
        },
        component: () => import('@/views/periodical.vue')
      },
      {
        path: '/course',
        name: 'course',
        meta: {
          title: '课程'
        },
        component: () => import('@/views/Course.vue')
      },
      {
        path: '/meet',
        name: 'meet',
        meta: {
          title: '会议'
        },
        component: () => import('@/views/Meet')
      },
      {
        path: '/live_info',
        name: 'live_info',
        meta: {
          title: '直播'
        },
        component: () => import('@/views/Live.vue')
      },
      {
        path: '/form',
        name: 'form',
        meta: {
          title: '表单'
        },
        component: () => import('@/views/material')
      },
      {
        path: '/guider',
        name: 'guider',
        meta: {
          title: '指南'
        },
        component: () => import('@/views/handbook')
      },
      {
        path: '/category',
        name: 'category',
        meta: {
          title: '分类'
        },
        component: () => import('@/views/Classifys.vue')
      },
      {
        path: '/ugc',
        name: 'ugc',
        meta: {
          title: 'UGC'
        },
        component: () => import('@/views/ugc.vue')
      },
      {
        path: '/ugc_edit',
        name: 'ugc_edit',
        meta: {
          title: 'UGC编辑'
        },
        component: () => import('@/views/ugc_edit.vue')
      },
      {
        path: '/task',
        name: 'task',
        meta: {
          title: '任务管理'
        },
        component: () => import('@/views/tasks.vue')
      },
      {
        path: '/exam',
        name: 'exam',
        meta: {
          title: '考试管理'
        },
        component: () => import('@/views/examination.vue')
      },
      {
        path: '/user_operate_log',
        name: 'user_operate_log',
        meta: {
          title: '考试管理'
        },
        component: () => import('@/views/user_operate_log.vue')
      },
      {
        path: '/comment',
        name: 'comment',
        meta: {
          title: '评论'
        },
        component: () => import('@/views/Comment.vue')
      },
      {
        path: '/intergral_log',
        name: 'intergral_log',
        meta: {
          title: '积分流水'
        },
        component: () => import('@/views/record.vue')
      },
      {
        path: '/notice',
        name: 'notice',
        meta: {
          title: '公告'
        },
        component: () => import('@/views/Notice.vue')
      },
      {
        path: '/leave',
        name: 'leave',
        meta: {
          title: '留言'
        },
        component: () => import('@/views/Leave.vue')
      },
      {
        path: '/informed_consent',
        name: 'informed_consent',
        meta: {
          title: '项目须知'
        },
        component: () => import('@/views/Consent.vue')
      },
      {
        path: '/goods',
        name: 'goods',
        meta: {
          title: '商品'
        },
        component: () => import('@/views/Goods.vue')
      },
      {
        path: '/order',
        name: 'order',
        meta: {
          title: '兑换订单'
        },
        component: () => import('@/views/Order.vue')
      },
      {
        path: '/staff_analysis',
        name: 'staff_analysis',
        meta: {
          title: '员工分析'
        },
        component: () => import('@/views/Employee.vue')
      },
      {
        path: '/content_analysis',
        name: 'content_analysis',
        meta: {
          title: '内容分析'
        },
        component: () => import('@/views/Content.vue')
      },
      {
        path: '/distributors_analysis',
        name: 'distributors_analysis',
        meta: {
          title: '分销员'
        },
        component: () => import('@/views/Distributors.vue')
      },
      {
        path: '/doctor_user_list',
        name: 'doctor_user_list',
        meta: {
          title: '医生用户列表'
        },
        component: () => import('@/views/Users.vue')
      },
      {
        path: '/doctor_auth_list',
        name: 'doctor_auth_list',
        meta: {
          title: '医生认证列表'
        },
        component: () => import('@/views/DoctorAuthList.vue')
      },
      {
        path: '/doctor_role_list',
        name: 'doctor_role_list',
        meta: {
          title: '医生用户组管理'
        },
        component: () => import('@/views/Group')
      },
      {
        path: '/patient_user_list',
        name: 'patient_user_list',
        meta: {
          title: '患者用户列表'
        },
        component: () => import('@/views/patient_user_list.vue')
      },
      {
        path: '/patient_role_list',
        name: 'patient_role_list',
        meta: {
          title: '患者用户管理'
        },
        component: () => import('@/views/patient_role_list.vue')
      },
      {
        path: '/backstage_user_list',
        name: 'backstage_user_list',
        meta: {
          title: '后台用户列表'
        },
        component: () => import('@/views/backstage_user_list.vue')
      },
      {
        path: '/backstage_role_list',
        name: 'backstage_role_list',
        meta: {
          title: '后台用户管理'
        },
        component: () => import('@/views/backstage_role_list.vue')
      },

      
      {
        path: '/theme_settings',
        name: 'theme_settings',
        meta: {
          title: '项目装修'
        },
        component: () => import('@/views/Theme.vue')
      },
      {
        path: '/project_settings',
        name: 'project_settings',
        meta: {
          title: '项目设置'
        },
        component: () => import('@/views/setting/itemset.vue')
      },
      {
        path: '/background',
        name: 'background',
        meta: {
          title: '登录背景'
        },
        component: () => import('@/views/setting/background.vue')
      },
      {
        path: '/homt_settings',
        name: 'homt_settings',
        meta: {
          title: '首页功能'
        },
        component: () => import('@/views/Setting.vue')
      },
      {
        path: '/Identity',
        name: 'Identity',
        meta: {
          title: '身份信息'
        },
        component: () => import('@/views/Identity')
      },
      {
        path: '/ad_list',
        name: 'ad_list',
        meta: {
          title: '轮播图列表'
        },
        component: () => import('@/views/Swiper.vue')
      },
      {
        path: '/cosDemo',
        name: 'cosDemo',
        meta: {
          title: '腾讯cos上传demo'
        },
        component: () => import('@/views/cosDemo.vue')
      },
      {
        path: '/seo',
        name: 'seo',
        meta: {
          title: 'SEO优化'
        },
        component: Seo
      },
      {
        path: '/front',
        name: 'front',
        meta: {
          title: '前台权限'
        },
        component: () => import('@/views/Front')
      },
      {
        path: '/admin_permission',
        name: 'admin_permission',
        meta: {
          title: '后台权限'
        },
        component: () => import('@/views/Backstage')
      },
      {
        path: '/survey',
        name: 'survey',
        meta: {
          title: '调研'
        },
        component: () => import('@/views/Survey')
      },
      {
        path: '/goods_category',
        name: 'storeCategory',
        meta: {
          title: '商品分类'
        },
        component: () => import('@/views/storeCategory')
      },
      {
        path: '/voucher',
        name: 'virtual_voucher',
        meta: {
          title: '虚拟券码管理'
        },
        component: () => import('@/views/virtual_voucher')
      },
      {
        path: '/goods',
        name: 'goodsEdit',
        meta: {
          title: '商品管理'
        },
        component: () => import('../components/Goods/edit')
      },
      {
        path: '/yxd',
        name: 'yxd',
        meta: {
          title: '医讯达'
        },
        component: () => import('@/views/Yxd')
      },
      {
        path: '/special_topic',
        name: 'special_topic',
        meta: {
          title: '合集管理'
        },
        component: () => import('@/views/special')
      },
      {
        path: '/reportcase',
        name: 'reportcase',
        meta: {
          title: '病例'
        },
        component: () => import('@/views/reportCase')
      },
      {
        path: '/add_user_custom',
        name: 'userdefined',
        meta: {
          title: '自定义用户信息'
        },
        component: () => import('@/views/UserDefined')
      },
      {
        path: '/tob/:id',
        name: 'tob_project',
        meta: {
          title: 'tob项目'
        },
        component: () => import('@/views/qiankun.vue')
      },
      {
        path: '/imsl',
        name: 'imsl',
        meta: {
          title: 'imsl管理'
        },
        component: () => import('@/views/imsl.vue')
      },
     
      {
        path: '/imsl/imslDetail',
        name: 'imslDetail',
        meta: {
          title: 'imsl对话详情'
        },
        component: () => import('../components/imsl/detail.vue')
      },
      {
        path: '/special_topic/special-operation',
        name: 'specialOperation',
        meta: {
          title: '合集操作'
        },
        component: () => import('../components/special/special-operation.vue')
      },
      
      // {
      //   path: 'material-operation-form',
      //   name: 'formMenu',
      //   meta: {
      //     title: '表单'
      //   },
      //   component: () => import('../components/material/create.vue')
      // },
      {
        path: 'form-details',
        name: 'ms-yxd-manage',
        meta: {
          title: '调研详情'
        },
        component: () => import('../components/material/ms-form-details.vue')
      },
      {
        path: '/salesman',
        name: 'distributors',
        meta: {
          title: '分销者行为'
        },
        component: () => import('@/views/Distributors.vue')
      },
      {
        path: '/customer',
        name: 'customer',
        meta: {
          title: '用户行为'
        },
        component: () => import('@/views/Customer.vue')
      },
      {
        path: '/404',
        name: '404',
        meta: {
          title: '找不到页面'
        },
        component: () => import('../views/404.vue')
      },
      {
        path: '/403',
        name: '403',
        meta: {
          title: '没有权限'
        },
        component: () => import('../views/403.vue')
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    meta: {
      title: '登录'
    },
    component: Login
  }
]

const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  const project = sessionStorage.getItem('yxd_backstage_project')
  document.title = `${to.meta.title} | ${project?JSON.parse(project).name+'后台管理':'后台管理'}  `
  const role = sessionStorage.getItem('yxd_backstage_info')
  let roleObject = JSON.parse(role)
  if (roleObject == null && to.path !== '/login') {
    next('/login')
  } else if (roleObject != null && to.path == '/login') {
    // 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
    next('/home')
  } else {
    next()
  }
})

export default router
