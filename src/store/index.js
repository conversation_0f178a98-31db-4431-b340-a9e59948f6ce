import { createStore } from "vuex";
export default createStore({
  state: {
    tagsList: [],
    collapse: false,
    projectInfo: JSON.parse(sessionStorage.getItem("yxd_backstage_project")),
    backInfo: JSON.parse(sessionStorage.getItem("yxd_backstage_info")),
    menuInfo: JSON.parse(sessionStorage.getItem("yxd_backstage_menu")),
    editorImgArr: [],
    isScroll: false,
  },
  mutations: {
    setIsScroll(state, val) {
      state.isScroll = val;
    },
    setProjectInfo(state, val) {
      state.projectInfo = val;
      sessionStorage.setItem("yxd_backstage_project", JSON.stringify(val));
    },
    setBackInfo(state, val) {
      state.backInfo = val;
      sessionStorage.setItem("yxd_backstage_info", JSON.stringify(val));
    },
    setMenuInfo(state, val) {
      state.menuInfo = val;
      sessionStorage.setItem("yxd_backstage_menu", JSON.stringify(val));
    },
    delTagsItem(state, data) {
      state.tagsList.splice(data.index, 1);
    },
    setTagsItem(state, data) {
      state.tagsList.push(data);
    },
    clearTags(state) {
      state.tagsList = [];
    },
    closeTagsOther(state, data) {
      state.tagsList = data;
    },
    closeCurrentTag(state, data) {
      for (let i = 0, len = state.tagsList.length; i < len; i++) {
        const item = state.tagsList[i];
        if (item.path === data.$route.fullPath) {
          if (i < len - 1) {
            data.$router.push(state.tagsList[i + 1].path);
          } else if (i > 0) {
            data.$router.push(state.tagsList[i - 1].path);
          } else {
            data.$router.push("/");
          }
          state.tagsList.splice(i, 1);
          break;
        }
      }
    },
    // 侧边栏折叠
    hadndleCollapse(state, data) {
      state.collapse = data;
    },
    SET_EDITOR_IMG_ARR: (state, data) => {
      state.editorImgArr = data;
    },
  },
  actions: {
    // 设置富文本内容图片
    SetEditorImg({ commit }, data) {
      let editImgArr = null;
      if (data && data.length > 0) {
        editImgArr = data.map((v) => {
          if (v.substr(v.lastIndexOf(".") + 1) !== "webp") {
            return v;
          }
        });
      } else {
        editImgArr = [];
      }
      commit("SET_EDITOR_IMG_ARR", editImgArr);
    },
  },
  modules: {},
});
