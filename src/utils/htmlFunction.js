const escapeMap = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#x27;",
  "`": "&#x60;",
};
const unescapeMap = {
  "&amp;": "&",
  "&lt;": "<",
  "&gt;": ">",
  "&quot;": '"',
  "&#x27;": "'",
  "&#x60;": "`",
  "&ldquo;": "“",
  "&rdquo;": "”",
  "&lsquo;": "‘",
  "&rsquo;": "’",
  "&middot;": "·",
  "&nbsp;": " ",
  "&hellip;": "…",
};
const htmlMethods = {
  excapeHtml(content) {
    var escaper = function(match) {
      return escapeMap[match];
    };
    // Regexes for identifying a key that needs to be escaped
    var source = "(?:" + Object.keys(escapeMap).join("|") + ")";
    var testRegexp = RegExp(source);
    var replaceRegexp = RegExp(source, "g");
    var string = content == null ? "" : "" + content;
    return testRegexp.test(string)
      ? string.replace(replaceRegexp, escaper)
      : string;
  },
  unexcapeHtml(content) {
    var escaper = function(match) {
      return unescapeMap[match];
    };
    // Regexes for identifying a key that needs to be escaped
    var source = "(?:" + Object.keys(unescapeMap).join("|") + ")";
    var testRegexp = RegExp(source);
    var replaceRegexp = RegExp(source, "g");
    var string = content == null ? "" : "" + content;
    return testRegexp.test(string)
      ? string.replace(replaceRegexp, escaper)
      : string;
  },
};
export default htmlMethods;
