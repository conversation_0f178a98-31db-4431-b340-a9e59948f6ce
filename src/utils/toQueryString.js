export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}
// 将一个对象转成QueryString
export function toQueryString(obj) {
  if (!obj) return "";
  return cleanArray(
    Object.keys(obj).map((key) => {
      if (obj[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(obj[key]);
    })
  ).join("&");
}
// 将字母转换成数字
export function charToNum(strChar){
  var regex = /[a-zA-Z]/g;
  let arr=strChar.match(regex);
  let num=0;
  for(let i=0;i<arr.length;i++){
      let t=arr[i].charCodeAt(0)%65+1;
      num+=t*Math.pow(26, arr.length-i-1)
  }
  return num;
}
// 将数字转换成字母
export function numToChar(num){
  let a=num/27;
  let b=num%27;
  if(a>=1) return numToChar(a-1)+String.fromCharCode(b+64);
  return String.fromCharCode(b+64);
}
