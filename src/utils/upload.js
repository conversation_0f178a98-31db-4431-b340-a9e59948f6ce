import { ElMessage } from "element-plus";
import { uploadAdminAvatar } from "@/request/service.js";

function filetoDataUrl(file) {
    return new Promise((resolve, reject) => {
        var reader = new FileReader();
        reader.onload = function () {
            resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

export async function beforeAvatarUpload(maxWidth, maxHeight, file) {
    let url;
    const img = new Image();
    img.src = URL.createObjectURL(file);

    await new Promise((resolve, reject) => {
        img.onload = () => {
            const width = img.width;
            const height = img.height;
            if(maxWidth&&maxHeight){
                if (width !== maxWidth || height !== maxHeight) {
                    ElMessage.warning(`图片尺寸超过限制，尺寸为${maxWidth}×${maxHeight}`);
                    reject();
                } else {
                    resolve();
                }
            }else{
                resolve();
            }
            
        };
        img.onerror = reject;
    });

    try {
        const imgDataUrl = await filetoDataUrl(file);
        let params = {
            base64: imgDataUrl,
        };
        let res = await uploadAdminAvatar(params);
        if (res.status === 200) {
            url = res.data.url;
        }
    } catch (error) {
        console.error(error);
    }

    return url;
}