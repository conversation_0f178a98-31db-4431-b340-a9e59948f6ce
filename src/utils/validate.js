/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  return str.trim();
}

export function validPhone(phone) {
  return /^((0\d{2,3}\d{7,8})|(1[23456789]\d{9}))$/.test(phone);
}

// 判断选项是否连续不重复(返回true则为连续不重复)
export function validOptions(options){
 return options.every((item,index)=>item.option === String.fromCharCode(index+65))
}
// 判断内容选项是否重复（返回true则为重复）
export function isRepeat(options){
  var hash = {};
  for(var i in options){
    if(hash[options[i].content])
      return true
      hash[options[i].content] = true
  }
  return false
}