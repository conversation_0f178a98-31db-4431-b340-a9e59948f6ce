<template>
  <div class="yxd_backstage">
    <backstage-index v-if="isIndex" @edit="editFn"></backstage-index>
    <backstage-edit
      v-else
      :info="currentInfo"
      @before="isIndex = true"
    ></backstage-edit>
  </div>
</template>

<script>
import backstageIndex from "@/components/Backstage/index";
import backstageEdit from "@/components/Backstage/edit";
export default {
  name: "backstage",
  components: {
    backstageIndex,
    backstageEdit,
  },
  data() {
    return {
      isIndex: true,
      currentInfo: {},
    };
  },
  mounted() {},
  methods: {
    editFn(val) {
      this.isIndex = false;
      this.currentInfo = val;
    },
  },
};
</script>

<style scoped lang="scss">

.yxd_backstage {
}
</style>
