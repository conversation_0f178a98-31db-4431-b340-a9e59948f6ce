<template>
  <div class="class_ify">
    <classify-index v-if="isIndex" @sort="sortClass" ></classify-index>
    <classify-create
      v-else
      :type="type"
      @back="isIndex = true"
    ></classify-create>
  </div>
</template>
<script>
import classifyIndex from "@/components/Classify/index";
import ClassifyCreate from "@/components/Classify/Classify";
export default {
  name: "Classifys",
  components: {
    classifyIndex,
    ClassifyCreate,
  },
  data() {
    return {
      isIndex: true,
      currentData: {},
      custom:{},
      type: "",
    };
  },
  mounted() {},
  methods: {
    sortClass(category) {
      this.type = category;
      this.isIndex = false;
    },
    // editUser(item) {
    //   this.type = "edit";
    //   this.currentData = item;
    //   this.isIndex = false;
    // },
  },
};
</script>
<style scoped lang="scss">
.class_ify {
}
</style>