<template>
  <div class="yxd_comment">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="comment" placeholder="请输入评论对象"></el-input>
          </div>
          <div class="col">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="创建开始时间"
              end-placeholder="创建结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column
            prop="objectTitle"
            label="评论对象"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="content"
            label="评论内容"
            width="300"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="hasAttachment"
            label="是否有图片"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="objectType"
            label="对象类型"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="评论人"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            sortable
            width="200"
            align="center"
          >
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            min-width="120"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="btnList.remove_btn"
                plain
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除当前评论</span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="deleteSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { getCommentsPage, batchDealComment } from "@/request/service.js";
export default {
  name: "comment",
  mixins: [initModule, initScroll],
  data() {
    return {
      comment: "",
      time: "",
      deleteVisible: false,
      currentChose: {},
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let projectIds = [];
      projectIds.push(this.$store.state.projectInfo.id);
      let beginTime;
      let endTime;
      if (this.time && this.time.length > 0) {
        beginTime = this.time[0];
        endTime = this.time[1];
      }
      let params = {
        objectTitle: this.comment,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectIds: projectIds,
        startBeginAt: beginTime,
        startStopAt: endTime,
      };
      getCommentsPage(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.total = res.totalSize;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.hasAttachment) {
              val.hasAttachment = "是";
            } else {
              val.hasAttachment = "否";
            }
          });
        }
      });
    },
    reset() {
      this.comment = "";
      this.time = "";
    },
    handleDelete(index, row) {
      this.currentChose = row;
      this.deleteVisible = true;
    },
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        dealType: 3,
        ids: ids,
      };
      batchDealComment(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_comment {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }

}
</style>
