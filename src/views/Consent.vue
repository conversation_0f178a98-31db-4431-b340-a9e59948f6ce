<template>
  <div class="yxd_consent">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
      v-html="content"
    ></div>
  </div>
</template>

<script>
import { getCompanyByProjectId } from "@/request/service.js";
import { unescapeMap } from "@/utils/constant.js";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "consent",
  mixins: [initScroll],
  data() {
    return {
      content: "",
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      getCompanyByProjectId(this.$store.state.projectInfo.id).then((res) => {
        if (res.status == 200) {
          this.content = this.unexcapeHtml(res.data.content);
          this.initScroll();
        }
      });
    },
    unexcapeHtml(content) {
      var escaper = function(match) {
        return unescapeMap[match];
      };
      // Regexes for identifying a key that needs to be escaped
      var source = "(?:" + Object.keys(unescapeMap).join("|") + ")";
      var testRegexp = RegExp(source);
      var replaceRegexp = RegExp(source, "g");
      var string = content == null ? "" : "" + content;
      return testRegexp.test(string)
        ? string.replace(replaceRegexp, escaper)
        : string;
    },
  },
};
</script>

<style scoped lang="scss">
.container{
  padding: 20px;
}
.yxd_consent {
}
</style>
