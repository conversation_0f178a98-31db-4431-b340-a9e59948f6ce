<template>
  <div class="yxd_content" ref="yxd_container">
    <div class="contain">
      <div class="board">
        <div class="box">
          <div class="title">昨日概况</div>
          <div class="main">
            <div class="num">
              <div class="top first">{{ yesterdayDatas?.viewCount }}</div>
              <div class="bottom ">阅读（次）</div>
            </div>
            <div class="num">
              <div class="top first">{{ yesterdayDatas?.shares }}</div>
              <div class="bottom ">转发（次）</div>
            </div>
            <div class="num">
              <div class="top">{{ yesterdayDatas?.completeCount }}</div>
              <div class="bottom ">完成阅读（次）</div>
            </div>
          </div>
        </div>
      </div>
      <div class="hot">
        <div class="row">
          <div class="title">流量分析</div>
          <div class="content_box">
            <span class="content_title">数据指标</span>
            <el-button :type="indicator == 1 ? 'primary' : ''" @click="changeIndicator(1)">阅读</el-button>
            <el-button :type="indicator == 2 ? 'primary' : ''" @click="changeIndicator(2)">转发</el-button>
            <el-button :type="indicator == 3 ? 'primary' : ''" @click="changeIndicator(3)">读完率</el-button>
          </div>
          <div class="content_box">
            <span class="content_title">内容</span>
            <el-select v-model="surveyType" style="width:400px" @change="changeSurveyType" filterable placeholder="请选择">
              <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="content_box" v-if="surveyType == 2">
            <span class="content_title">资讯</span>
            <el-select v-model="searchSurveyTitle" style="width:400px" :remote-method="remoteSurvey" :loading="loading"
              @change="changeSurvey" remote filterable placeholder="请选择">
              <el-option v-for="item in options1" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="content_box">
            <span class="content_title">统计周期</span>
            <el-date-picker v-model="surveyTime"   :disabled-date="disabledDate" type="datetimerange" :shortcuts="shortcuts" style="width:400px" @blur="blurScreen" @focus="changeScreen" @change="surveyChangeTime" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </div>
          <div id="linesChart" v-if="echartsShow" style="width: 100%; height: 400px; margin: 10px auto 0" class="d_item"></div>
        </div>
        <div class="row">
          <div class="title">直播流量分析</div>
          <div class="content_box">
            <span class="content_title">内容</span>
            <el-select v-model="searchTitle" style="width:400px" :remote-method="remote" :loading="loading" @change="change" remote
              filterable placeholder="请选择">
              <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="content_box" v-if="liveTime">
            <span class="content_title">直播日期</span><span style="font-size:14px">{{ liveTime }}</span>
          </div>
          <div class="board" v-if="liveTime">
            <div class="box">
              <div class="main">
                <div class="num">
                  <div class="top first">{{ averageTime }}</div>
                  <div class="bottom">平均观看时长(分钟)</div>
                </div>
                <div class="num">
                  <div class="top">{{ shares }}</div>
                  <div class="bottom">转发次数</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { liveQuery, liveAnalyse, yesterdayData, articleAnalyse, getAriticlePage } from "@/request/service.js";
import * as echarts from "echarts";
import dayjs from "dayjs";
export default {
  name: "content",
  data() {
    return {
      pageIndex: 1,
      pageSize: 10,
      yesterdayDatas: {},
      searchTitle: "",
      loading: false,
      liveTime: "",
      shares: "",
      averageTime: "",
      searchSurveyTitle: "",
      options2: [
        { label: "全部", value: 1 },
        { label: "单篇", value: 2 },
      ],
      options: [],
      surveyTime: [],
      surveyType: 1,
      indicator: 1,
      echartsShow:false,
      shortcuts: [
        {
          text: "最近一周",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          },
        },
        {
          text: "最近一个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          },
        },
        {
          text: "最近三个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
          },
        },
      ],
      myChart:null,
      lineOptions: {
        tooltip: {
              trigger: "axis",
              formatter: (params) => {
                return `<td>${params[0].marker}${params[0].name}</td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td>${params[0].data}%</td>
        `;
              },
            },
        xAxis: {
          name: "",
          type: "category",
          data: [],
        },
        yAxis: {
          name: "登录人数",
          type: "value",
          axisLine: { show: true ,length:10 },
          minInterval: 1,
        },
        series: [
          {
            data: [],
            type: "line",
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
    this.initDate()
    this.changeSurvey()
  },
    // 在Vue组件中
  beforeUnmount() {
    if (this.myChart != null && this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    disabledDate(time) {
      return time.getTime() > Date.now();  // 8.64e7 毫秒数代表一天
    },
    initDate() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      // 设置默认时间
      this.surveyTime = [
        dayjs(start).format("YYYY-MM-DD 00:00:00"),
        dayjs(end).format("YYYY-MM-DD 00:00:00")
      ];
    },
    // 根据分辨率判断距离底部的高度
    changeScreen(){
        if(this.liveTime){
          this.$refs.yxd_container.style.paddingBottom = "60px" 
        }else{
          this.$refs.yxd_container.style.paddingBottom = "300px" 
        }
    },
    calculateScaleRatio() {
      // 获取屏幕分辨率的宽度和高度（以像素为单位）
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;
 
      // 获取屏幕的 devicePixelRatio
      const devicePixelRatio = window.devicePixelRatio;
 
      // 计算缩放比例（%）
      return Math.round((screenWidth * devicePixelRatio) / screenWidth * 100);
    },
    blurScreen(){
          this.$refs.yxd_container.style.paddingBottom = "0px" 
    },
    initLine(val) {
      this.$nextTick(() => {
        this.myChart = echarts.init(
          document.getElementById("linesChart"),
          null,
          { renderer: "svg" }
        );
        this.lineOptions.xAxis.data = [];
        this.lineOptions.yAxis.name = this.indicator == 1 ? '阅读数' : this.indicator == 2 ? '转发数' : '读完率（%）';
        this.lineOptions.series[0].data = [];
        if(val&&val.length){
          val.forEach((d) => {
            this.lineOptions.xAxis.data.push(d.dateTime);
            this.lineOptions.series[0].data.push(d.value);
          });
        }
        
        this.myChart.setOption(this.lineOptions);
      });
    },
    async change(val) {
      this.options.forEach((element) => {
        if (element.id == val) {
          this.liveTime = element.liveStartTime;
        }
      });
      let params = {
        liveId: val,
      };
      let res = await liveAnalyse(params);
      this.shares = res.data.shares;
      this.averageTime = res.data.averageTime;
      this.changeScreen()
    },
    changeSurveyType(val) {
      if (val == 1) {
        this.searchSurveyTitle = ""
      }
      this.surveyTime = []
      this.changeSurvey()
    },
    surveyChangeTime(val) {
      this.surveyTime = val
      this.changeSurvey()
    },
    changeIndicator(val) {
      this.indicator = val
      this.changeSurvey()
    },
    async changeSurvey() {
      this.echartsShow = false
      if (this.surveyType == 1) {
        if (this.surveyTime.length > 0 && this.indicator) {
          let params = {
            "indicator": this.indicator,
            "articleId": this.searchSurveyTitle,
            "startTime": this.surveyTime[0],
            "endTime": this.surveyTime[1]
          };
          let res = await articleAnalyse(params);
          this.echartsShow = true
           this.initLine(res.data)
        }
      } else {
        if (this.surveyTime.length > 0 && this.indicator && this.searchSurveyTitle) {
          let params = {
            "indicator": this.indicator,
            "articleId": this.searchSurveyTitle,
            "startTime": this.surveyTime[0],
            "endTime": this.surveyTime[1]
          };
          let res = await articleAnalyse(params);
          this.echartsShow = true
          this.initLine(res.data)
        }
      }
    },
    async init() {
      let { data } = await yesterdayData();
      this.yesterdayDatas = data;
    },
    async remote(val) {
      if (val && val.length > 1) {
        this.loading = true;
        let params = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize,
          searchTitle: val,
        };
        let res = await liveQuery(params);
        this.loading = false;
        this.options = res.data;
      }
    },
    async remoteSurvey(val) {
      if (val && val.length > 1) {
        this.loading = true;
        let params = {
          pageIndex: 1,
          pageSize: 10,
          value: val,
          projectId: this.$store.state.projectInfo.id,
        };
        let res = await getAriticlePage(params)
        this.loading = false;
        this.options1 = res.data;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_content {
  .contain {
    .content_box {
      margin-top: 16px;
      .title {
        display: inline-block;
        width: 100px;
        margin-right: 10px;
        text-align: right;
      }
    }

    .board {
      display: flex;
      justify-content: space-between;
      margin-top: 2px;
      background: #fff;

      .box {
        width: 100%;
        background: #fff;
        padding: 20px 25px;

        .title {
          height: 24px;
          opacity: 1;
          font-size: 18px;
          font-family: PingFangSC, PingFangSC-Medium;
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
          margin-bottom: 18px;
        }

        .main {
          display: flex;
          margin-bottom: 20px;

          .num {
            width: 50%;
            margin-top: 30px;
            .top {
              position: relative;
              height: 22px;
              font-size: 30px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 500;
              color: #000000;
              line-height: 30px;
              text-align: center;
              margin-bottom: 18px;
              display: flex;
              justify-content: center;
              align-items: center;

              .circle {
                display: block;
                margin-right: 4px;
                width: 6px;
                height: 6px;
                opacity: 1;
                background: #1890ff;
                border-radius: 50%;
              }
            }

            .first::after {
              position: absolute;
              content: "";
              height: 30px;
              width: 1px;
              background: #e9e9e9;
              right: 0;
              top: 9px;
            }

            .bottom {
              text-align: center;
              opacity: 1;
              font-size: 14px;
              font-family: HelveticaNeue, HelveticaNeue-Medium;
              color: #999999;
              line-height: 14px;
            }
          }
        }
      }
    }

    .hot {
      margin-top: 12px;
      width: 100%;
      padding-bottom: 30px;
      .row:first-child{
        background: #fff;
      }
      .row:last-child{
        background: #fff;
        margin-top: 12px;
      }
      .content_title{
          font-size: 14px;
          opacity: 1;
          color: #999999;
          line-height: 24px;
          display: inline-block;
          width: 70px;
          text-align: right;
          padding-right: 10px;
      }
      .row {
        padding: 20px;

        .box {
          padding: 0 !important;
        }

        .board {
          margin-top: 10px !important;
        }

        .bottom {
          font-size: 14px !important;
          color: #999999 !important;
        }

        .top {
          font-size: 30px !important;
          color: #000000 !important;
          font-weight: 500;
          line-height: 30px;
          margin-top: 30px;
        }

        .title {
          opacity: 1;
          font-size: 18px;
          font-family: PingFangSC, PingFangSC-Medium;
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
        }

        .all {
          display: flex;
          justify-content: space-between;

          .item {
            margin-top: 25px;
            display: flex;
            flex-direction: column;
            width: 30%;
            opacity: 1;
            font-size: 22px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: #000000;
            line-height: 22px;
            text-align: center;

            .t1 {
              text-align: center;
              margin-bottom: 17px;
              opacity: 1;
              font-size: 22px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              color: #000000;
              line-height: 22px;
            }

            .t2 {
              text-align: center;
              opacity: 1;
              font-size: 20px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 600;
              color: rgba(0, 0, 0, 0.75);
              line-height: 38px;
            }
          }
        }
      }
    }
  }
}
</style>
