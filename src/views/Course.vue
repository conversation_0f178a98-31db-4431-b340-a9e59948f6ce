<template>
  <div class="yxd_reportcase">
    <div id="container" class="container">
      <el-tabs v-model="activeName" @tab-click="change">
        <el-tab-pane label="课程列表" name="first">
          <div class="yxd_report_case1" >
            <course-list></course-list>
          </div>
        </el-tab-pane>
        <el-tab-pane label="课程任务" name="second">
          <div class="yxd_report_case2" >
            <add-assign v-if="type == 'add'||type == 'edit'"></add-assign>
            <assignment v-if="$route.query.name&&!type"></assignment>
          </div>
        </el-tab-pane>
         <el-tab-pane  label="课程排序" name="third">
          <div class="yxd_report_case3" >
            <assign-sort ref="questionBank"></assign-sort>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import courseList from "@/components/Course/courseList.vue";
import assignment from "@/components/Course/assignment.vue";
import addAssign from "@/components/Course/addOrEditAssign.vue";
import assignSort from "@/components/Course/assignSort.vue";

export default {
  name: "examination",
  mixins: [initModule, initScroll],
  components: {
    // exam,
    courseList,
    assignment,
    addAssign,
    assignSort
  },
  data() {
    return {
      activeName: "first",
      secondStatus: false,
      type: "",
    };
  },
  computed: {
    // activeName:function(){
    //   if(!this.btnList.survey_list&&this.btnList.case_list){
    //      return  "first"
    //   }else if(!this.btnList.survey_list&&!this.btnList.case_list){
    //      return  "third"
    //   }
    //   return "second"
    // }
  },
  watch: {
    $route(val) {
      this.type = this.$route.query.type;
        if(val.query.name=='second'){
          this.activeName="second"

        }
        if(val.query.name=='first'){
          this.activeName="first"

        }
        if(val.query.name=='third'){
          this.activeName="third"
          this.$refs.questionBank.init()
        }
    }
  },
  mounted() {
    if(this.$route.query.name){
      this.activeName =this.$route.query.name;
     this.type = this.$route.query.type;
    }else{
      this.activeName ="first"
    }
    console.log(this.activeName)
  },
  methods: {
    change(val){
      this.$router.push({path:'/Course',query:{name:val.props.name}})
    }
  },
};
</script>

<style scoped lang="scss">

.yxd_reportcase {
  .yxd_report_case1 {
    .table {
      cursor: pointer;
    }
  }
  .yxd_report_case2 {
  }
  .yxd_report_case3 {
  }
}
</style>
