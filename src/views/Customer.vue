<template>
  <div class="yxd_customer">
    <customer-index v-if="isIndex"></customer-index>
  </div>
</template>

<script>
import customerIndex from "@/components/Customer/index";
export default {
  name: "customer",
  components: {
    customerIndex,
  },
  data() {
    return {
      isIndex: true,
      currentData: {},
      custom: {},
      type: "",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.yxd_customer {
}
</style>
