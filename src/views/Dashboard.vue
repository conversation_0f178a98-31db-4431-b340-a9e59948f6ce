<template>
  <div class="dash-board">
    <div class="container-sp">
      <el-row>
        <el-col :span="24">
          <h3>访问统计</h3>
          <ul>
            <!-- <li>
              <span class="tl">今日PV</span>
              <span class="bold">23</span>
            </li> -->
            <li>
              <span class="tl"
                >昨日PV
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>
                        上一个自然日，当前项目专区的所有页面被访问次数求和
                      </div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{ statisticsData.pv }}</span>
            </li>
            <li>
              <span class="tl"
                >累计PV
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>专区生效后，所有页面累计被访问的次数求和</div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{ statisticsData.pvToDate }}</span>
            </li>
            <!-- <li>
              <span class="tl">今日UV</span>
              <span class="bold">5</span>
            </li> -->
            <li>
              <span class="tl"
                >昨日UV
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>
                        上一个自然日，当前项目专区的所有页面被访问的用户的数量求和
                      </div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{ statisticsData.uv }}</span>
            </li>
            <li>
              <span class="tl"
                >累计UV
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>
                        专区生效后，所有页面累计被访问的用户ID去重后求和
                      </div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{ statisticsData.uvToDate }}</span>
            </li>
          </ul>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <h3>专区用户规模</h3>
          <ul>
            <li>
              <span class="tl"
                >总人数
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>该项目专区生效后，累计注册的用户ID数求和</div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{ accountData.currentAccountCount }}</span>
            </li>
            <li>
              <span class="tl"
                >新增人数
                <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>上一个自然日，当前项目专区新注册用户ID数</div>
                    </div>
                  </template>
                </el-tooltip>
              </span>
              <span class="bold">{{
                accountData.yesterdayAddAccountCount
              }}</span>
            </li>
          </ul>
        </el-col>
      </el-row>
      <div class="yxd_box">
        <h3>
          专区内容建设
          <el-tooltip popper-class="atooltip" effect="light" placement="top">
            <i
              class="el-icon-question"
              style="font-size: 14px; vertical-align: middle"
            ></i>
            <template #content>
              <div>
                <div>
                  统计范围，已设置分类的内容(投递但未配置分类的内容不算)
                </div>
              </div>
            </template>
          </el-tooltip>
        </h3>
        <div class="yxd-3">
          <div class="box">
            <h3>课程数</h3>
            <ul>
              <li class="first">
                <span class="bold">{{ courseData.currentCourseCount }}</span>
                <span class="tl"
                  >历史累计<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>课程的历史累计数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
              <li>
                <span class="bold">{{
                  courseData.yesterdayAddCourseCount
                }}</span>
                <span class="tl"
                  >新增<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>课程新增数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
            </ul>
          </div>
          <div class="box">
            <h3>直播数</h3>
            <ul>
              <li class="first">
                <span class="bold">{{ liveData.currentLiveCount }}</span>
                <span class="tl"
                  >历史累计<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>直播的历史累计数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
              <li>
                <span class="bold">{{ liveData.comingSoonLiveCount }}</span>
                <span class="tl"
                  >即将开播<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>已配置的当日及以后开播的数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
            </ul>
          </div>
          <div class="box">
            <h3>资讯数</h3>
            <ul>
              <li class="first">
                <span class="bold">{{ articleData.currentArticleCount }}</span>
                <span class="tl"
                  >历史累计<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>资讯的历史累计数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
              <li>
                <span class="bold">{{
                  articleData.yesterdayAddArticleCount
                }}</span>
                <span class="tl"
                  >新增<el-tooltip
                    popper-class="atooltip"
                    effect="light"
                    placement="top"
                  >
                    <i
                      class="el-icon-question"
                      style="font-size: 14px; vertical-align: middle"
                    ></i>
                    <template #content>
                      <div>
                        <div>资讯新增数量</div>
                      </div>
                    </template>
                  </el-tooltip></span
                >
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="statistics">
        <div class="top">
          <h3>用户活跃趋势</h3>
          <div class="right">
            <div class="item">
              <span>统计周期</span>
              <el-date-picker
                v-model="date"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="YYYY-MM-DD"
                @change="change"
              >
              </el-date-picker>
            </div>
            <div class="item">
              <span>单位</span>
              <el-select v-model="unit" @change="select" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="main">
          <div id="linesChart" style="width: 100%; height: 530px"></div>
        </div>
      </div>
      <div class="statistics">
        <div class="top">
          <h3>用户注册趋势</h3>
          <div class="right">
            <div class="item">
              <span>统计周期</span>
              <el-date-picker
                v-model="date1"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts1"
                value-format="YYYY-MM-DD"
                @change="change1"
              >
              </el-date-picker>
            </div>
            <div class="item">
              <span>单位</span>
              <el-select v-model="unit1" @change="select1" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="main">
          <div id="linesChart1" style="width: 100%; height: 530px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  countHomeAccount,
  countHomeArticle,
  countHomeCourse,
  countHomeLive,
  getStatistics,
  getUserUvByDateType,
  userRegisterData,
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
export default {
  name: "dashboard",
  data() {
    return {
      myChart: null,
      myChart1: null,
      date: "",
      date1: "",
      unit: 1,
      unit1: 1,
      options: [
        {
          value: 1,
          label: "天",
        },
        {
          value: 2,
          label: "周",
        },
        {
          value: 3,
          label: "月",
        },
        {
          value: 4,
          label: "季度",
        },
      ],
      lineOptions: {
        xAxis: {
          name: "",
          type: "category",
          data: [],
        },
        yAxis: {
          name: "登录人数",
          type: "value",
          axisLine: { show: true },
          minInterval: 1,
        },
        series: [
          {
            data: [],
            type: "line",
          },
        ],
      },
      lineOptions1: {
        xAxis: {
          name: "",
          type: "category",
          data: [],
        },
        yAxis: {
          name: "注册人数",
          type: "value",
          axisLine: { show: true },
          minInterval: 1,
        },
        series: [
          {
            data: [],
            type: "line",
          },
        ],
      },
      shortcuts: [
        {
          text: "最近一周",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          },
        },
        {
          text: "最近一个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          },
        },
        {
          text: "最近三个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
          },
        },
      ],
      shortcuts1: [
        {
          text: "最近一周",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          },
        },
        {
          text: "最近一个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          },
        },
        {
          text: "最近三个月",
          value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
          },
        },
      ],
      accountData: {},
      articleData: {},
      courseData: {},
      liveData: {},
      statisticsData: {},
    };
  },
  computed: {
    minDate() {
      return this.$store.state.projectInfo.startTime;
    },
  },
  mounted() {
    this.initDate();
    this.initDate1();
    this.initStatistics();
    this.initAccount();
    this.initArticle();
    this.initCourse();
    this.initLive();
    this.initLine();
    this.initLine1();
  },
  // 在Vue组件中
  beforeUnmount() {
    if (this.myChart != null && this.myChart) {
      this.myChart.dispose();
    }
    if (this.myChart1 != null && this.myChart1) {
      this.myChart1.dispose();
    }
  },
  methods: {
    select($event) {
      this.unit = $event;
      this.initLine();
    },
    select1($event) {
      this.unit1 = $event;
      this.initLine1();
    },
    change($event) {
      let pre = $event[0];
      const flag = dayjs(pre).isBefore(dayjs(this.minDate));
      // 选择的时间在 最早的日期 项目启动的时间 之前
      if (flag) {
        ElMessage.warning("在项目启动的时间之前的日期无法选择");
        this.initDate();
      }
      this.date = $event;
      this.initLine();
    },
    change1($event) {
      let pre = $event[0];
      const flag = dayjs(pre).isBefore(dayjs(this.minDate));
      // 选择的时间在 最早的日期 项目启动的时间 之前
      if (flag) {
        ElMessage.warning("在项目启动的时间之前的日期无法选择");
        this.initDate1();
      }
      this.date1 = $event;
      this.initLine1();
    },
    initDate() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      // 设置默认时间
      this.date = [
        dayjs(start).format("YYYY-MM-DD"),
        dayjs(end).format("YYYY-MM-DD"),
      ];
    },
    initDate1() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      // 设置默认时间
      this.date1 = [
        dayjs(start).format("YYYY-MM-DD"),
        dayjs(end).format("YYYY-MM-DD"),
      ];
    },
    initStatistics() {
      getStatistics().then((res) => {
        if (res.status == 200) {
          this.statisticsData = res.data;
        }
      });
    },
    initAccount() {
      let params = {};
      countHomeAccount(params).then((res) => {
        if (res.status == 200) {
          this.accountData = res.data;
        }
      });
    },
    initArticle() {
      let params = {};
      countHomeArticle(params).then((res) => {
        if (res.status == 200) {
          this.articleData = res.data;
        }
      });
    },
    initCourse() {
      let params = {};
      countHomeCourse(params).then((res) => {
        if (res.status == 200) {
          this.courseData = res.data;
        }
      });
    },
    initLive() {
      let params = {};
      countHomeLive(params).then((res) => {
        if (res.status == 200) {
          this.liveData = res.data;
        }
      });
    },
    initLine() {
      this.$nextTick(() => {
        this.myChart = echarts.init(
          document.getElementById("linesChart"),
          null,
          { renderer: "svg" }
        );
        let params = {
          dateType: this.unit,
          endTime: this.date[1],
          startTime: this.date[0],
        };
        this.lineOptions.xAxis.data = [];
        this.lineOptions.series[0].data = [];
        getUserUvByDateType(params).then((res) => {
          if (res.status == 200) {
            this.lineOptions.xAxis.name = `日期（${this.textFn(
              res.data[0].dateType
            )}）`;
            res.data.forEach((d) => {
              this.lineOptions.xAxis.data.push(d.dateTime);
              this.lineOptions.series[0].data.push(d.count);
            });
            this.myChart.setOption(this.lineOptions);
          }
        });
      });
    },
    initLine1() {
      this.$nextTick(() => {
        this.myChart1 = echarts.init(
          document.getElementById("linesChart1"),
          null,
          { renderer: "svg" }
        );
        let params = {
          dateType: this.unit1,
          endTime: this.date1[1],
          startTime: this.date1[0],
        };
        this.lineOptions1.xAxis.data = [];
        this.lineOptions1.series[0].data = [];
        userRegisterData(params).then((res) => {
          if (res.status == 200) {
            this.lineOptions1.xAxis.name = `日期（${this.textFn(
              res.data[0].dateType
            )}）`;
            res.data.forEach((d) => {
              this.lineOptions1.xAxis.data.push(d.dateTime);
              this.lineOptions1.series[0].data.push(d.count);
            });
            this.myChart1.setOption(this.lineOptions1);
          }
        });
      });
    },
    textFn(val) {
      let cur;
      switch (val) {
        case 1:
          cur = "天";
          break;
        case 2:
          cur = "周";
          break;
        case 3:
          cur = "月";
          break;
        case 4:
          cur = "季度";
          break;
      }
      return cur;
    },
  },
};
</script>

<style scoped lang="scss">
.dash-board {
  .yxd_box {
    padding: 28px 30px 36px 30px;
    background: #fff;
    margin-top: 24px;
    margin-bottom: 24px;
    .yxd-3 {
      height: 170px;
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      .box {
        width: 28%;
        padding: 14px 25px;
        background: #fff;
        box-shadow: 0 0 5px #e9e9e9;
        h3 {
          font-size: 16px;
          padding-left: 10px;
        }
        ul {
          margin-top: 25px;
          display: flex;
          justify-content: space-between;
          li {
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .tl {
              opacity: 1;
              font-size: 14px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.65);
              line-height: 22px;
            }
            .bold {
              opacity: 1;
              font-size: 36px;
              font-family: HelveticaNeue, HelveticaNeue-Medium;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              line-height: 38px;
            }
          }
          .first::after {
            position: absolute;
            content: "";
            width: 1px;
            height: 46px;
            top: 6px;
            right: 0px;
            background: #e9e9e9;
          }
        }
      }
    }
  }

  .el-row {
    padding: 14px 25px;
    background: #fff;
    height: 170px;
    margin-bottom: 24px;
    ul {
      margin-top: 25px;
      display: flex;
      justify-content: space-between;
      li {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
        }
        .bold {
          opacity: 1;
          font-size: 36px;
          font-family: HelveticaNeue, HelveticaNeue-Medium;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 38px;
        }
      }
      li::after {
        position: absolute;
        content: "";
        right: 0px;
        width: 1px;
        height: 46px;
        opacity: 1;
        background: #e9e9e9;
      }
      li:last-child::after {
        width: 0px;
      }
    }
  }
  .statistics {
    padding: 20px;
    background: #fff;
    margin-top: 24px;
    margin-bottom: 24px;
    .title {
      height: 24px;
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
    }
    .top {
      display: flex;
      justify-content: space-between;
    }
    .right {
      display: flex;
      justify-content: space-between;
      .item {
        margin-left: 20px;
        span {
          margin-right: 20px;
        }
      }
    }
  }
}
</style>
