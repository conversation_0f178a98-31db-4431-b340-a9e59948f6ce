<template>
  <div class="yxd_distributors">
    <distributors-index v-if="isIndex"></distributors-index>
  </div>
</template>

<script>
import distributorsIndex from "@/components/Distributors/index";
export default {
  name: "distributors",
  components: {
    distributorsIndex,
  },
  data() {
    return {
      isIndex: true,
      currentData: {},
      custom: {},
      type: "",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.yxd_distributors {
}
</style>
