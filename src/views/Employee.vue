<template>
  <div class="yxd_employee">
    <div class="container-employee">
      <el-tabs v-model="activeName">
        <el-tab-pane label="概览" name="first">
          <overview :type="activeName"></overview>
        </el-tab-pane>
        <el-tab-pane label="明细" name="second">
          <detail :type="activeName"></detail>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import overview from "@/components/Employee/overview";
import detail from "@/components/Employee/detail";
export default {
  name: "employee",
  components: {
    overview,
    detail,
  },
  data() {
    return {
      activeName: "first",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.yxd_employee {
  .container-employee {
    overflow: auto;
    ::v-deep .el-tabs {
      .el-tabs__header {
        margin: 0;
        background-color: #fff;
        padding-top: 5px;
        padding-left: 30px;
        padding-right: 30px;
        border-bottom: 2px solid #d9d9d9;
        .el-tabs__nav-wrap::after {
          height: 0px !important;
        }
      }
    }
  }
}
</style>
