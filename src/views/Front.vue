<template>
  <div class="yxd_front">
    <front-index v-if="isIndex" @next="isIndex = false"></front-index>
    <front-edit v-else @before="isIndex = true"></front-edit>
  </div>
</template>

<script>
import frontIndex from "@/components/Front/index";
import frontEdit from "@/components/Front/edit";
export default {
  name: "front",
  components: {
    frontIndex,
    frontEdit,
  },
  data() {
    return {
      isIndex: true,
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.yxd_front {
}
</style>
