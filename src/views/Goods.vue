<template>
  <div class="yxd_goods">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">商品名称</span>
            <el-input v-model="title" placeholder="请输入商品名称"></el-input>
          </div>
          <!-- <div class="col">
          <span class="tl">创建人</span>
          <el-select v-model="createUserId" placeholder="请选择">
            <el-option
              v-for="item in classGroup"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div> -->
          <div class="col">
            <span class="tl">创建时间</span>
            <el-date-picker
              v-model="lineTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="init()">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <div>
          <!-- <el-button v-if="true" @click="operation_change({operation: domConfig.soltButtons[0]})" type="primary" icon="el-icon-plus">表单添加</el-button> -->
          <!-- <el-button v-if="true" @click="setPower">批量设置权限</el-button>-->
          <el-button v-if="true" @click="setClass">批量设置分类</el-button>
          <!-- <el-button v-if="btnList.batch_form_audit_on" @click="lineBatch">批量审核</el-button>
          <el-button v-if="btnList.batch_form_audit_off" @click="offLineBatch">批量去审</el-button> -->
          <!-- <el-button v-if="true" @click="deleteClass" icon="el-icon-close">批量删除</el-button> -->
        </div>
      </div>
      <div class="table">
        <el-table
          ref="multipleTable"
          border
          :data="tableData"
          :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column prop="id" label="商品ID" width="120" align="center">
          </el-table-column>
          <el-table-column
            prop="title"
            label="商品名称"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="goodsAttributes"
            label="商品属性"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="paymentAmount"
            label="梅花数量（个）"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="stock"
            label="库存数量（个）"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            min-width="120"
            align="center"
          >
          </el-table-column>

          <el-table-column
            prop="status"
            label="审核状态"
            width="120"
            align="center"
          >
            <template #default="scope">
              <label
                class="status-label"
                :style="{
                  background:
                    scope.row.status == '1'
                      ? 'rgb(64, 162, 63)'
                      : 'rgb(167, 173, 189)',
                }"
              ></label>
              <span
                :class="{ start: scope.row.status == '1' }"
                class="default"
              ></span>
              <span>{{
                scope.row.status === "0" ? "待审核" : "审核通过"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="300"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                size="mini"
                type="danger"
                plain
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <classify-dialog-goods
        ref="classifyRef"
        :choseData="choseData"
      ></classify-dialog-goods>
      <!-- <el-dialog title="批量设置分类" v-model="editVisible" width="70%">
        <div class="edit-head">
          <div class="left">
            <span>已选分类</span>
            <div class="edit-tag">
              <el-tag v-for="tag in tags" :key="tag.tagName" closable size="mini" :type="tag.type">
                {{ tag.tagName }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="edit-main">
          <div class="title">
            <span>可选分类</span>
            <div class="group"></div>
          </div>
          <div class="title" v-for="(item, index) in classGroup" :key="index">
            <div class="group">
              <div class="btn" @click="item.checked = !item.checked" :class="{ checked: item.checked }">
                {{ item.tagName }}
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog> -->
      <el-dialog title="批量审核" v-model="onlineVisible" width="70%">
        <h3>确定审核这些内容吗？</h3>
        <el-tag
          class="delete-box"
          v-for="(item, index) in choseData"
          :key="index"
        >
          {{ item.formName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveOnlineEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量去审" v-model="offLineVisible" width="70%">
        <h3>确定去审这些内容吗？</h3>
        <el-tag
          class="delete-box"
          v-for="(item, index) in choseData"
          :key="index"
        >
          {{ item.formName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveOffEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag
          class="delete-box"
          v-for="(item, index) in offLineData"
          :key="index"
        >
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="saveDeleteEdit" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span
          >是否{{ lineText == "审核提示" ? "审核" : "去审" }}
          <el-tag>{{ currentChose.formName }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="lineSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <span
          >是否删除 <el-tag>{{ currentChose.title }}</el-tag></span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="deleteSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import { goodsList,goodsDelete } from "@/request/service.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import formMixin from "@/mixins/form-mixin";
import tableMixins from "@/mixins/table";
import controlDialog from "@/components/controlDialog.vue";
import classifyDialogGoods from "@/components/classifyDialogGoods.vue";
export default {
  name: "yxd_goods",
  mixins: [initModule, initScroll, formMixin, tableMixins],
  components: {
    controlDialog,
    classifyDialogGoods
  },
  data() {
    return {
      title: "",
      lineTime: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      classGroup: [],
      // powerVisible: true,
      editVisible: false,
      settingVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      deleteVisible: false,
      lineText: "上线提示",
      currentChose: {},
      RoleList: [],
      choseData: [],
      ClassifyData: [],
      radio: 1,
      selectPower: "",
      // checkList: [],
      selectvalue: [],
      ClassifyValue: [],
      createUserId: "",
    };
  },
  mounted() {
    this.init();
    // this.getPowerData()
    // this.getClassifyData()
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
    projectId() {
      return this.$store.state.projectInfo.id;
    },
  },
  methods: {
    //  点击权限多选框选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.selectvalue = [];
      nodesObj.forEach((item) => this.selectvalue.push(item.data.id));
    },
    //  点击分类多选框选项
    ClassifyChange() {
      let nodesObj = this.$refs["Classify"].getCheckedNodes();
      this.ClassifyValue = [];
      nodesObj.forEach((item) => this.ClassifyValue.push(item.data.id));
    },
    init() {
      this.tableData = [];
      let createStartTime;
      let createEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        createStartTime = this.lineTime[0];
        createEndTime = this.lineTime[1];
      }
      let params = {
        title: this.title,
        createStartTime: createStartTime,
        createEndTime: createEndTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
      };

      goodsList(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds: [],
        tagType: "guider",
      };
      listTag(params).then((res) => {
        this.ClassifyData = this.removeClassifyChild(res.data);
      });
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    // 清除空的子类
    removeClassifyChild(data) {
      return data.filter((item) => {
        if (item.childTags.length === 0) {
          delete item.childTags;
          return item;
        } else {
          return this.removeClassifyChild(item.childTags);
        }
      });
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog("guider");
      this.$refs.classifyRef.gitClassList();
      // this.initTag();
      // this.editVisible = true;
    },


    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.title = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {};
      obj.stopPropagation = () => {};
      try {
        this.$refs.cascader.clearValue(obj);
        this.$refs.Classify.clearValue(obj);
      } catch (err) {
        this.$refs.cascader.handleClear(obj);
        this.$refs.Classify.handleClear(obj);
      }
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },

    // 单条删除
    deleteSave() {
      let params = {
        id: this.currentChose.id,
      };
      goodsDelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.init();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_goods {
  //  ::v-deep {
  //     .el-cascader-panel {
  //       background-color: #ff0;
  //     }
  //   }
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }

  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .operate {
    .operate-right {
      flex: 1;
      .article-total {
        float: right;
        border-radius: 4px;
      }
    }
  }

  .table {
    .status-label {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 6px;
      display: inline-block;
      position: relative;
      top: 1px;
    }
  }

  .power-head {
    table {
      .line {
        height: 45px;
      }

      tr {
        padding: 30px 0;

        .title {
          width: 70px;
          // font-size: 20px;
        }
      }
    }
  }

  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      width: 58.333%;
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }

  .edit-main {
    display: flex;
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }

  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
</style>
<style lang="scss">
.el-input--suffix {
  width: 135px !important;
}

.el-date-editor--datetimerange {
  width: 260px !important;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
}
</style>
