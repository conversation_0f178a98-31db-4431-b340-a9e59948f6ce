<template>
  <div class="about">
    <v-header />
    <v-sidebar />
    <div class="content-box" :class="{ 'content-collapse': collapse }">
      <!-- <v-tags></v-tags> -->
      <div class="content">
        <router-view v-slot="{ Component }">
          <transition name="move" mode="out-in">
            <keep-alive :include="tagsList">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
        <iframe
          :src="path"
          style="width: 100%; height: 100%"
          v-if="path"
          frameborder="0"
        ></iframe>
        <!-- <div id="qiankun_container"></div> -->
        <div class="content-footer">
          <!-- <span @click="goMedsci">- Powerd by MedSci - </span> -->
        </div>
        <!-- <el-backtop target=".content"></el-backtop> -->
      </div>
    </div>
  </div>
</template>
<script>
import vHeader from "../components/Header";
import vSidebar from "../components/Sidebar";
import vTags from "../components/Tags.vue";
import { start } from "qiankun";
export default {
  components: {
    vHeader,
    vSidebar,
    vTags,
  },
  data() {
    return {
      path: "",
    };
  },
  watch: {
    $route(val) {
      if (location.href.indexOf("/tob/") == -1) {
        this.path = "";
      } else {
        this.path = location.origin + "/pifu/" + location.hash;
      }
    },
  },
  computed: {
    tagsList() {
      return this.$store.state.tagsList.map((item) => item.name);
    },
    collapse() {
      return this.$store.state.collapse;
    },
  },
  mounted() {
    if (location.href.indexOf("/tob/") == -1) {
      this.path = "";
    } else {
      this.path = location.origin + "/pifu/" + location.hash;
    }

    //if (
    //  window.location.hash.includes('/tob/') ||
    //  window.location.hash.includes('/home') ||
    //  window.location.hash.includes('/add_user')
    //) {
    //  start({
    //    sandbox: { strictStyleIsolation: false }
    //  })
    //}

    start({
      sandbox: { strictStyleIsolation: false },
    });
  },
  methods: {
    goMedsci() {
      window.open("https://www.medsci.cn/");
    },
  },
};
</script>
<style lang="scss" scoped>
.content-box {
  .content {
    position: relative;
    .content-footer {
      margin-top: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      span {
        color: #7f7f7f;
        cursor: pointer;
      }
    }
  }
}
#qiankun_container {
  height: 0%;
}
</style>
