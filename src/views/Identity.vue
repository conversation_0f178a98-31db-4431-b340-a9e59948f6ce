<template>
  <div class="yxd_identity">
    <el-tabs v-model="activeName" type="card" class="demo-tabs">
    <el-tab-pane label="医生用户" name="first" v-if="btnList.identity_doctor">
       <doctor></doctor>
    </el-tab-pane>
    <el-tab-pane label="患者用户" name="second" v-if="btnList.identity_patient">
      <patient></patient>
    </el-tab-pane>
  </el-tabs>
    
  </div>
</template>
<script>
import doctor from "@/components/Identity/doctor";
import patient from "@/components/Identity/patient";
import initModule from "@/mixins/initModule.js";
export default {
  data(){
    return{
        activeName:"first"
    }
  },
  mixins:[initModule],
  components:{
    patient,
    doctor
  },
  mounted(){
    if(this.btnList.identity_doctor&&this.btnList.identity_patient){
      this.activeName="first"
    }
    if(this.btnList.identity_doctor&&!this.btnList.identity_patient){
      this.activeName="first"
    }
    if(!this.btnList.identity_doctor&&this.btnList.identity_patient){
      this.activeName="second"
    }
  }
}
</script>
<style lang="scss" scoped>
    .yxd_identity{
     ::v-deep .el-tabs__header{
       margin: 0;
       background: #fff;
      }
    }
</style>
