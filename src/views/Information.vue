<template>
  <div class="yxd_information">
    <information-index @go="goEdit" v-if="isIndex"></information-index>
    <information-create
      :edit="currentId"
      @back="isIndex = true"
      v-else
    ></information-create>
  </div>
</template>

<script>
import informationIndex from "@/components/Information/index";
import informationCreate from "@/components/Information/create";
export default {
  name: "information",
  components: {
    informationIndex,
    informationCreate,
  },
  data() {
    return {
      isIndex: true,
      currentId: "",
    };
  },
  mounted() {},
  methods: {
    goEdit(obj) {
      this.isIndex = false;
      this.currentId = obj.id;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_information {
}
</style>
