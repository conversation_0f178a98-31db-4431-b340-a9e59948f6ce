<template>
  <div class="yxd_leave">
    <leave-index @edit="editFn" v-if="isIndex"></leave-index>
    <leave-reply :info="info" @go="isIndex = true" v-else></leave-reply>
  </div>
</template>

<script>
import leaveIndex from "@/components/Leave/index";
import leaveReply from "@/components/Leave/reply";
export default {
  name: "leave",
  components: {
    leaveIndex,
    leaveReply,
  },
  data() {
    return {
      isIndex: true,
      info: {},
    };
  },
  mounted() {},
  methods: {
    editFn(val) {
      this.isIndex = false;
      this.info = val;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_leave {
}
</style>
