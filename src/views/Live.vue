<template>
  <div class="yxd_live">
    <div id="container" class="container" :class="{ isScroll: isScroll }" ref="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入直播标题"></el-input>
          </div>
          <div class="col">
            <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开播开始时间"
              end-placeholder="开播结束时间" value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <!-- 筛选权限 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择权限"
            :options="RoleList"
             ref="cascader"
            collapse-tags	
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles',
            multiple: true
            }"
            clearable>
          </el-cascader>
          </div>
          <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择分类"
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button v-if="btnList.batchSetRole" @click="setPower">批量设置权限</el-button>
        <el-button v-if="btnList.batchSetTag" @click="setClass">批量设置分类</el-button>
        <el-button v-if="btnList.batch_start_btn" @click="lineBatch">批量上线</el-button>
        <el-button v-if="btnList.batch_end_btn" @click="offLineBatch">批量下线</el-button>
        <el-button v-if="btnList.batch_remove_btn" @click="deleteClass" icon="el-icon-close">批量删除</el-button>
        <el-button  v-if="btnList.export_live_info" @click="handleDown('', '','all')" >全部导出</el-button>
        <el-button
          @click="filterList()"
          :type="btnStatus?'primary':''"
          >未设置分类</el-button
        >
      </div>
      <div class="table">
        <el-table ref="multipleTable" border :data="tableData" tooltip-effect="dark" style="width: 100%"  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column sortable prop="id" label="ID" width="120" align="center">
          </el-table-column>
          <el-table-column prop="name" label="直播观看地址" align="center" min-width="120">
            <template #default="scope">
              <a :href="scope.row.playUrl" target="_blank">{{
                  scope.row.name
              }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="playUrl" label="播放地址" min-width="70" align="center">
            <template #default="scope">
              <el-tooltip popper-class="topClass" class="item" effect="dark" :content="scope.row.playUrl"
                placement="top">
                <el-button type="text" @click="copyText('playUrl', scope.row)">复制</el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="pushUrl" label="流地址" min-width="70" align="center">
            <template #default="scope">
              <el-tooltip popper-class="topClass" class="item" effect="dark" :content="scope.row.pushUrl"
                placement="top">
                <el-button type="text" @click="copyText('pushUrl', scope.row)">复制</el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="liveStartTime" label="开播时间" sortable min-width="150" align="center">
          </el-table-column>
          <el-table-column prop="statusName" label="直播状态" width="120" align="center">
          </el-table-column>
          <el-table-column prop="hits" label="PV/UV/并发量" width="120" align="center">
          <template #default="scope">
               {{scope.row.hits}}/{{scope.row.userHits}}/{{scope.row.maxOnlineHits}}
             </template>
          </el-table-column>
          
          <!-- 新增字段 -->
          <!-- <el-table-column prop="userHits" label="点踩次数" width="120" align="center">
          </el-table-column> -->
          <!-- 新增字段 -->
          <el-table-column prop="perCapitaDuration" label="人均时长/互动次数" width="120" align="center">
          <template #default="scope">
               {{scope.row.perCapitaDuration}}/{{scope.row.interactiveCount}}
          </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="220" fixed="right">
            <template #default="scope">
              <el-button v-if="btnList.start_btn" size="mini" @click="handleUp(scope.$index, scope.row)">{{
                  !scope.row.isOffLine ? "下线" : "上线"
              }}</el-button>
              <el-button v-if="btnList.remove_btn" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)" :disabled="!scope.row.isOffLine">删除</el-button>
              <el-button v-if="btnList.export_live_info" size="mini" @click="handleDown(scope.$index, scope.row)">导出</el-button>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="maxOnlineHits"
            label="观看人数"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="perCapitaDuration"
            label="人均观看时长（分钟）"
            width="120"
            align="center"
          >
          </el-table-column> -->
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
      <!-- 批量上线 -->
     <el-dialog title="批量上线" v-model="onlineVisible" width="70%">
        <h3>确定上线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.name }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量下线 -->
      <el-dialog title="批量下线" v-model="offLineVisible" width="70%">
        <h3>确定下线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.name }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 上/下线 -->
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span>是否{{ lineText == "上线提示" ? "上线" : "下线" }}
          <el-tag>{{ currentChose.name }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="lineSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量删除 -->
      <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.name }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 删除单条 -->
      <el-dialog  title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.name }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量设置权限弹框 -->
      <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog> 
      <!-- 批量设置分类弹框 -->
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import { liveQuery, getRoleList, setDelePower,query,listTag,batchLineModule,deleteBeOfflineLive} from "@/request/service.js";
import { exportUserInfo,exportLive } from "@/request/download.js";
import { ElMessage } from "element-plus";
import  controlDialog  from '../components/controlDialog.vue'
import  classifyDialog  from '../components/classifyDialog.vue'
import filterList from "@/mixins/filterList.js";
export default {
  name: "live",
  mixins: [initModule, initScroll, initAccountTypeList,filterList],
  components:{
    controlDialog,
    classifyDialog
  },
  data() {
    return {
      editVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      settingVisible:false,
      deleteVisible:false,
      title: "",
      time: "",
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      // powerVisible: false,
      radio: 1,
      selectPower: '',
      lineText:'',
      checkList: [],
      selectvalue: [],
      RoleList: [],
      choseData: [],
      ClassifyData:[],
      ClassifyValue: [],
      offLineData:"",
      accountTypeValue: '',
      refesh:false
    };
  },
  mounted() {
    this.init();
    this.getClassifyData()
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val){
      if(val===0||val===1){
        this.getPowerData(val)
      }else if(val===""){
        this.RoleList = []
      }
    },
    init() {
      if(this.choseData.length>0){
            this.refesh=true
          }
      this.tableData=[]
      let liveEndTime = "";
      let liveStartTime = "";
      if (this.time && this.time.length > 0) {
        liveStartTime = this.time[0];
        liveEndTime = this.time[1];
      }
      let params = {
        liveEndTime: liveEndTime,
        liveStartTime: liveStartTime,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        searchTitle: this.title,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      liveQuery(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.filterData = this.tableData;
          this.btnStatus=false
          this.initScroll();
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'live_info',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
   getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    // 批量设置权限
    setPower(){
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.$refs.controlRef.openDialog('live_info')
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据！')
        return
      }
      this.$refs.classifyRef.openDialog('live_info')
      this.$refs.classifyRef.gitClassList()
    },
    // 批量上线
     lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.onlineVisible = true
    },
    // 批量下线
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.offLineVisible = true
    },
    // 批量删除
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData=this.choseData.filter(item=>item.isOffLine == true)
      this.settingVisible = true;
    },
       // 批量上线按钮
    saveOnlineEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"live_info"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
          ElMessage.success("批量上线成功");
          this.onlineVisible = false
        }
      })
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"live_info"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
           ElMessage.success("批量下线成功");
          this.offLineVisible = false
        }
      })
    },
    // 上/下线
    handleUp(index, row) {
      if (!row.isOffLine) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    // 上/下线按钮
     lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        operationType: this.lineText == "上线提示" ? 1 : 0,
        moduleType:'live_info',
        moduleIds: ids,
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "上线提示"? ElMessage.success("上线成功"):ElMessage.success("下线成功")
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    // 删除单条
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.time = "";
      this.title = "";
      this.selectvalue =[]
      this.ClassifyValue = []
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    copyText(type, row) {
      let Url; //每一行的某个值，如选中的当前行的url
      if (type == "pushUrl") {
        Url = row.pushUrl;
      } else {
        Url = row.playUrl;
      }
      var oInput = document.createElement("input"); //创建一个隐藏input（重要！）
      oInput.value = Url; //赋值
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象
      document.execCommand("Copy"); // 执行浏览器复制命令
      oInput.className = "oInput";
      oInput.style.display = "none";
    },
    handleDown(index, row,types) {
               if(types=="all"){
        // 全部导出
       exportLive(this.time[0],this.time[1]).then((res) => {
        if (res.size == 51) {
          ElMessage.warning("暂无数据导出");
          
        }else if(res.size==63){
          ElMessage.warning("上线时间不能为空");
        } else {
          let name = `直播数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        }
        })
      }else{
      exportUserInfo(row.id).then((res) => {

        if (res.size > 57) {
           let name = row.name+`数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
      
      });
        }
    },
        // 勾选框
    handleSelectionChange(selection){
          this.choseData=selection
    },
    //  点击权限多选框选项
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    }, 
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    // 删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
      };
      deleteBeOfflineLive(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
      // 单条删除
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        ids: ids,
      };
      deleteBeOfflineLive(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;  
          this.init();
        }
      });
    },

  },
};
</script>

<style scoped lang="scss">
.yxd_live {
  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }

  }

  

  .table {
    cursor: pointer;
  }

  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
 
    .left {
      width: 58.333%;
      display: flex;
      align-items: flex-start;
      
      
      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }

  }

  .edit-main {
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        width: 70px;
        font-size: 12px;
        color: #409eff;
        text-align: right;
        margin-right: 20px;
        font-weight: 600;
        margin-top: 3px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}

.power-head {
  table {
    .line {
      height: 45px;
    }

    tr {
      padding: 30px 0;

      .title {
        width: 70px;
        // font-size: 20px;
      }

    }
  }
}
</style>
