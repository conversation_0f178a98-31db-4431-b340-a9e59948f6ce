<template>
  <div class="login-wrap">
    <background></background>
    <div class="ms-login">
      <div class="ms-title">{{ projectInfo.name }}</div>
      <el-form :model="param" :rules="rules" ref="login" label-width="0px" class="ms-content">
        <el-form-item prop="mobile">
          <el-input size="large" v-model="param.mobile" placeholder="手机号码">
            <template #prepend>
              <el-button icon="el-icon-phone"></el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="password" prop="code">
          <el-input placeholder="验证码" size="large" v-model="param.code" @keyup.enter="submitForm">
            <template #prepend>
              <el-button icon="el-icon-message"></el-button>
            </template>
          </el-input>
          <el-button size="large" @click="getCode" type="primary" :disabled="sendCoding">{{
              sendCoding ? count + "s后重新发送" : "获取验证码"
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="checked">自动登录</el-checkbox>
        </el-form-item>
        <div class="login-btn">
          <el-button size="large" :disabled="disable" type="primary" @click="submitForm">登录</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import background from "@/components/Background.vue";
import { validPhone } from "@/utils/validate.js";
import { ElMessage } from "element-plus";
import {
  autoLogin,
  login,
  sendSms,
  getYxdProjectDetailByOrigin,
  getYxdProjectDetail,
} from "@/request/service.js";
export default {
  components: {
    background,
  },
  data() {
    return {
      isChoseAuto: false,
      yxd_Info: {},
      projectInfo: {},
      count: 60,
      sendCoding: false,
      checked: false,
      btnStatus: "",
      disable:true,
      param: {
        mobile: "",
        code: "",
      },
      rules: {
        mobile: [
          { required: true, message: "请输入手机号码", trigger: "change" },
        ],
        code: [
          { required: true, message: "请输入短信验证码", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.$store.commit("clearTags");
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      getYxdProjectDetailByOrigin().then((res) => {
        let params = {
          id: res.data.id,
          projectCode: res.data.projectCode,
        };
        getYxdProjectDetail(params).then((item) => {
          if (item.status == 200) {
            this.projectInfo = item.data;
            this.$store.commit("setProjectInfo", item.data);
            this.initAuto();
          }
        });
      });
    },
    initAuto() {
      this.isChoseAuto = localStorage.getItem("isChoseAuto");
      if (this.isChoseAuto) {
        this.checked = true;
        autoLogin().then((res) => {
          if (res.status == 200) {
            this.$message.success("登录成功！");
            this.$store.commit("setBackInfo", res.data);
            this.$router.push("/");
          } else {
            localStorage.removeItem("isChoseAuto");
          }
        });
      } else {
        this.checked = false;
      }
    },
    submitForm() {
      this.$refs.login.validate((valid) => {
        if (valid) {
          login(this.param).then((res) => {
            if (res.status == 200) {
              this.$message.success("登录成功！");
              if (this.checked) {
                localStorage.setItem(
                  "isChoseAuto",
                  JSON.stringify(res.data.token.accessToken)
                );
              }
              const auto = localStorage.getItem('isChoseAuto')
              const autoData = JSON.parse(auto)
              this.$store.commit("setBackInfo", res.data);
              this.$router.push("/");
            }
          });
        } else {
          this.$message.error("请输入手机号码和短信验证码！");
          return false;
        }
      });
    },
    getCode() {
      if (validPhone(this.param.mobile)) {
        let params = {
          mobile: this.param.mobile,
        };
        sendSms(params).then(() => {
          this.sendCoding = true;
          let that = this;
          let timer = setInterval(function () {
            that.count--;
            if (that.count == 0) {
              clearInterval(timer);
              that.sendCoding = false;
              that.count = 60;
            }
          }, 1000);
        });
        this.disable = false
      } else {
        ElMessage.error("请输入正确的手机号码！");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
}

.ms-login {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 400px;
  background-clip: padding-box;
  padding: 35px 35px 15px 35px;
  border-radius: 5px;
  background: #fff;
  border: 1px solid #eaeaea;
  box-shadow: 0 0 25px #cac6c6;
  transform: translateX(-50%) translateY(-50%);

  .ms-title {
    width: 100%;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
    color: #000;
    border-bottom: 1px solid #ddd;
  }

  .ms-content {
    padding: 30px 30px;

    .password {
      .el-form-item__content {
        display: flex;
        justify-content: space-between;

        .el-input-group {
          width: 64%;
        }

        .el-button--primary {
          width: 30%;
          margin-left: 6%;
          padding: 0;
        }
      }
    }

    .login-btn {
      text-align: center;
    }

    .login-btn button {
      width: 100%;
      height: 36px;
      margin-bottom: 10px;
    }
  }
}
</style>
