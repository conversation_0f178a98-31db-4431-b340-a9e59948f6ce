<template>
  <div class="yxd_meet">
    <div class="container">
      <div v-if="showIndex" class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入会议标题"></el-input>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div v-if="showIndex">
        <div class="table">
          <el-table
            border
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column
              prop="meetingName"
              label="会议名称"
              width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="lecturer"
              label="主讲人"
              width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="startDate"
              label="开始日期"
              sortable
              width="100"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="开始时间"
              sortable
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="endDate"
              label="结束日期"
              sortable
              min-width="100"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="endTime"
              label="结束时间"
              sortable
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="attachmentsUpdateTime"
              label="附件更新时间"
              sortable
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="verifyStatus"
              label="核销状态"
              min-width="100"
              align="center"
            >
              <template #default="scope">
                <span
                  :class="{ start: scope.row.verifyStatus == 4 }"
                  class="default"
                ></span>
                <span>{{ statusFn(scope.row.verifyStatus) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="passedTime"
              label="核销成功时间"
              sortable
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="220"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  size="mini"
                  :disabled="!scope.row.hasAttachments"
                  @click="handleRead(scope.$index, scope.row)"
                  >查看附件</el-button
                >
                <el-button
                  v-if="scope.row.hasAttachments && scope.row.verifyStatus != 2"
                  size="mini"
                  :disabled="scope.row.verifyStatus == 4"
                  @click="handleDelete(scope.$index, scope.row)"
                  >核销通过</el-button
                >
                <el-button
                  v-if="
                    scope.row.hasAttachments && !scope.row.verifyStatus == 4
                  "
                  size="mini"
                  @click="handleStop(scope.$index, scope.row)"
                  >核销不通过</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <div v-else>
        <div class="box">
          <h3>会议现场照片</h3>
          <ul>
            <li v-for="(item, index) in pathData[0].pathList" :key="index">
              <el-image
                style="width: 100px; height: 100px"
                :src="item.url"
                :preview-src-list="item.srcList"
              >
              </el-image>
            </li>
          </ul>
        </div>
        <div class="box">
          <h3>讲者单独近身讲课中照片</h3>
          <ul>
            <li v-for="(item, index) in pathData[1].pathList" :key="index">
              <el-image
                style="width: 100px; height: 100px"
                :src="item.url"
                :preview-src-list="item.srcList"
              >
              </el-image>
            </li>
          </ul>
        </div>
        <div class="box">
          <h3>会议执行情况</h3>
          <ul>
            <li v-for="(item, index) in pathData[2].pathList" :key="index">
              <a
                v-if="item.type == 'excel'"
                :href="item.filePath"
                target="_blank"
                >{{ item.fileName }}</a
              >
              <el-image
                v-if="item.type == 'img'"
                style="width: 100px; height: 100px"
                :src="item.url"
                :preview-src-list="item.srcList"
              >
              </el-image>
            </li>
          </ul>
        </div>
        <div class="btn">
          <el-button @click="showIndex = true">取消</el-button>
          <el-button
            v-if="curObject.verifyStatus == 3"
            @click="open"
            type="primary"
            >核销</el-button
          >
        </div>
      </div>
    </div>
    <el-dialog
      title="核销操作"
      v-model="centerDialogVisible"
      width="30%"
      center
    >
      <span>若附件审核无误，请点击核销通过。反之，则点核销不通过</span>
      <template #footer>
        <span>
          <el-button @click="dialogFn('cancel')" size="mini">不通过</el-button>
          <el-button type="primary" @click="dialogFn('confirm')" size="mini"
            >通过</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import {
  getMeetingList,
  getMeetingAttachments,
  verifyMeeting,
} from "@/request/service.js";
export default {
  name: "meet",
  mixins: [initModule],
  data() {
    return {
      title: "",
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      showIndex: true,
      pathData: [],
      curObject: {},
      centerDialogVisible: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        meetingTitle: this.title,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        projectId: this.$store.state.projectInfo.id,
      };
      getMeetingList(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
        }
      });
    },
    statusFn(value) {
      let text = "";
      switch (value) {
        case 1: {
          text = "待审核";
          break;
        }
        case 2: {
          text = "未通过";
          break;
        }
        case 3: {
          text = "审核中";
          break;
        }
        case 4: {
          text = "已通过";
          break;
        }
      }
      return text;
    },
    reset() {
      this.title = "";
    },
    open() {
      this.centerDialogVisible = true;
    },
    dialogFn(type) {
      if (type == "cancel") {
        this.verifyMeetingFn(false, false, this.curObject);
      } else if (type == "confirm") {
        this.verifyMeetingFn(true, false, this.curObject);
      }
    },
    handleRead(index, row) {
      this.curObject = row;
      let params = {
        meetingId: row.meetingId,
        projectId: this.$store.state.projectInfo.id,
      };
      getMeetingAttachments(params).then((res) => {
        if (res.status == 200) {
          res.data.forEach((d) => {
            let arr = [];
            d.pathList.forEach((t) => {
              if (d.type == 0 || d.type == 1) {
                arr.push({
                  url: t.filePath,
                  srcList: [t.filePath],
                });
              } else {
                let flag =
                  t.filePath.includes("jpg") ||
                  t.filePath.includes("jpeg") ||
                  t.filePath.includes("png");
                if (flag) {
                  arr.push({
                    url: t.filePath,
                    srcList: [t.filePath],
                    type: "img",
                  });
                } else {
                  arr.push({
                    fileName: t.fileName,
                    filePath: t.filePath,
                    type: "excel",
                  });
                }
              }
            });
            this.pathData.push({
              type: d.type,
              pathList: arr,
            });
          });
          this.showIndex = false;
        }
      });
    },
    verifyMeetingFn(type, meet, row) {
      let params = {
        id: row.id,
        meetingId: row.meetingId,
        projectId: this.$store.state.projectInfo.id,
        userName: row.meetingName,
        verifyResult: type,
      };
      verifyMeeting(params).then((res) => {
        this.centerDialogVisible = false;
        if (res.status == 200) {
          this.currentPage = 1;
          this.init();
          this.showIndex = !meet;
        }
      });
    },
    handleDelete(index, row) {
      let params = {
        id: row.id,
        meetingId: row.meetingId,
        projectId: this.$store.state.projectInfo.id,
        userName: row.meetingName,
        verifyResult: true,
      };
      verifyMeeting(params).then((res) => {
        if (res.status == 200) {
          this.currentPage = 1;
          this.init();
        }
      });
    },
    handleStop(index, row) {
      let params = {
        id: row.id,
        meetingId: row.meetingId,
        projectId: this.$store.state.projectInfo.id,
        userName: row.meetingName,
        verifyResult: false,
      };
      verifyMeeting(params).then((res) => {
        if (res.status == 200) {
          this.currentPage = 1;
          this.init();
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_meet {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }
  .table {
    margin-bottom: 30px;
    .default {
      border-radius: 50%;
      width: 10px;
      height: 10px;
      background: #7f7f7f;
      margin-right: 5px;
    }
    .start {
      border-radius: 50%;
      width: 10px;
      height: 10px;
      background: rgb(62, 201, 119);
      margin-right: 5px;
    }
  }
  .box {
    margin-top: 30px;
    ul {
      margin-top: 30px;
      display: flex;
      li {
        margin-right: 20px;
      }
    }
  }
  .btn {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }
}
</style>
