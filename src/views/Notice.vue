<template>
  <div class="yxd_notice">
    <notice-index @type="typeFn" @create="create" v-if="isIndex"></notice-index>
    <notice-create
      :info="noticeInfo"
      :type="typeValue"
      @close="close"
      v-else
    ></notice-create>
  </div>
</template>

<script>
import noticeIndex from "@/components/Notice/index";
import noticeCreate from "@/components/Notice/create";
export default {
  name: "notice",
  components: {
    noticeIndex,
    noticeCreate,
  },
  data() {
    return {
      isIndex: true,
      noticeInfo: {},
      typeValue: "",
    };
  },
  mounted() {},
  methods: {
    close() {
      this.isIndex = true;
    },
    create(obj) {
      this.isIndex = false;
      this.noticeInfo = obj;
    },
    typeFn(val) {
      this.typeValue = val;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_notice {
}
</style>
