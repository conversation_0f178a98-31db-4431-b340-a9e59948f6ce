<template>
  <div class="yxd_order">
    <div id="container" class="container" :class="{ isScroll: isScroll }" ref="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">商品名称</span>
            <el-input v-model="title" placeholder="请输入"></el-input>
          </div>
          <div class="col">
          <span class="tl">商品类型</span>
          <el-select v-model="goodsCategoryName" clearable placeholder="请选择">
            <el-option
              v-for="item in classGroup"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div>
          <div class="col">
            <span class="tl">兑换人姓名</span>
            <el-input v-model="uname" placeholder="请输入"></el-input>
          </div>
          <div class="col">
            <span class="tl">兑换人手机</span>
            <el-input v-model="mobile" placeholder="请输入"></el-input>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="init()">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="table">
        <el-table ref="multipleTable" border :data="tableData" :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column  prop="id" label="序号" width="120" align="center">
          </el-table-column>
          <el-table-column prop="goodsTitle" label="商品名称" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="goodsCategoryName" label="商品类型" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="goodsAttributes" label="商品属性" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="createdName" label="兑换人" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="receiverName" label="收件人" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="mobile" label="手机号码" min-width="120" align="left">
          </el-table-column>
          <el-table-column prop="expressAddress" label="收件地址" min-width="120" align="left">
          </el-table-column>
                    <el-table-column prop="status" label="订单状态" width="120" align="center">
            <template #default="scope">
              <label class="status-label" :style="{background: scope.row.status == '1' ?  'rgb(64, 162, 63)' : 'rgb(167, 173, 189)' }"></label>
              <span :class="{ start: scope.row.status == '1' }" class="default"></span>
              <span>{{ scope.row.status=='1'?'已发货':'待发货' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间"  width="200" align="center">
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 50,100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { ElMessage } from "element-plus";
import {  exChangeLog } from "@/request/service.js";
import { exportUserBehavior } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import formMixin from "@/mixins/form-mixin"
import tableMixins  from "@/mixins/table"
import  controlDialog  from '@/components/controlDialog.vue'
export default {
  name: "yxd_order",
  mixins: [initModule, initScroll,formMixin,tableMixins],
  components:{
    controlDialog,
  },
  data() {
    return {
      title: "",
      goodsCategoryName:"",
      uname:"",
      mobile:"",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      // powerVisible: true,
      editVisible: false,
      settingVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      dialogVisible: false,
      deleteVisible: false,
      lineText: "上线提示",
      currentChose: {},
      RoleList: [],
      choseData: [],
      ClassifyData:[],
      radio: 1,
      selectPower: '',
      // checkList: [],
      selectvalue: [],
      ClassifyValue: [],
      createUserId:'',
      classGroup:[
        {id:'生活服务',name:'生活服务'},
        {id:'图书',name:'图书'},
        {id:'系列课程',name:'系列课程'},
        {id:'软件模板',name:'软件模板'},
        {id:'虚拟券码',name:'虚拟券码'}
      ]
    };
  },
  mounted() {
    this.init();
    // this.getPowerData()
    // this.getClassifyData()
  },
  computed: {
    projectId() {
      return this.$store.state.projectInfo.id;
    },
  },
  methods: {
    //  点击权限多选框选项
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    }, 
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    init() {
      let params = {
        title: this.title,
        uname:this.uname,
        mobile: this.mobile,
        goodsCategoryName:this.goodsCategoryName,
        pageSize:this.pageSize,
        pageIndex:this.currentPage
      }
      exChangeLog(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll();
          // this.tableData.forEach((val) => {
          //   if (val.approvalStatus == 1) {
          //     val.approvalStatus = "已上线";
          //   } else {
          //     val.approvalStatus = "已下线";
          //   }
          //   val.gotoUrl =
          //     `https://` +
          //     window.location.host +
          //     `/news/detail/${val.encodeId}`;
          // });
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'guider',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
  getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    changeCheck(val) {
    },
    goTab(index,item) {
      this.$router.push("/goods/edit")
    },
    // 批量设置权限
    setPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }else{
       this.$refs.controlRef.openDialog('guider')
      }
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog('guider')
      this.$refs.classifyRef.gitClassList()
      // this.initTag();
      // this.editVisible = true;
    },
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData=this.choseData.filter(item=>item.status === 0)
      this.settingVisible = true;
    },
    // saveEdit() {
    //   let ids = [];
    //   let tagDtoList = [];
    //   this.tags.forEach((d) => {
    //     tagDtoList.push({
    //       tagId: d.tagId,
    //       tagName: d.tagName,
    //     });
    //   });
    //   this.choseData.forEach((val) => {
    //     ids.push(val.id);
    //   });
    //   let params = {
    //     type: 4,
    //     ids: ids,
    //     tagDtoList: tagDtoList,
    //   };
    //   batchEdit(params).then((res) => {
    //     if (res.status == 200) {
    //       this.init();
    //       this.editVisible = false;
    //     }
    //   });
    // },
     // 批量删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        guiderIds: ids,
      };
      formBatchDelete(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
    // 批量上线按钮
    saveOnlineEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:"PASS"
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量上线成功");
          this.init();
          this.onlineVisible = false;
        }
      });
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = [];
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:"WAITING"
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("批量下线成功");
          this.init();
          this.offLineVisible = false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.title = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.onlineVisible = true;
    },
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineVisible = true;
    },
    handleEdit(index, row) {
      this.$emit("go", row);
    },
    handleDown(index, row) {
      if (row.status == "PASS") {
        this.lineText = "去审提示";
      } else {
        this.lineText = "审核提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
    // 指南导出
    deriveData(index, row) {
      exportUserGuider(row.id).then((res) => {
        if (res.size > 57) {
          let name = row.title+`指南详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
        // if (res.status == 200) {
        //   const blob = res.data;
        //   const reader = new FileReader();
        //   reader.readAsDataURL(blob);
        //   reader.onload = (e) => {
        //     const a = document.createElement('a');
        //     a.download = `文件名称.zip`;
        //     // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; 		   filename=\"20181211191944.zip\"",
        //     a.href = e.target.result;
        //     document.body.appendChild(a);
        //     a.click();
        //     document.body.removeChild(a);
        //   };
        // }
      });
    },
    exportNoLike(index, row) {
      // this.deleteVisible = true;
      // this.currentChose = row.userHits;
    },
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    // 上线/下线
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        projectId: this.$store.state.projectInfo.id,
        ids: ids,
        status:this.lineText == "审核提示" ? 'PASS' : 'WAITING',
      };
      formBatchAudit(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "审核提示"? ElMessage.success("审核成功"):ElMessage.success("去审成功")
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    // 单条删除
    deleteSave() {
      let params = {
        projectId: this.$store.state.projectInfo.id,
        id: this.currentChose.id,
        formId:this.currentChose.formId
      };
      formBatchDelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;  
          this.init();
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_order{
//  ::v-deep {
//     .el-cascader-panel {
//       background-color: #ff0;
//     }
//   }
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }
  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }
  .operate {
    .operate-right{
       flex: 1;
      .article-total {
       float: right ;
       border-radius: 4px;
     }
    }
  }
  .table {
    .status-label{
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 6px;
      display: inline-block;
      position: relative;
      top: 1px;
    }
  }
  .power-head {
    table {
      .line {
        height: 45px;
      }
      tr {
        padding: 30px 0;
        .title {
          width: 70px;
          // font-size: 20px;
        }
      }
    }
  }
  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 58.333%;
      display: flex;
      align-items: center;
      span {
        white-space: nowrap;
        margin-right: 10px;
      }
      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;
        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }
  .edit-main {
    display: flex;
    margin-top: 30px;
    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;
      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }
      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }
        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
</style>
<style lang="scss">
.el-input--suffix {
  width: 135px !important;
}
.el-date-editor--datetimerange {
  width: 260px !important;
}
.el-checkbox-group {
  display: flex;
  flex-direction: column;
}
</style>