<template>
  <div class="yxd_setting">
    <setting-index v-if="isIndex" @go="goIndex" @edit="edit"></setting-index>
    <setting-create
      :type="currentType"
      :tableData="currentData"
      @back="isIndex = true"
      v-else
    ></setting-create>
  </div>
</template>

<script>
import settingIndex from "@/components/Setting/index";
import settingCreate from "@/components/Setting/create";
export default {
  name: "setting",
  components: {
    settingIndex,
    settingCreate,
  },
  data() {
    return {
      isIndex: true,
      currentType: "",
      currentData: {},
    };
  },
  mounted() {},
  methods: {
    goIndex(val) {
      this.currentType = val;
      this.isIndex = false;
    },
    edit(obj) {
      this.currentData = obj;
    },
  },
};
</script>
<style scoped lang="scss">
.yxd_setting {
}
</style>
