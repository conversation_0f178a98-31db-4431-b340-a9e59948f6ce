<template>
  <div class="yxd_survey">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
      v-if="!$route.query.type"
    >
      <div class="header"  id="operate1">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入标题"></el-input>
          </div>
          <!-- 创建人 -->
          <!-- <div class="col">
            <span class="tl">创建人</span>
            <el-select v-model="createdBy" filterable placeholder="请选择">
              <el-option
                v-for="item in listCreate"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div> -->
          <!-- 发布时间 -->
          <div class="col">
            <el-date-picker
              @change='changePublishTime'
              v-model="createTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="发布开始日期"
              end-placeholder="发布结束日期">
            </el-date-picker>
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
           <!-- 筛选权限 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择权限"
            :options="RoleList"
             ref="cascader"
            collapse-tags	
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
           <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择分类"
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>

      <div class="operate">
        <!-- <el-button @click="addSurvey">+ 调研添加</el-button> -->
        <el-button v-if="btnList.batchSetRole" @click="setPower">批量设置权限</el-button>
        <el-button v-if="btnList.batchSetTag" @click="setClass">批量设置分类</el-button>
        <el-button v-if="btnList.batch_start_btn" @click="lineBatch">批量上线</el-button>
        <el-button v-if="btnList.batch_end_btn" @click="offLineBatch">批量下线</el-button>
        <el-button v-if="btnList.batch_remove_btn" @click="deleteClass" icon="el-icon-close">批量删除</el-button>
        <el-button
          @click="filterList()"
          :type="btnStatus?'primary':''"
          >未设置分类</el-button
        >
      </div>
      <div class="table">
        <el-table border :data="tableData" tooltip-effect="dark" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column prop="id" label="ID" sortable></el-table-column>
          <el-table-column
            prop="templateName"
            label="标题"
            min-width="250"
            align="center"
          >
          </el-table-column>
          <el-table-column prop="createdName" label="创建人" width="150"></el-table-column>
          <el-table-column
            prop="publishedTime"
            label="发布时间"
            width="150"
            sortable
            clearable
            align="center"
          >
          </el-table-column>
          <el-table-column label="奖励"
          >
          <!-- 自定义列表头 -->
          <template  #header>
            <el-dropdown trigger="click"  @command="filterChange" size="medium">
              <span :class="['el-dropdown-link','bonus-style',bonus !== '' ? 'active' : '']">
                <span>奖励</span>
                <i class="el-icon-arrow-down"></i>
              </span>
              <template #dropdown>
                <el-dropdown-item
                  v-for="(item, index) in filterLists"
                  :key="index"
                  :command="item['value']"
                  :class="item['value'] === bonus ? 'active' : ''"
                >
                  {{ item['label'] }}
                </el-dropdown-item>
              </template>
            </el-dropdown>
          </template>
          <template #default="scope">
              <label class="status-label" :style="{background: scope.row.bonus == 0 ?  'rgb(167, 173, 189)': scope.row.bonus == 1 ? 'rgb(64, 162, 63)' : 'rgb(230, 162, 60)' } "></label>
              <span>{{
                scope.row.bonus == 0 ? "无" : (scope.row.bonus == 1 ? '积分' : '梅花')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label='PV/UV/已提交' min-width="120"  align="center">
            <template #default="scope">
              <div>
                <span>{{scope.row.allHits}}/</span>
                <router-link  :to="{path:'form-details',query:{id:scope.row.id,type:'UvRecord'}}">{{scope.row.userHits}}/</router-link>
                <router-link  :to="{path:'form-details',query:{id:scope.row.id,type:'Record'}}">{{scope.row.joinCount}}</router-link>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="approvalStatus"
            label="状态"
            width="70"
            align="center"
          > 
            <!-- <template slot="header" #header>
              <el-dropdown trigger="click"  @command="statusChange" size="medium">
                <span :class="['el-dropdown-link','bonus-style',approvalStatus !== '' ? 'active' : '']">
                  <span>状态</span>
                  <i class="el-icon-arrow-down"></i>
                </span>
                <template #dropdown>
                  <el-dropdown-item
                    v-for="(item, index) in statusList"
                    :key="index"
                    :command="item['value']"
                    :class="item['value'] === approvalStatus ? 'active' : ''"
                  >
                    {{ item['label'] }}
                  </el-dropdown-item>
                </template>
              </el-dropdown>
            </template> -->
            <template #default="scope">
              <!-- <label class="status-label" :style="{background: scope.row.approvalStatus == 0 ?  'rgb(167, 173, 189)':  'rgb(64, 162, 63)' } "></label> -->
              <span>{{
                scope.row.approvalStatus == 0 ? "已下线" : "已上线"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            min-width="280"
          >
            <template #default="scope">
              <!-- <el-button size="mini" plain @click="addSurvey(scope.row)">编辑</el-button> -->
              <el-button
                size="mini"
                v-if="btnList.start_btn"
                @click="handleUp(scope.$index, scope.row)"
                >{{
                  scope.row.approvalStatus == 0 ? "上线" : "下线"
                }}</el-button
              >
              <el-button v-if="btnList.remove_btn" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)" :disabled="scope.row.approvalStatus === 1">删除</el-button>
              <el-button
                size="mini"
                v-if="btnList.export_survey"
                @click="handleLead(scope.$index, scope.row)"
                >导出</el-button
              >
              <el-button size="mini" v-if="btnList.survey_statistics" plain @click="statistics(scope.row)">统计</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 新增或编辑 -->
    <!-- <survey-create v-else @close="close" :obj="itemObj"></survey-create> -->
    <!-- 统计 -->
      <statistics  @close="close" v-if="$route.query.type&&$route.query.type=='statistics'" :formId="formId" :templateName="templateName" :surveyId="surveyId" :obj="itemObj"></statistics>
     <!-- 批量上线 -->
     <el-dialog title="批量上线" v-model="onlineVisible" width="70%">
        <h3>确定上线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.templateName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量下线 -->
      <el-dialog title="批量下线" v-model="offLineVisible" width="70%">
        <h3>确定下线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.templateName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 上/下线 -->
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
        <span>是否{{ lineText == "上线提示" ? "上线" : "下线" }}
          <el-tag>{{ currentChose.templateName }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="lineSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量删除 -->
       <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.templateName }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 删除单条 -->
      <el-dialog  title="删除提示" v-model="deleteVisible" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.templateName }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    <!-- 批量设置权限弹框 -->
    <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog>
    <!-- 批量设置分类弹框 -->
    <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import  controlDialog  from '../components/controlDialog.vue'
import  classifyDialog  from '../components/classifyDialog.vue'
import surveyCreate from "@/components/Survey/create";
import statistics from "@/components/Survey/statistics";
import { ElMessage } from "element-plus";
import filterList from "@/mixins/filterList.js";

import {
  getMedsciSurveyPage,
  // surveyOnOffLine,
  excelExport,
  query,
  listTag,
  batchLineModule,
  deleteBeOfflineSurvey
} from "@/request/service.js";
export default {
  name: "Survey",
  mixins: [initModule, initScroll, initAccountTypeList,filterList],
  components:{
    controlDialog,
    classifyDialog,
    surveyCreate,
    statistics
  },
  data() {
    return {
      title: "",
      lineText:'',
      tableData: [],
      choseData: [],
      ClassifyData:[],
      ClassifyValue: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      RoleList:[],
      selectvalue:[],
      onlineVisible:false,
      offLineVisible:false,
      dialogVisible:false,
      settingVisible:false,
      deleteVisible:false,
      createdBy: '',
      listCreate: [],
      createTime: '',
      filterLists: [
        { label: '全部', value: null },
        { label: '无', value: 0 },
        { label: '积分', value: 1 }
      ],
      bonus: '',
      statusList: [
        { label: '全部', value: 2 },
        { label: '已下线', value: 0 },
        { label: '已上线', value: 1 }
      ],
      approvalStatus: '',
      isIndex: true, //是否展示调研首页,
      itemObj: {},
      publishedEndTime: '',
      publishedStartTime: '',
      offLineData:{},
      surveyId:"",
      formId:"",
      templateName:"",
      accountTypeValue: '',
      refesh:false
    };
  },
  created(){
    if(this.$route.query.type){
      this.isIndex = false
      this.surveyId = this.$route.query.surveyId
      this.formId=this.$route.query.formId
      this.templateName=this.$route.query.templateName
    }
  },
  mounted() {
    
    this.init();
    this.getClassifyData()
    
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val){
      if(val===0||val===1){
        this.getPowerData(val)
      }else if(val===""){
        this.RoleList = []
      }
    },
    dataURLtoFile(dataUrl, fileName){
  let arr = dataUrl.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], fileName, { type: mime })
},
    filterChange(id) {
      this.bonus = id
      this.init() // 列表刷新的方法
    },
    statusChange(status) {
      this.approvalStatus = status
      this.init()
    },
    // 创建人
    getCreateByOptions() {

    },
    // 更改发布时间
    changePublishTime(val){
      this.publishedStartTime = val ? val[0] : ''
      this.publishedEndTime = val ? val[1] : ''
    },
    init() {
      if(this.choseData.length>0){
            this.refesh=true
          }
      this.tableData=[]
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        value: this.title,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue,
        // approvalStatus:1,
        bonus: this.bonus,
        approvalStatus: this.approvalStatus,
        publishedEndTime: this.publishedEndTime,
        publishedStartTime: this.publishedStartTime
      };
      getMedsciSurveyPage(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.filterData = this.tableData;
          this.btnStatus=false
          this.initScroll();
        }
      });
    },
    // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
     // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'survey',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
   getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
     // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    // 批量设置权限
    setPower() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }else{
       this.$refs.controlRef.openDialog('survey')
      }  
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据！')
        return
      }
      this.$refs.classifyRef.openDialog('survey')
      this.$refs.classifyRef.gitClassList()
    },
    // 批量上线
     lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.onlineVisible = true
    },
    // 批量下线
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.offLineVisible = true
    },
    // 批量删除
    deleteClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.offLineData=this.choseData.filter(item=>item.approvalStatus === 0)
      this.settingVisible = true;
    },
    // 删除单条
    handleDelete(index, row) {
      this.deleteVisible = true;
      this.currentChose = row;
    },
     // 批量上线按钮
    saveOnlineEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"survey"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
          ElMessage.success("批量上线成功");
          this.onlineVisible = false
        }
      })
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"survey"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
          ElMessage.success("批量下线成功");
          this.offLineVisible = false
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    // 上/下线
    handleUp(index, row) {
      if (row.approvalStatus) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    // 上/下线按钮
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        operationType: this.lineText == "上线提示" ? 1 : 0,
        moduleType:'survey',
        moduleIds: ids,
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "上线提示"? ElMessage.success("上线成功"):ElMessage.success("下线成功")
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    handleLead(index, val) {
      let params = {
        id: val.id,
        formId: val.templateId,
        userId: this.$store.state.backInfo.userInfo.userId,
        userName: this.$store.state.backInfo.userInfo.userName,
      };
      excelExport(params).then((res) => {
        if (res.status == 200) {
          window.location.href = res.data;
        }
      });
    },
    reset() {
      this.title = '';
      this.selectvalue =[]
      this.ClassifyValue = []
      this.createTime= ''
      this.publishedEndTime = ''
      this.publishedStartTime = ''
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    // 勾选框
    handleSelectionChange(selection){
          this.choseData=selection
    },
    //  点击权限多选框选项
     change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    }, 
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
    // 删除
    saveDeleteEdit() {
      let ids = [];
      this.offLineData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
        projectId: this.$store.state.projectInfo.id,
        // projectId: this.$store.state.projectInfo.id,
      };
      deleteBeOfflineSurvey(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.settingVisible = false;
        }
      });
    },
     // 单条删除
    deleteSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        ids: ids,
        projectId: this.$store.state.projectInfo.id,
      };
      deleteBeOfflineSurvey(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;  
          this.init();
        }
      });
    },
    // 新增或编辑调研
    statistics(item){
      this.$router.push({path:"/survey",query:{type:"statistics",surveyId:item.id,formId:item.templateId,templateName:item.templateName}})
    },
    close() {
      this.isIndex = true
      this.init()
    }

  },
};
</script>
<style lang="scss" scoped>
.el-dropdown {
  cursor: pointer;
}
.bonus-style{
  color:#999;
}
.active{
  color: #409eff!important;
}
.status-label{
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
  position: relative;
  top: 1px;
}
</style>
<style scoped lang="scss">
.yxd_survey {
.container{
  height:calc( 100% - 220px);
}
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
     white-space: nowrap;
    }
  }
  
  .table {
    cursor: pointer;
  }
  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 58.333%;
      display: flex;
      align-items: center;
      span {
        white-space: nowrap;
        margin-right: 10px;
      }
      .edit-tag {
        min-height: 30px;
        border-bottom: 1px solid #dcdfe6;
        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
    .right {
    }
  }
  .edit-main {
    margin-top: 30px;
    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;
      display: block;
      span {
        width: 70px;
        font-size: 12px;
        color: #409eff;
        text-align: right;
        margin-right: 20px;
        font-weight: 600;
        margin-top: 3px;
      }
      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }
        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}
</style>
