<template>
  <div class="yxd_swiper">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="首页轮播图" name="first">
          <template v-if="activeName == 'first'">
            <swiper-index
              v-if="isOneIndex"
              :type="activeName"
              @create="createOne"
              @operate="operateFn1"
            ></swiper-index>
            <swiper-create
              v-else
              @back="isOneIndex = true"
              :info="oneForm"
              :type="activeName"
              :operate="operateType1"
            ></swiper-create>
          </template>
        </el-tab-pane>
        <el-tab-pane label="首页广告位" name="second">
          <template v-if="activeName == 'second'">
            <swiper-index
              v-if="isTwoIndex"
              :type="activeName"
              @create="createTwo"
              @operate="operateFn2"
            ></swiper-index>
            <swiper-create
              v-else
              @back="isTwoIndex = true"
              :info="twoForm"
              :type="activeName"
              :operate="operateType2"
            ></swiper-create>
          </template>
        </el-tab-pane>
        <el-tab-pane label="资讯轮播图" name="third">
          <template v-if="activeName == 'third'">
            <swiper-index
              v-if="isThreeIndex"
              :type="activeName"
              @create="createThree"
              @operate="operateFn3"
            ></swiper-index>
            <swiper-create
              v-else
              @back="isThreeIndex = true"
              :info="threeForm"
              :type="activeName"
              :operate="operateType3"
            ></swiper-create>
          </template>
        </el-tab-pane>
        <el-tab-pane label="直播轮播图" name="fourth">
          <template v-if="activeName == 'fourth'">
            <swiper-index
              v-if="isFourIndex"
              :type="activeName"
              @create="createFour"
              @operate="operateFn4"
            ></swiper-index>
            <swiper-create
              v-else
              @back="isFourIndex = true"
              :info="fourForm"
              :type="activeName"
              :operate="operateType4"
            ></swiper-create>
          </template>
        </el-tab-pane>
        <el-tab-pane label="课程轮播图" name="five">
          <template v-if="activeName == 'five'">
            <swiper-index
              v-if="isFiveIndex"
              :type="activeName"
              @create="createFive"
              @operate="operateFn5"
            ></swiper-index>
            <swiper-create
              v-else
              @back="isFiveIndex = true"
              :info="fiveForm"
              :type="activeName"
              :operate="operateType5"
            ></swiper-create>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import swiperIndex from "@/components/Swiper/index";
import swiperCreate from "@/components/Swiper/create";
import initScroll from "@/mixins/initScroll.js";
export default {
  name: "swiper",
  mixins: [initScroll],
  components: {
    swiperIndex,
    swiperCreate,
  },
  data() {
    return {
      activeName: "first",
      isOneIndex: true,
      isTwoIndex: true,
      isThreeIndex: true,
      isFourIndex: true,
      isFiveIndex: true,
      oneForm: {},
      twoForm: {},
      threeForm: {},
      fourForm: {},
      fiveForm: {},
      operateType1: "",
      operateType2: "",
      operateType3: "",
      operateType4: "",
      operateType5: "",
    };
  },
  mounted() {
    this.initScroll();
  },
  methods: {
    operateFn1(val) {
      this.operateType1 = val;
    },
    operateFn2(val) {
      this.operateType2 = val;
    },
    operateFn3(val) {
      this.operateType3 = val;
    },
    operateFn4(val) {
      this.operateType4 = val;
    },
    operateFn5(val) {
      this.operateType5 = val;
    },
    createOne(val) {
      this.isOneIndex = false;
      this.oneForm = val;
    },
    createTwo(val) {
      this.isTwoIndex = false;
      this.twoForm = val;
    },
    createThree(val) {
      this.isThreeIndex = false;
      this.threeForm = val;
    },
    createFour(val) {
      this.isFourIndex = false;
      this.fourForm = val;
    },
    createFive(val) {
      this.isFiveIndex = false;
      this.fiveForm = val;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_swiper {
}
</style>
