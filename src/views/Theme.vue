<template>
  <div class="yxd_theme">
    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="handleChangeTab"
    >
      <el-tab-pane label="主题设置" name="first">
        <div
          id="container"
          class="container"
          :class="{ isScroll: isScroll }"
          ref="container"
        >
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column
              prop="name"
              align="center"
              label="名称"
              width="120"
            >
            </el-table-column>
            <!-- <el-table-column
              prop="projectNameColor"
              align="center"
              label="项目名称"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.projectNameColor"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.projectNameColor }"
                ></div>
              </template>
            </el-table-column> -->
            <el-table-column
              prop="topNavigation"
              align="center"
              label="顶部导航"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.topNavigation"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.topNavigation }"
                ></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="bottomNavigation"
              align="center"
              label="底部导航"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.bottomNavigation"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.bottomNavigation }"
                ></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="fontColor"
              align="center"
              label="字体颜色"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.fontColor"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.fontColor }"
                ></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="iconFontColor"
              align="center"
              label="图标字体颜色"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.iconFontColor"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.iconFontColor }"
                ></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="buttonColor"
              align="center"
              label="按钮颜色"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.buttonColor"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.buttonColor }"
                ></div>
              </template> </el-table-column
            ><el-table-column
              prop="backgroundColor"
              align="center"
              label="背景颜色"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.backgroundColor"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.backgroundColor }"
                ></div>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="otherDetailEntrance"
              align="center"
              label="其他细节入口"
              width="220"
            >
              <template #default="scope">
                <div
                  v-if="!scope.row.readonly"
                  @click="changeTheme(scope.row)"
                  class="color-picker-box"
                >
                  <el-color-picker
                    v-model="scope.row.otherDetailEntrance"
                    @change="handleColorChange"
                  ></el-color-picker>
                </div>
                <div
                  v-else
                  class="color-theme"
                  :style="{ background: scope.row.otherDetailEntrance }"
                ></div>
              </template>
            </el-table-column> -->
            <el-table-column
              prop="status"
              align="center"
              label="使用状态"
              class-name="special"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-color="#2f92ee"
                  inactive-color="#f0f2f5"
                  @change="switchChange(scope.row)"
                ></el-switch>
                <span>{{ scope.row.status ? "已开启" : "已关闭" }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="登录背景" name="second">
        <backgroundpage></backgroundpage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  themeColorList,
  openStatus,
  saveCustomColor,
} from "@/request/service.js";
import initScroll from "@/mixins/initScroll.js";
import backgroundpage from "./setting/background.vue";
export default {
  name: "theme",
  mixins: [initScroll],
  components: {
    backgroundpage,
  },
  data() {
    return {
      tableData: [],
      rowDeatil: {},
      activeName: "first",
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      themeColorList().then((res) => {
        if (res.status == 200) {
          this.tableData = res.data;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.status == 1) {
              val.status = true;
            } else {
              val.status = false;
            }
          });
        }
      });
    },
    switchChange(row) {
      if (row.status) {
        let params = {
          id: row.id,
        };
        openStatus(params).then((res) => {
          if (res.status == 200) {
            this.tableData.forEach((d) => {
              d.status = false;
              if (d.id == row.id) {
                d.status = true;
              }
            });
          }
        });
      }
    },
    changeTheme(row) {
      this.rowDeatil = row;
    },
    handleColorChange(color) {
      // 在这里处理颜色选择变化的逻辑
      let params = this.rowDeatil;
      saveCustomColor(params).then((res) => {
        if (res.status == 200) {
          this.init();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  padding-bottom: 20px;
}
.yxd_theme {
  .color-theme {
    height: 28px;
    width: 100px;
    margin: 0 auto;
  }
  span {
    margin-left: 10px;
  }

  ::v-deep .demo-tabs {
    background-color: #fff;
  }
}
.color-picker-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-color-picker--mini .el-color-picker__trigger {
  height: 30px;
  width: 100px;
}
::v-deep .el-color-picker__trigger {
  border: 0px solid #e6e6e6;
  padding: 0px;
}
::v-deep .el-color-picker__color {
  border: 0px solid #999;
}
::v-deep .el-icon-close:before {
  content: "自定义颜色";
}
::v-deep .el-color-picker__empty {
  color: #409eff;
  width: 100px;
  font-size: 16px;
  font-weight: 600;
}
</style>
