<template>
  <div class="yxd_defined">
    <div class="container">
      <h3>自定义用户信息</h3>
      <div class="description">
        <span
          >可以根据项目需求，自定义批量导入用户时，模板中需要填写的字段。</span
        >
        <span>最多可以自定义20个。</span>
      </div>
      <div class="operate">
        <el-button @click="setNew">添加字段</el-button>
      </div>
      <div class="table">
        <el-table border :data="tableData" tooltip-effect="dark">
          <el-table-column prop="id" label="序号" width="100" align="center">
          </el-table-column>
          <el-table-column
            prop="fieldDisplayName"
            label="字段名"
            width="200"
            align="center"
          >
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                :disabled="scope.row.disabled"
                size="mini"
                @click="handleEdit(scope.$index, scope.row)"
                >修改</el-button
              >
              <el-button
                :disabled="scope.row.disabled"
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="添加/修改字段" v-model="editVisible" width="50%">
      <div class="edit-main">
        <div class="col">
          <span class="tl"><span style="color: red">*</span> 字段名称</span>
          <el-input
            v-model="currentChose.fieldName"
            placeholder="请输入<=20字以内的字段名"
          ></el-input>
        </div>
        <div class="col">
          <span class="tl"> <span style="color: red">*</span> 显示名称</span>
          <el-input
            v-model="currentChose.fieldDisplayName"
            placeholder="默认跟字段名称保持一致，可手动修改"
          ></el-input>
        </div>
        <div class="col">
          <span class="tl"><span style="color: red">*</span> 字段类型</span>
          <el-select v-model="currentChose.fieldTypeName" placeholder="请选择">
            <el-option
              v-for="item in categoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editVisible = false" size="mini">取 消</el-button>
          <el-button type="primary" @click="saveEdit" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog title="删除" v-model="deleteOneVisible" width="30%">
      <span>确定删除{{ currentChose.fieldDisplayName }}</span>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteOneVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="saveDeleteOne" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import initModule from '@/mixins/initModule.js'
import {
  listUserDefinedFieldByFieldName,
  updateUserDefinedFieldById,
  deleteUserDefinedFieldById,
  addUserDefinedField
} from '@/request/service.js'
export default {
  name: 'userdefined',
  mixins: [initModule],
  data() {
    return {
      name: '',
      title: '',
      categoryValue: '',
      categoryList: [
        {
          label: '字符串',
          value: '1'
        },
        {
          label: '数字',
          value: '2'
        },
        {
          label: '日期',
          value: '3'
        },
        {
          label: '时间',
          value: '4'
        },
        {
          label: '整数',
          value: '5'
        },
        {
          label: '附件上传',
          value: '8'
        }
      ],
      editVisible: false,
      tableData: [],
      deleteOneVisible: false,
      currentChose: {
        fieldDisplayName: '',
        fieldMark: '',
        fieldName: '',
        fieldType: '',
        id: '',
        fieldTypeName: ''
      },
      setType: ''
    }
  },
  computed: {
    // currentChoseType() {
    //   let val = null;
    //   if (this.currentChose.fieldType) {
    //     this.categoryList.forEach((d) => {
    //       if (d.value == this.currentChose.fieldType) {
    //         val = d.label;
    //       }
    //     });
    //   }
    //   return val;
    // },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let params = {
        fieldName: '',
        projectId: this.$store.state.projectInfo.id
      }
      listUserDefinedFieldByFieldName(params).then((res) => {
        this.tableData = res.data.map((d) => {
          return {
            ...d,
            disabled: [
              '序号',
              '真实姓名',
              '昵称',
              '手机号码',
              '用户组ID'
            ].includes(d.fieldDisplayName)
          }
        })
      })
    },
    setNew() {
      if(this.tableData.length>20)  return   ElMessage.warning('自定义字段最多添加15个')
      this.currentChose = {
        fieldDisplayName: '',
        fieldMark: '',
        fieldName: '',
        fieldType: '',
        id: '',
        fieldTypeName: ''
      }
      this.setType = 'add'
      this.editVisible = true
    },
    handleEdit(index, row) {
      console.log(index, row)
      this.currentChose = row
      if (this.currentChose.fieldType) {
        this.categoryList.forEach((d) => {
          if (d.value == this.currentChose.fieldType) {
            this.currentChose.fieldTypeName = d.label
          }
        })
      }
      this.editVisible = true
    },
    handleDelete(index, row) {
      this.currentChose = row
      if (this.currentChose.fieldType) {
        this.categoryList.forEach((d) => {
          if (d.value == this.currentChose.fieldType) {
            this.currentChose.fieldTypeName = d.label
          }
        })
      }
      this.deleteOneVisible = true
    },
    toFieldValue(val) {
      let d
      this.categoryList.forEach((t) => {
        if (t.value == val) {
          d = t.value
        }
      })
      return Number(d)
    },
    saveEdit() {
      if (!this.currentChose.fieldName) {
        ElMessage.warning('字段名称不可为空！')
        return
      }
      if (!this.currentChose.fieldDisplayName) {
        ElMessage.warning('显示名称不可为空！')
        return
      }
      if (!this.currentChose.fieldTypeName) {
        ElMessage.warning('字段类型不可为空！')
        return
      }

      if (this.setType == 'add') {
        let params = {
          fieldDisplayName: this.currentChose.fieldDisplayName,
          fieldName: this.currentChose.fieldName,
          fieldType: this.toFieldValue(this.currentChose.fieldTypeName),
          projectId: this.$store.state.projectInfo.id
        }
        addUserDefinedField(params).then(() => {
          this.editVisible = false
          this.currentChose = {
            fieldDisplayName: '',
            fieldMark: '',
            fieldName: '',
            fieldType: '',
            id: '',
            fieldTypeName: ''
          }
          this.setType = ''
          this.init()
        })
      } else {
        let params = {
          fieldDisplayName: this.currentChose.fieldDisplayName,
          fieldName: this.currentChose.fieldName,
          fieldType: this.toFieldValue(this.currentChose.fieldTypeName),
          projectId: this.$store.state.projectInfo.id,
          id: this.currentChose.id
        }
        updateUserDefinedFieldById(params).then(() => {
          this.editVisible = false
          this.currentChose = {
            fieldDisplayName: '',
            fieldMark: '',
            fieldName: '',
            fieldType: '',
            id: '',
            fieldTypeName: ''
          }
          this.setType = ''
          this.init()
        })
      }
    },
    saveDeleteOne() {
      let params = {
        id: this.currentChose.id,
        projectId: this.$store.state.projectInfo.id
      }
      deleteUserDefinedFieldById(params).then(() => {
        this.deleteOneVisible = false
        this.init()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.yxd_defined {
  .description {
    margin-top: 30px;
  }
  .table {
    width: 500px;
  }
  .edit-main {
    .col {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-input {
        width: 30%;
      }
      .el-textarea {
        width: 30%;
      }
      .tl {
        width: 70px;
        white-space: nowrap;
        margin-right: 20px;
        text-align: right;
      }
    }
  }
}
</style>
