<template>
  <div class="yxd_yxd">
    <div class="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入医讯达标题"></el-input>
          </div>
          <!-- 请选择用户类型 -->
          <div class="col">
            <el-select
              v-model="accountTypeValue"
              clearable
              class="m-2"
              placeholder="请选择用户类型"
              @change="handleChange"
            >
              <el-option
                v-for="item in accountTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div> 
          <!-- 筛选权限 -->
          <div class="col" v-if="true">
           <el-cascader
           placeholder="请选择权限"
            :options="RoleList"
            ref="cascader"
            collapse-tags	
            @change="change"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'title',
            children:'childRoles',
             multiple: true
            }"
            clearable>
          </el-cascader> 
        </div>
        <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
            placeholder="请选择分类"
            :options="ClassifyData"
            ref="Classify"
            collapse-tags	
            @change="ClassifyChange"
            :props="{ 
            checkStrictly: true,
            value: 'id',
            label:'tagName',
            children:'childTags',
            multiple: true
            }"
            clearable>
          </el-cascader> 
          </div>
           <div class="col">
          <el-date-picker v-model="lineTime" type="datetimerange" range-separator="至" start-placeholder="创建开始时间"
            end-placeholder="创建结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div>
        <div class="operate">
          <el-button @click="setPower" v-if="btnList.batchSetRole">批量设置权限</el-button>
          <el-button @click="setClass" v-if="btnList.batchSetTag">批量设置分类</el-button>
          <!-- <el-button @click="setDetClass">批量删除分类</el-button> -->
          <el-button  @click="lineBatch" v-if="btnList.batch_start_btn">批量上线</el-button>
          <el-button  @click="offLineBatch" v-if="btnList.batch_end_btn">批量下线</el-button>
          <el-button  v-if="btnList.export_eda" @click="handleLead('', '','all')" >全部导出</el-button>
          <el-button @click="filterList()" :type="btnStatus ? 'primary' : ''"
          >未设置分类</el-button
        >
        </div>
        <div class="table">
          <el-table
            ref="multipleTable"
            border
            :data="tableData"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            ></el-table-column>
            <el-table-column prop="id" label="Id" width="55" align="center">
            </el-table-column>
            <el-table-column
              prop="title"
              label="标题"
              width="200"
              align="center"
            >
              <template #default="scope">
                <a :href="'/yxdDetail/'+scope.row.encryptionId" target="_blank">{{
                  scope.row.title
                }}</a>
              </template>
            </el-table-column>
            <el-table-column
              prop="num"
              label="PV / UV / 分享 / 点赞 / 收藏"
              min-width="180"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="createdTime"
              label="创建时间"
              width="150"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="createdName"
              label="创建人"
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              min-width="70"
              align="center"
            >
              <template #default="scope">
                <span
                  :class="{ start: scope.row.status }"
                  class="default"
                ></span>
                <span>{{ scope.row.yxdOnOffLine===1?'上线':'下线'}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="publishedTime"
              label="发布时间"
              min-width="150"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              min-width="220"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  size="mini"
                  v-if="btnList.start_btn"
                  @click="handleRead(scope.$index, scope.row)"
                  >{{
                    scope.row.yxdOnOffLine == "1" ? "下线" : "上线"
                  }}</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.$index, scope.row)"
                  :disabled="scope.row.yxdOnOffLine == 1"
                  >删除</el-button
                >
                <el-button
                plain
                size="mini"
                v-if="btnList.export_eda"
                @click="handleLead(scope.$index, scope.row)"
                >导出</el-button
              >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
        <!-- 编辑弹出框 -->
        <el-dialog title="批量设置分类" v-model="editVisible" width="70%">
          <div class="edit-head">
            <div class="left">
              <span>已选分类</span>
              <div class="edit-tag">
                <el-tag
                  v-for="tag in tags"
                  :key="tag.tagName"
                  closable
                  size="mini"
                  :type="tag.type"
                >
                  {{ tag.tagName }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="edit-main">
            <div class="title">
              <span>可选分类</span>
              <div class="group"></div>
            </div>
            <div class="title" v-for="(item, index) in classGroup" :key="index">
              <div class="group">
                <div
                  class="btn"
                  @click="item.checked = !item.checked"
                  :class="{ checked: item.checked }"
                >
                  {{ item.tagName }}
                </div>
              </div>
            </div>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="editVisible = false" size="mini"
                >取 消</el-button
              >
              <el-button type="primary" @click="saveEdit" size="mini"
                >确 定</el-button
              >
            </span>
          </template>
        </el-dialog>

        <!-- 批量删除分类 -->
        <!-- <el-dialog title="批量删除分类" v-model="deleteVisible" width="70%">
          <div class="edit-head">
            <div class="left">
              <span>已选分类</span>
              <div class="edit-tag">
                <el-tag
                  v-for="tag in tags"
                  :key="tag.tagName"
                  closable
                  size="mini"
                  :type="tag.type"
                >
                  {{ tag.tagName }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="edit-main">
            <div class="title">
              <span>可选分类</span>
              <div class="group"></div>
            </div>
            <div class="title" v-for="(item, index) in classGroup" :key="index">
              <div class="group">
                <div
                  class="btn"
                  @click="item.checked = !item.checked"
                  :class="{ checked: item.checked }"
                >
                  {{ item.tagName }}
                </div>
              </div>
            </div>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="deleteVisible = false" size="mini"
                >取 消</el-button
              >
              <el-button type="primary" @click="saveDelete" size="mini"
                >确 定</el-button
              >
            </span>
          </template>
        </el-dialog> -->

        <el-dialog title="删除" v-model="deleteOneVisible" width="30%">
          <span>确定删除{{ currentChose.title }}</span>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="deleteOneVisible = false" size="mini"
                >取 消</el-button
              >
              <el-button type="primary" @click="saveDeleteOne" size="mini"
                >确 定</el-button
              >
            </span>
          </template>
        </el-dialog>

        <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
          <span
            >是否{{ lineText == "上线提示" ? "上线" : "下线" }}
            <el-tag>{{ currentChose.title }}</el-tag></span
          >
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false" size="mini"
                >取 消</el-button
              >
              <el-button type="primary" @click="lineSave" size="mini"
                >确 定</el-button
              >
            </span>
          </template>
        </el-dialog>
         <!-- 批量上线 -->
     <el-dialog title="批量上线" v-model="onlineVisible" width="70%">
        <h3>确定上线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onlineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOnlineEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 批量下线 -->
      <el-dialog title="批量下线" v-model="offLineVisible" width="70%">
        <h3>确定下线这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in choseData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="offLineVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveOffEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
        <!-- 批量设置权限弹框 -->
        <control-dialog ref="controlRef" :choseData="choseData" :RoleList="RoleList"></control-dialog>
        <!-- 批量设置分类弹框 -->
        <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import {
  edaGetAriticlePage,
  edaDeleteTag,
  edaBatchEdit,
  tagTypeList,
  query,
  listTag,
  batchLineModule
} from "@/request/service.js";
import { exportUserEda,edaList   } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import initAccountTypeList from "@/mixins/initAccountTypeList.js";
import filterList from "@/mixins/filterList.js";
import  controlDialog  from '../components/controlDialog.vue'
import  classifyDialog  from '../components/classifyDialog.vue'
export default {
  name: "meet",
  mixins: [initModule, initAccountTypeList,filterList],
  components:{
    controlDialog,
    classifyDialog
  },
  data() {
    return {
      lineTime:"",
      editVisible: false,
      deleteVisible: false,
      deleteOneVisible: false,
      dialogVisible: false,
      onlineVisible: false,
      offLineVisible: false,
      title: "",
      tableData: [],
      classGroup: [],
      choseData: [],
      lineText: "上线提示",
      total: 0,
      currentPage: 1,
      pageSize: 20,
      currentChose: {},
      RoleList:[],
      ClassifyData:[],
      selectvalue:[],
      ClassifyValue: [],
      accountTypeValue: '',
      refesh:false
    };
  },
  mounted() {
    this.init();
    this.getClassifyData()
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
  },
  methods: {
    set(){
      this.init()
    },
    handleChange(val){
      if(val===0||val===1){
        this.getPowerData(val)
      }else if(val===""){
        this.RoleList = []
      }
    },
    reset() {
      this.title = '',
      this.selectvalue=[]
      this.ClassifyValue = []
      let obj = {}
            obj.stopPropagation = () => {}
            try{
                this.$refs.cascader.clearValue(obj)
                this.$refs.Classify.clearValue(obj)
            }catch(err){
                this.$refs.cascader.handleClear(obj)
                this.$refs.Classify.handleClear(obj)
            }
    },
    init() {
     if(this.choseData.length>0){
            this.refesh=true
          }
      this.tableData=[]
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        title: this.title,
        status: 1,
        projectId: this.$store.state.projectInfo.id,
        roleIds:this.selectvalue,
        classifyIds:this.ClassifyValue
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      edaGetAriticlePage(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data?.map((d) => {
          return {
            ...d,
            num: `${d.allHits}/${d.userHits}/${d.shares}/${d.likes}/${d.collect}`,
            gotoUrl:
              `https://` +
              window.location.host +
              `/yxdDetail/${d.encryptionId}`,
          };
        });
        this.filterData = this.tableData;
        this.btnStatus = false;
      });
    },
  // 权限下拉列表
    getPowerData(val) {
      let params = {
        accountType: val,
        title: '',
      };
        query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds:[],
        tagType:'eda',
      };
      listTag(params).then((res) => {
        this.ClassifyData=this.removeClassifyChild(res.data);
      })
    },
   getList(data){
    return  data.filter(item=>{
        if(item.childRoles.length===0){
           delete item.childRoles
           return item
        }else{
          return  this.getList(item.childRoles)
        }
     })
    },
    // 清除空的子类
    removeClassifyChild(data){
      return  data.filter(item=>{
        if(item.childTags.length===0){
           delete item.childTags
           return item
        }else{
          return  this.removeClassifyChild(item.childTags)
        }
     })
    },
    // 选项框勾选
    handleSelectionChange(selection) {
          this.choseData=selection
    },
    // 权限搜索框选中
    // handleChange(value){
    // },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    handleRead(index, val) {
      if (val.yxdOnOffLine == 1) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.currentChose = val;
      this.dialogVisible = true;
    },
    initTag() {
      let params = {
        tagIds: [],
        tagType: "eda",
      };
      tagTypeList(params).then((res) => {
        if (res.status == 200) {
          this.classGroup = res.data;
          this.classGroup.forEach((val) => {
            val.checked = false;
          });
        }
      });
    },
    saveEdit() {
      let ids = [];
      let tagDtoList = [];
      this.tags.forEach((d) => {
        tagDtoList.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      this.choseData.forEach((val) => {
        ids.push(val.id);
      });
      let params = {
        ids: ids,
        tagDtoList: tagDtoList,
        type: 4,
      };
      edaBatchEdit(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.editVisible = false;
        }
      });
    },
    saveDelete() {
      let ids = [];
      let tagDtoList = [];
      this.tags.forEach((d) => {
        tagDtoList.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      this.choseData.forEach((val) => {
        ids.push(val.encryptionId);
      });
      let params = {
        edaIds: ids,
        tags: tagDtoList,
      };
      edaDeleteTag(params).then((res) => {
        if (res.status == 200) {
          this.init();
          this.deleteVisible = false;
        }
      });
    },
    // 批量设置权限
    setPower(){
     if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.$refs.controlRef.openDialog('eda')
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog('eda')
      this.$refs.classifyRef.gitClassList()
    },
    // 批量删除分类
    // setDetClass() {
    //   if (this.choseData.length == 0) {
    //     ElMessage.warning("请至少选择一条数据！");
    //     return;
    //   }
    //   this.initTag();
    //   this.deleteVisible = true;
    // },
 // 批量上线
     lineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.onlineVisible = true
    },
    // 批量下线
    offLineBatch() {
      if (this.choseData.length == 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      this.offLineVisible = true
    },
    // 批量上线按钮
    saveOnlineEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      // let params = {
      //   courseIds: courseIds,
      //   projectId: this.$store.state.projectInfo.id
      // }
      let params = {
        operationType: 1,
        moduleIds: ids,
        moduleType:"eda"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
          ElMessage.success("批量上线成功");
          this.onlineVisible = false
        }
      })
    },
    // 批量下线按钮
    saveOffEdit() {
      let ids = []
      this.choseData.forEach((val) => {
        ids.push(val.id)
      })
      let params = {
        operationType: 0,
        moduleIds: ids,
        moduleType:"eda"
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.init()
           ElMessage.success("批量下线成功");
          this.offLineVisible = false
        }
      })
    },

    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let tagDtoList = [];
      this.tags.forEach((d) => {
        tagDtoList.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      let params = {
        moduleIds: ids,
        operationType: this.currentChose.yxdOnOffLine == 1 ? 0 : 1, //yxdOnOffLine（ 0 下线 ，1上线）
        moduleType:"eda"
      };
      batchLineModule(params).then(() => {
        this.currentChose.yxdOnOffLine == 1? ElMessage.success("下线成功"):ElMessage.success("上线成功")
        this.init();
        this.dialogVisible = false;
      });
    },
    handleDelete(index, val) {
      this.currentChose = val;
      this.deleteOneVisible = true;
    },
    saveDeleteOne() {
      let ids = [];
      ids.push(this.currentChose.id);
      let tagDtoList = [];
      this.tags.forEach((d) => {
        tagDtoList.push({
          tagId: d.tagId,
          tagName: d.tagName,
        });
      });
      let params = {
        ids: ids,
        objectType: 'eda',
        type: 1,
        tagDtoList: tagDtoList,
      };
      edaBatchEdit(params).then(() => {
        this.init();
        this.deleteOneVisible = false;
      });
    },
    // 导出
    handleLead(index, row,types) {
       if(types=="all"){
        // 全部导出
       edaList(this.lineTime[0],this.lineTime[1],this.$store.state.projectInfo.id).then((res) => {
        if (res.size == 51) {
          ElMessage.warning("暂无数据导出");
          
        }else if(res.size==63){
          ElMessage.warning("上线时间不能为空");
        } else {
          let name = `医迅达数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        }
        })
      }else{
      exportUserEda(row.id).then((res) => {
        if (res.size > 57) {
          let name = row.title+`数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          ElMessage.warning("暂无数据导出");
        }
      })}
    },
    //  点击权限多选框选项
    change(){
       let nodesObj = this.$refs['cascader'].getCheckedNodes()
       this.selectvalue=[]
       nodesObj.forEach(item=>this.selectvalue.push(item.data.id))
    },
    //  点击分类多选框选项
    ClassifyChange(){
       let nodesObj = this.$refs['Classify'].getCheckedNodes()
       this.ClassifyValue=[]
       nodesObj.forEach(item=>this.ClassifyValue.push(item.data.id))
    }, 
  },
};
</script>

<style scoped lang="scss">
.yxd_yxd {
  .header {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }
}
.edit-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    width: 58.333%;
    display: flex;
    align-items: center;
    span {
      white-space: nowrap;
      margin-right: 10px;
    }
    .edit-tag {
      min-height: 30px;
      min-width: 80px;
      border-bottom: 1px solid #dcdfe6;
      .el-tag--mini {
        margin-bottom: 3px;
      }
    }
  }
  .right {
  }
}
.edit-main {
  display: flex;
  margin-top: 30px;
  .title {
    display: flex;
    margin-top: 8px;
    margin-bottom: 6px;
    overflow: hidden;
    height: auto;
    span {
      // width: 70px;
      // font-size: 12px;
      // color: #409eff;
      // text-align: right;
      // margin-right: 20px;
      // font-weight: 600;
      // margin-top: 3px;
      margin-right: 10px;
    }
    .group {
      .btn {
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color: #f0f2f5;
        font-size: 11px;
        font-weight: 400;
        display: inline-block;
        cursor: pointer;
        margin-right: 20px;
        margin-bottom: 6px;
      }
      .checked {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}
</style>
