<template>
  <div class="yxd_group">
    <group-index v-if="isIndex" @edit="editManage"></group-index>
    <group-detail
      :info="currentData"
      @back="backManage"
      v-else
    ></group-detail>
  </div>
</template>

<script>
import groupIndex from "@/components/backstage_role_list/index";
import groupDetail from "@/components/backstage_role_list/detail";
export default {
  name: "group",
  components: {
    groupIndex,
    groupDetail,
  },
  data() {
    return {
      isIndex: true,
      currentData: [],
    };
  },
  mounted() {},
  methods: {
    editManage(item) {
      this.currentData = item;
      this.isIndex = false;
    },
    backManage() {
      this.isIndex = true;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_group {
}
</style>
