<template>
  <div class="yxd_users">
    <users-index v-if="isIndex" @add="addUser" @edit="editUser"></users-index>
    <users-create
      v-else
      :tableList="currentData"
      :type="type"
      @back="isIndex = true"
    ></users-create>
  </div>
</template>

<script>
import usersIndex from "@/components/backstage_user_list/index";
import usersCreate from "@/components/backstage_user_list/create";
export default {
  name: "users",
  components: {
    usersIndex,
    usersCreate,
  },
  data() {
    return {
      isIndex: true,
      currentData: {},
      custom:{},
      type: "",
    };
  },
  mounted() {},
  methods: {
    addUser(item) {
      this.type = "add";
      this.currentData = item;
      this.isIndex = false;
    },
    editUser(item) {
      this.type = "edit";
      this.currentData = item;
      this.isIndex = false;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_users {
}
</style>
