<template>
  <div>
    <el-upload
      class="avatar-uploader"
      :show-file-list="false"
      :before-upload="handleAvatarSuccess"
    >
      <img v-if="imageUrl" :src="imageUrl" class="avatar" />
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
  </div>
</template>

<script>
import COS from "cos-js-sdk-v5";
import {
  ossToken,

} from "@/request/service.js";
export default {
  data() {
    return {
      imageUrl: "",
    };
  },
  methods: {
    handleAvatarSuccess(res, file) {
      this.uploadFile(res);
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    async uploadFile(file) {
      const res = await ossToken({ type: 1 });
      const cos = new COS({
        SecretId: res.data.accessKeyId,
        SecretKey: res.data.accessKeySecret,
        XCosSecurityToken:res.data.securityToken
      });
      cos.uploadFile(
        {
          Bucket: res.data.publicBucketName,
          Region: "ap-shanghai",
          Key: file.name,
          Body: file,
        },
        (err, data) => {
          if (err) {
          } else {

          }
        }
      );
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
  },
};
</script>

<style>
</style>