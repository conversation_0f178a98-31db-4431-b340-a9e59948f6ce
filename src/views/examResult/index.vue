<template>
    <div class="containerss" id="scrollBlock">
    <span v-if="data.isAnswer || data.isParse || data.isPass">
      <div class="left">
        <div
          class="top_img"
          :class="[
            data.isPass == null || data.isPass == 1 ? 'topTrue' : 'topFalse',
          ]"
        >
          <div class="top_img_title">
            <img
              src="https://static.medsci.cn/public-image/ms-image/90246d70-bed5-11eb-b319-cd51f388f4e7_user.png"
              alt=""
            />{{ data.userName }}的成绩单
          </div>
          <div class="top_img_content">
            <div v-if="data.thisExamScore != null">
              {{ data.thisExamScore }}<span>（分）</span>
            </div>
            <div v-if="data.isPass != null">
              {{
                data.isPass == 1
                  ? "恭喜您通过本次答题"
                  : "很遗憾您未通过本次答题"
              }}
            </div>
            <div>{{ data.title }}</div>
          </div>
        </div>
        <!-- 总体答题情况-->
        <div class="zong" v-if="data.statisticsType!=null&&data.statisticsType != 0">
          <div
            class="zong_left"
            v-if="
              (data.statisticsType == 2 && data.abscissas.length >= 2) ||
              data.statisticsType == 3 ||
              data.statisticsType == 4
            "
          >
            <div class="img">
              <img src="@/assets/images/exam/zongti.png" alt="" />总体答题情况
            </div>
            <div id="lineChat" style="width: 100%; height: 300px"></div>
          </div>
          <div
            class="zong_right"
            :class="
              (data.statisticsType == 2 && data.abscissas.length < 2) ||
              data.statisticsType == 1
                ? 'zong_right_w'
                : ''
            "
          >
            <div
              class="r_item"
              v-for="(item, index) in data.quotas"
              :key="index"
            >
              <img src="@/assets/images/exam/t_zong.png" alt="" />
              <span class="discription">{{ item }}</span>
            </div>
          </div>
        </div>
        <!-- 练习题结果 -->
        <div class="bottom_practice" v-if="data.mode==2">
          <div class="bg">
            <div>
              <div class="fc"><span>{{data.rightCount}}</span></div>
              <div>答对</div>
            </div>
            <div>
              <div >{{data.errorAnswerCount}}</div>
              <div>答错</div>
            </div>
            <div>
              <div >{{data.accuracyRate}}%</div>
              <div>正确率</div>
            </div>
          </div>
        </div>
        <div
          class="left_content"
          :class="`li` + (index + 1)"
          v-for="(item, index) in tiList"
          :key="index"
        >
          <el-tag
            class="mx-1"
            size="small"
            >{{ item.type == "1" ? "单选题" : "多选题" }}</el-tag
          >
          <div class="question">
            {{ item.paperQuestionSort }} .{{ item.title }}
          </div>
          <div
            class="answer"
            :style="item1.isSelected ? `background: ${themeBg()}` : ''"
            :class="[item1.isSelected ? 'answer1' : '']"
            v-for="(item1, index1) in item.options"
            :key="index1"
          >
            <div
              style="border-radius: 50%"
              v-if="item.type == 1"
              :class="[item1.isSelected ? 'active' : '']"
            >
              {{ item1.option }}
            </div>
            <div
              v-if="item.type == 2"
              :class="[item1.isSelected ? 'active' : '']"
            >
              {{ item1.option }}
            </div>
            <div :class="[item1.isSelected ? 'active1' : '']">
              {{ item1.content }}
            </div>
          </div>
          <!-- 正确答案 -->
          <div class="answerResult" v-if="item.answer || item.correctType">
            <div v-if="item.answer" style="margin-left: 0">
              正确答案:
              <span :style="{ color: $store.state.buttonColor }">{{
                item.answer
              }}</span>
            </div>
            <div v-if="item.correctType !== null && item.submitOptions">
              你的答案:
              <span>
                <div v-if="item.correctType == 1">
                  <van-icon
                    style="margin-right: 2px"
                    name="passed"
                    size="17"
                    :color="$store.state.buttonColor"
                  />
                  <img
                    style="width: 17px; height: 17px; margin-right: 2px"
                    src="@/assets/images/exam/exactness.png"
                    alt=""
                  />
                  <span :style="{ color: $store.state.buttonColor }">{{
                    item.submitOptions
                  }}</span>
                </div>
                <div v-if="item.correctType == 2">
                  <img
                    style="width: 17px; height: 17px; margin-right: 2px"
                    src="@/assets/images/exam/wrong.png"
                    alt=""
                  />
                  <span style="color: #eb6661">{{ item.submitOptions }}</span>
                </div>
              </span>
            </div>
          </div>
          <!-- 解析 -->
          <div class="jiexi" v-if="item.analysis">
            <div><img src="@/assets/images/exam/bianji.png" alt="" /> 解析</div>
            <div style="margin-top: 6px">
              {{ item.analysis }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right_top">
          <div>
            答题卡
            <div class="text_bottom"></div>
          </div>
          <div
            :style="{ color: $store.state.buttonColor }"
            v-if="data.errorAnswerCount > 0"
          >
            答错{{ data.errorAnswerCount }}题
          </div>
        </div>
        <div class="line"></div>
        <div class="right_content">
          <div
            v-for="(item, index) in datika"
            @click="scrollTops(index + 1)"
            :key="index"
          >
            <div
              :class="[
                check == item.paperQuestionSort
                  ? 'checkactive'
                  : item.correctType == 1
                  ? 'resultActive'
                  : item.correctType == 2
                  ? 'resultActive1'
                  : '',
              ]"
            >
              {{ item.paperQuestionSort }}
            </div>
          </div>
        </div>
        <div class="btn">
          <el-button type="primary" v-if="data.isExam && source" @click="Next"
            >再考一次</el-button
          >
        </div>
      </div>
      <div class="zhiding" @click="toTop">
        <img src="@/assets/images/exam/zhiding.png" alt="" />
      </div>
    </span>

    <div class="content_noJiexi" v-else>
      <div class="top_img" :class="[top ? 'topTrue' : 'topFalse']">
        <div class="top_img_title">
          <img src="@/assets/images/exam/header.png" alt="" />{{
            data.userName
          }}的成绩单
        </div>
        <div class="top_img_content">
          <div v-if="data.thisExamScore">
            {{ data.thisExamScore }}<span>（分）</span>
          </div>
          <div>{{ data.title }}</div>
        </div>
      </div>
      <div class="content_noJiexi_content">
        <el-button type="primary" v-if="data.isExam && source" @click="Next"
          >再考一次</el-button
        >
      </div>
    </div>
  </div>

</template>

<script >
import {recordView} from "@/request/service.js"
import { hexToRgb } from "@/utils";
import html2canvas from "html2canvas";
import * as echarts from "echarts";
export default {
  name: "examPreviewPc",
  data() {
    return {
      top: true,
      data: {},
      datika: [],
      tiList: [],
      check: 0,
      h: "",
      m: "",
      s: "",
      scroll: false,
      show: false,
      showData: {},
      source: "",
      lineOptions: {},
    };
  },
  computed: {

  },
  async mounted() {
    let params={
      userId:this.$route.query.userId,
      userName:this.$route.query.userName,
      examId:this.$route.query.examId,
      examUserId:this.$route.query.examUserId
    }
     let res = await recordView(params)
     this.data = res.data.examResult
     this.getDate(res.data.examAnalysis)
     this.datika = res.data.answerList
     window.addEventListener("scroll", this.handleScroll, true);
    //  this.init();
    //   this.initChat();
  },
  methods: {
    // 折线图
    initChat() {
      setTimeout(() => {
        const linesChatElement = document.getElementById("lineChat");
        if (linesChatElement) {
          var myChart = echarts.init(linesChatElement, null, {
            renderer: "svg",
          });
          myChart.setOption(this.lineOptions);
        }
      }, 1000); // 等待1秒后再次尝试获取元素
    },
    themeBg() {
      return hexToRgb('#409EFF', 0.3);
    },
    takephotos() {
      let save2 = document.getElementById("zhengshu");
      html2canvas(save2, {
        scale: 2, // 处理模糊问题
        dpi: 300, // 处理模糊问题
      }).then((canvas) => {
        // let url = canvas.toDataURL("image/png");
        // let aLink = document.createElement("a");
        // aLink.style.display = "none";
        // aLink.download = "完课证书"; //文件名
        // document.body.appendChild(aLink);
        // aLink.href = url;
        // aLink.click(); // 触发点击
        // document.body.removeChild(aLink); // 然后移除
        this.convertCanvasToImg(canvas);
      });
    },

    //转换图片格式
    convertCanvasToImg(canvas) {
      // canvas base64 转 blob
      var myBlob = this.dataURLtoBlob(canvas.toDataURL("img/png", 0.92));
      // blob转URL对象
      var myUrl = URL.createObjectURL(myBlob);
      // 创建a标签，下载图片
      this.downImg(myUrl);
    },

    //base64 转 blob
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },

    //下载图片 filename是图片名称
    downImg(url) {
      // 创建a标签 并设置其相关属性，最后触发其点击事件
      let a = document.createElement("a");
      let clickEvent = document.createEvent("MouseEvents");
      a.setAttribute("href", url);
      a.setAttribute("download", "filename");
      a.setAttribute("target", "_blank");
      clickEvent.initEvent("click", true, true);
      a.dispatchEvent(clickEvent);
    },

    del() {
      this.show = false;
    },
    Next() {
      let params = {
        type: 3,
        examId: this.$route.query.examId,
      };
      this.$router.push({
        path: "examinationDetail",
        query: {
          examId: this.$route.query.examId,
          type: 3,
          pcView: this.$route.query.pcView,
          title: this.data.title,
          isTime: this.$route.query.isTime,
          answerListShow:this.$route.query.answerListShow,
          nextStatus:this.$route.query.nextStatus
        },
      });
    },
    // scrollChange() {
    //   let li = document.getElementsByClassName("left_content");
    //   let checks;
    //   if (
    //     li[li.length - 1] &&
    //     li[li.length - 1].offsetTop >=
    //       document.documentElement.scrollTop + 100 &&
    //     this.check == li.length
    //   ) {
    //     checks == li.length;
    //   } else {
    //     for (var j = 0; j < li.length; j++) {
    //       if (document.documentElement.scrollTop + 100 >= li[j].offsetTop) {
    //         checks = j + 1;
    //       }
    //     }
    //   }
    //   this.check = checks;
    // },
    handleScroll() {
      let scrolltop =
        document.documentElement.scrolltop || document.body.scrollTop;
      scrolltop > 30 ? (this.gotop = true) : (this.gotop = false);
    },
    init() {
      let params = {
        examId: this.$route.query.examId,
        examUserId: this.$route.query.examUserId,
      };
      this.$api.exam.examResult(params).then((res) => {
        this.data = res.data;
        if (res.data.statisticsType == 2 && res.data.abscissas.length >= 2) {
          this.lineOptions = {
            color: ['#409EFF'],
            xAxis: {
              type: "category",
              data: res.data.abscissas,
            },

            grid: {
              top: 20, // 上边距
              bottom: 20, // 下边距
            },
            yAxis: {
              type: "value",
            },
            series: [
              {
                data: res.data.scores,
                type: "line",
                label: {
                  show: true,
                  position: "top",
                  formatter: function (params) {
                    return "{custom|" + params.value + "}{custom|分}";
                  },
                  rich: {
                    custom: {
                       color: '#409EFF',
                      fontSize: 12,
                      fontWeight: "bold",
                    },
                    unit: {
                      color: "#0000ff",
                      fontSize: 12,
                    },
                  },
                },
              },
            ],
          };
        }
        if (res.data.statisticsType == 3 || res.data.statisticsType == 4) {
          let data = res.data.coordinateList.map((element) => {
            return {
              name: element.labelName + `:${element.labelScore}分`,
              max: element.labelScore,
            };
          });
          let dataValue = this.transformData(res.data.userLabelScoreList);
          this.lineOptions = {
            legend: {
                show:true,
                bottom:'bottom',
            },
            radar: {
              // shape: 'circle',
              indicator: data,
              center: ["50%", "50%"],
              radius: [10, "64%"], // 调整雷达图的大小
              axisName: {
                formatter: (value) => {
                  let t;
                  t = value.split(":")[0];
                  if (t.length > 4) {
                    let result = "";
                    for (let i = 0; i < t.length; i++) {
                      result += t[i];
                      if ((i + 1) % 4 === 0) {
                        result += "\n";
                      }
                    }
                    t = result;
                  } else {
                    t = t;
                  }
                  let v = value.split(":")[1];
                  return "{w|" + t + "}\n{custom|" + v + "}";
                },
                rich: {
                  custom: {
                    color: "#3493EF",
                    fontSize: 12,
                    fontWeight: "bold",
                    align: "center",
                    width: 30,
                  },
                  w: {
                    align: "center", // 设置文本宽度
                    color: "#666666",
                  },
                },
              },
            },
            series: [
              {
                name: "Budget vs spending",
                type: "radar",
                areaStyle: {
                  opacity: 0.2,
                },
                data: dataValue,
              },
            ],
          };
        }
        if (localStorage.getItem("taskId")) {
          let data = {
            taskId: localStorage.getItem("taskId"),
          };
          this.$api.exam.taskComplete(data).then((resp) => {
            if (resp.data) {
              this.show = true;
              this.showData = resp.data;
            }
          });
        }
      });

      let params1 = {
        examUserId: this.$route.query.examUserId,
        pageIndex: 0,
        pageSize: this.datika.length,
        type: 2,
      };
      this.$api.exam.examResul(params1).then((res) => {
        this.getDate(res.data);
      });
    },
    transformData(userLabelScoreList) {
      const data = userLabelScoreList.map((itemList, index) => {
        let value;
        value = itemList.map((e) => {
          return e.userLabelScore;
        });
        return { name: `第${index + 1}次`, value: value };
      });
      return data;
    },
    //   获取当前题
    getDate(data) {
      data.forEach((element) => {
        element.options = JSON.parse(element.options);
        if (element.submitOptions && element.submitOptions !== "null") {
          if (element.answer) {
            element.answer = JSON.parse(element.answer);
            element.answer = element.answer.join("");
          }
          element.submitOptions.forEach((row) => {
            element.options.forEach((e) => {
              if (row == e.option) {
                e.isSelected = true;
              }
            });
          });
          element.submitOptions = element.submitOptions.join("");
        }
      });
      this.tiList = data;
    },

    // 选中
    checks(id, item) {
      this.tiList.forEach((element, index) => {
        if (id == element.id) {
          element.sel.forEach((row) => {
            if (element.type == "singe") {
              if (row.key == item.key) {
                row.check = true;
                this.tiList[index].result.push(item.key);
              } else {
                row.check = false;
              }
            } else {
              if (row.key == item.key) {
                if (row.check == true) {
                  row.check = false;
                  this.tiList[index].result.splice(
                    this.tiList[index].result.indexOf(item.key),
                    1
                  );
                } else {
                  row.check = true;
                  this.tiList[index].result.push(item.key);
                }
              }
            }
          });
        }
      });
    },
    // 选中滑动
    // 选中滑动
    scrollTops(index) {
      // var that=this
      this.scroll = true;

      this.check = index;
      let i = `li${index}`;
      let target = document.getElementsByClassName(i)[0];
      $("#scrollBlock").animate({ scrollTop: target.offsetTop - 84 }, 0);
    },
    toTop() {
      let top = document.getElementsByClassName('containerss')[0].scrollTop
      //实现滚动效果
      const timeTop = setInterval(() => {
          document.getElementsByClassName('containerss')[0].scrollTop =
          top -=
            50;
        if (top <= 0) {
          clearInterval(timeTop);
        }
      }, 10);
    },
  },
};
</script>

<style lang="scss" scoped>
#item {
  width: 5.45rem;
  height: 7.28rem;
  background: url("~@/assets/images/zhengshu.png") no-repeat;
  background-size: 100% 100%;
  padding: 0 0.26rem;
  .item_name {
    font-weight: 400;
    font-size: 0.3rem;
    color: #333333;
    letter-spacing: 0;
    margin-top: 2.3rem;
  }
  .item_time {
    font-weight: 400;
    font-size: 0.22rem;
    color: #999999;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 0.26rem;
  }
  .item_zhiwei {
    font-weight: 400;
    font-size: 0.22rem;
    color: #999999;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 0.46rem;
  }
  .item_title {
    font-weight: 500;
    letter-spacing: 0;
    text-align: center;
    height: 1.35rem;
    align-items: center;
    margin-bottom: 0.24rem;
    justify-content: center;
    font-size: 0.32rem;
    color: #333333;
    line-height: 1.1;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .item_titlea {
    font-weight: 400;
    font-size: 0.22rem;
    color: #333333;
    letter-spacing: 0;
    position: absolute;
    bottom: 1.1rem;
    width: calc(100% - 0.52rem);
    text-align: center;
  }
  .item_titleb {
    font-weight: 400;
    font-size: 0.2rem;
    color: #999999;
    letter-spacing: 0;
  }
}
.exam_box{
  width: 100%;
  display: flex;
  justify-content: center;
  overflow: auto;
}
.containerss {
  min-height: 100vh;
  width: 1180px;
  height: 100px;
  padding:0 calc((100% - 1180px)/2);
  margin: 20px 0;
  position: relative;
  letter-spacing: 1px;
  overflow: auto;

  .bottom_practice{
  width: 100%;
  background: #FFFFFF;
  padding: 12px 0 21px;
  margin: 0 auto 20px;
  .bg{
     width: 850px;
    height: 72px;
    padding: 0 282px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    background: url("~@/assets/images/exam/bg_Pc.png") no-repeat;
    background-size: 100% 100%;
    >div{
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      .fc{
        color: #3A97EE !important;
        span{
          position: relative;
        }
        span::after{
          content:"";
          width: 40px;
          height: 9px;
          opacity: 0.42;
          bottom: 4px;
          left: -5px;
          position: absolute;
          background-image: linear-gradient(270deg, #ffffff00 0%, #3997EE 100%);
          border-radius: 7px;
          display: inline-block;
        }
      }
      div:first-child{
        font-weight: 600;
        font-size: 24px;
        color: #000000;
      }
      div:last-child{
        font-weight: 400;
        font-size: 16px;
        color: #999999;
        margin-top: 5px;
      }
    }
  }
}
  .nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 21px 37% 21px 15%;
    position: fixed;
    top: 0;
    left: 0;
    background: #fff;
    > div:first-child {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
    .time {
      display: flex;
      align-items: center;
    }
    img {
      width: 26px;
      height: 26px;
    }
  }
  .content_noJiexi {
    width: 100%;
    padding-bottom: 30px;
    .topTrue {
      background: url("~@/assets/images/exam/top1.png") no-repeat;
      background-size: 100% 100%;
    }
    .topFalse {
      background: url("~@/assets/images/exam/top2.png") no-repeat;
      background-size: 100% 100%;
    }
    .top_img {
      width: 100%;
      padding: 18px 29px 20px;
      color: #fff;
      &_title {
        color: #fff;
        font-size: 16px;
        display: flex;
        font-weight: 400;
        align-items: center;
        img {
          width: 22px;
          height: 22px;
          margin-right: 5px;
        }
      }
      &_content {
        text-align: center;
        > div:first-child {
          font-size: 36px;
          font-weight: 700;
          span {
            font-size: 14px;
          }
        }
        > div:nth-child(2) {
          font-size: 20px;
          font-weight: 700;
        }
        > div:last-child {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
    &_content {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      background: #fff;
    }
  }
  .left {
    width: 880px;
    padding-bottom: 30px;
    border-radius: 4px;
    overflow: hidden;
    .zong {
      background: #fff;
      margin-bottom: 20px;
      padding: 14px 32px 20px;
      .zong_left {
        width: 100%;
        .img {
          height: 20px;
          display: flex;
          align-items: center;
          font-weight: 600;
        }
        img {
          width: 19px;
          height: 20px;
          border-radius: 4px;
          margin-right: 6px;
        }
        #linesChat {
          width: 100%;
        }
      }
      .zong_right_w {
        width: 100% !important;
      }
      .zong_right {
        width: 100%;
        margin-top: 20px;
        .r_item {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          letter-spacing: 1px;
          line-height: 20px;
          display: flex;
          margin-bottom: 10px;
          > div:first-child {
            width: 20px;
            margin-right: 6px;
          }

          img {
            width: 20px;
            height: 20px;
            margin-right: 6px;
          }
          .discription {
            width: calc(100% - 20px - 6px);
          }
        }
      }
    }
    .mx-1 {
      color: #409EFF !important;
      border-color: #409EFF !important;
    }
    .topTrue {
      background: url("~@/assets/images/exam/top1.png") no-repeat;
      background-size: 100% 100%;
    }
    .topFalse {
      background: url("~@/assets/images/exam/top2.png") no-repeat;
      background-size: 100% 100%;
    }
    .top_img {
      width: 100%;

      padding: 18px 29px 20px;
      color: #fff;
      &_title {
        color: #fff;
        font-size: 16px;
        display: flex;
        font-weight: 400;
        align-items: center;
        img {
          width: 25px;
          height: 25px;
          margin-right: 5px;
        }
      }
      &_content {
        text-align: center;
        > div:first-child {
          font-size: 36px;
          font-weight: 700;
          span {
            font-size: 14px;
          }
        }
        > div:nth-child(2) {
          font-size: 20px;
          font-weight: 700;
        }
        > div:last-child {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
    &_content {
      padding: 19px 20px;
      background: #fff;
      box-sizing: border-box;
      margin-bottom: 15px;
      border-radius: 4px;
      .question {
        margin-top: 10px;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 10px;
      }
      .active1 {
        color: #409EFF;
      }
      .active {
        border: solid 1px #409EFF !important;
        color: #409EFF;
      }
      .answer:hover {
        cursor: pointer;
      }
      .answer1 {
        // background: #eaf4fe;
        border-radius: 4px;
      }
      .answer {
        display: flex;
        font-size: 16px;
        align-items: center;
        margin-bottom: 10px;
        line-height: 20px;

        > div:first-child {
          width: 25px;
          height: 25px;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: solid 1px #d2d2d2;
          margin-right: 10px;
          margin-top: 4px;
          letter-spacing: -1px;
        }
        > div:last-child {
          width: 90%;
        }
        padding: 10px 18px;
      }
      .answerResult {
        font-size: 14px;
        display: flex;
        background-image: linear-gradient(
          1deg,
          rgba(252, 252, 252, 0.09) 0%,
          #f6fbff 100%
        );
        border-radius: 3px;
        padding: 20px 20px;
        > div{
          display: flex;
          align-items: center;
        }
        > div:last-child {
          margin-left: 30px;
          display: flex;
          align-items: center;
          div {
            margin-left: 5px;
            display: flex;
            align-items: center;
            img {
              margin-right: 5px !important;
            }
          }
        }
      }
    }

    .jiexi {
      padding: 0 20px;
      color: #333333;
      font-size: 16px;

      img {
        width: 21px;
        height: 25px;
      }
      > div:first-child {
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
  }
  .right {
    width: 280px;
    padding: 14px 20px;
    background: #fff;
    position: fixed;
    left: calc((100vw - 1180px) / 2 + 900px);
    max-height: 500px;
    overflow: auto;
    top: 20px;
    .right_top {
      display: flex;
      justify-content: space-between;
      > div:first-child {
        position: relative;
        color: #333333;
        font-weight: 600;
        font-size: 16px;
        .text_bottom {
          position: absolute;
          width: 42px;
          bottom: 1px;
          height: 6px;
          z-index: -1;
          background-image: linear-gradient(
            -76deg,
            rgba(47, 146, 238, 0) 6%,
            #409EFF 90%
          );
          border-radius: 2px;
        }
      }
    }

    .right_content {
      display: flex;
      flex-wrap: wrap;
      letter-spacing: 0;
      justify-content: space-between;

      > div {
        width: 16.5%;
        display: flex;
        justify-content: center;

        > div:hover {
          cursor: pointer;
        }
        .checkactive {
          width: 28px;
          height: 28px;
          background: #f5f8fc;
          border: 1px solid #409EFF;
          border-radius: 50%;
          color: #409EFF;
        }
        .resultActive {
          background: #409EFF;
          color: #fff;
        }
        .resultActive1 {
          background: #ff7979;
          color: #fff;
        }
        > div {
          margin-bottom: 10px;
          width: 28px;
          height: 28px;
          background: #f5f8fc;
          font-size: 12px;
          color: #333333;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }
      }
    }
    .right_content:after {
      content: "";
      flex: auto;
    }
  }

  .zhiding {
    position: fixed;
    left: calc((100vw - 1180px) / 2 + 900px);
    top: 80%;
    z-index: 999;
    width: 30px;
    height: 30px;
  }
  .zhiding:hover {
    cursor: pointer;
  }
}
.fle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  font-size: 12px;
  border-radius: 50%;
  line-height: 12px;
}
.line {
  width: 100%;
  height: 1px;
  background: #e9e9e9;
  margin: 5px 0 10px;
}

.btn {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  ::v-deep .el-button {
    width: 100%;
    height: 38px;
    border-radius: 4px;
    margin: 5px 0;
    background: #409EFF;
    border: 1px solid #409EFF;
    color: #fff;
  }
}
</style>
<style lang="scss" scoped>
#zhengshu {
  width: 703px;
  height: 433px;
  background: #fff;
  background: url("~@/assets/images/zhengshu1.png") no-repeat;
  background-size: 100% 100%;
  text-align: center;
  position: relative;
}
.item_name {
  font-weight: 400;
  font-size: 18px;
  color: #333333;
  letter-spacing: 0;
  margin-top: 140px;
}
.item_time {
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  letter-spacing: 0;
  text-align: center;
}
.item_zhiwei {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  margin-bottom: 64px;
  height: 20px;
}
.item_title {
  font-weight: 500;
  font-size: 20px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  align-items: center;
  margin: 26px 0 6px;
  justify-content: center;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.1;
  padding: 0 30px;
}
.item_titlea {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  position: absolute;
  bottom: 74px;
  width: 100%;
  text-align: center;
}
.item_titleb {
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  letter-spacing: 0;
  position: absolute;
  bottom: 30px;
  width: 100%;
  text-align: center;
}
.del {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  img {
    width: 22px;
    height: 22px;
  }
  img:hover {
    cursor: pointer;
  }
  margin-bottom: 16px;
}
.van-popup {
  background: none;
  display: flex;
  flex-direction: column;
}
.el-button {
  width: 104px;
  height: 38px;
  margin: 43px auto;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
}
</style>