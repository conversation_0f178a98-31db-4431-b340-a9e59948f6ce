<template>
  <div class="yxd_reportcase">
    <div id="container" class="container">
      <el-tabs v-model="activeName" @tab-click="change">
        <el-tab-pane  label="考试管理"  v-if="btnList.examination" name="first">
          <div class="yxd_report_case1" :type="activeName">
            <add-exam v-if="$route.query&&$route.query.type=='addAdnEdit'" @sub="sub"></add-exam>
            <search-exam v-else-if="$route.query&&$route.query.type=='search'" @sub="sub"></search-exam>
            <add-exam-scale v-else-if="$route.query&&$route.query.type=='addAdnEditScale'" @sub="sub"></add-exam-scale>
            <exam @types="types" v-else></exam>
          </div>
        </el-tab-pane> 
        <el-tab-pane  label="试卷管理" name="second" v-if="btnList.examination_paper">
          <div class="yxd_report_case2" :type="activeName">
            <add-paper v-if="$route.query&&$route.query.type=='add1'" @sub="sub"></add-paper>
            <add-practice v-else-if="$route.query&&$route.query.type=='add5'" @sub="sub"></add-practice>
            <add-paper-suiji v-else-if="$route.query&&$route.query.type=='add2'" @sub="sub"></add-paper-suiji>
            <add-paper-practice v-else-if="$route.query&&$route.query.type=='add4'" @sub="sub"></add-paper-practice>
            <add-paper-scale v-else-if="$route.query&&$route.query.type=='add3'" @sub="sub"></add-paper-scale>
            <test-paper @types="types" @edit="editPaper" v-else></test-paper>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="btnList.question_bank" label="题库管理" name="third">
          <div class="yxd_report_case3" :type="activeName">
            <question-bank ref="questionBank"></question-bank>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import exam from "@/components/Examination/exam.vue";
import testPaper from "@/components/Examination/testPaper.vue";
import addExam from "@/components/Examination/addExam.vue";
import searchExam from "@/components/Examination/searchExam.vue";
import addPaper from "@/components/Examination/addPaper.vue";
import addPaperSuiji from "@/components/Examination/addPaperSuiji.vue";
import addPaperScale from "@/components/Examination/addPaperScale.vue";
import addPaperPractice from "@/components/Examination/addPaperPractice.vue";
import addExamScale from "@/components/Examination/addExamScale.vue";
import addPractice from "@/components/Examination/addPractice.vue";

import questionBank from "@/components/Examination/questionBank.vue";
export default {
  name: "examination",
  mixins: [initModule, initScroll],
  components: {
    // exam,
    testPaper,
    questionBank,
    addPaper,
    exam,
    addExam,
    searchExam,
    addPaperSuiji,
    addPaperScale,
    addPaperPractice,
    addExamScale,
    addPractice
  },
  data() {
    return {
      activeName: "first",
      secondStatus:false,
      type:"list"
    };
  },
  computed:{
    // activeName:function(){
    //   if(!this.btnList.survey_list&&this.btnList.case_list){
    //      return  "first"
    //   }else if(!this.btnList.survey_list&&!this.btnList.case_list){
    //      return  "third"
    //   }
    //   return "second"
    // }
  },

  mounted() {
    if(JSON.stringify(this.btnList)!='{}'){
      if(this.btnList.examination){
      this.activeName="first"
      }
      if(!this.btnList.examination&&this.btnList.examination_paper){
        this.activeName="second"
      }
      if(!this.btnList.examination&&!this.btnList.examination_paper&&btnList.question_bank){
        this.activeName="third"
      }
    }
    
    if(this.$route.query){
      if(this.$route.query.type=="addAdnEdit"||this.$route.query.type=="search"){
        this.activeName="first"
      }
      else if(this.$route.query.type=="add1"||this.$route.query.type=="add2"||this.$route.query.type=="add3"){
        this.activeName="second"
      }
    }
  },
  methods: {
    change(){
      this.$refs.questionBank.goTopicBank()
    },
    types(e,row,data){
      if(row=="searchSet"){
        this.$router.push({path:'/exam',query:{type:e,searchSet:row,id:data}})
      }
      if(e=='search'){
         this.$router.push({path:'/exam',query:{type:e,data:encodeURI(JSON.stringify(row))}})
      }
      if(e!='search'&&row&&row.id){
      this.$router.push({path:'/exam',query:{type:e,id:row.id}})
      }
      if(e&&!row&&!data){
        this.$router.push({path:'/exam',query:{type:e}})
      }
    },
    editPaper(row,mode){
      if(mode==1){
       this.$router.push({path:'/exam',query:{id:row.id , title:row.title,type:'add3'}})
      }else if(mode==2){
        this.$router.push({path:'/exam',query:{id:row.id , title:row.title,type:'add4'}})
      }else if(mode==3){
        this.$router.push({path:'/exam',query:{id:row.id , title:row.title,type:'add5'}})
      }else{
         if(row.type==1){
       this.$router.push({path:'/exam',query:{id:row.id , title:row.title,type:'add1'}})

      }else{
        this.$router.push({path:'/exam',query:{id:row.id , title:row.title,type:'add2'}})
      }
      }
     

    },
    sub(e){
      this.$router.push({path:'/exam',query:{type:e}})
    }
  },
};
</script>

<style scoped lang="scss">
.yxd_reportcase {
  .yxd_report_case1 {
    .table {
      cursor: pointer;
    }
  }
  .yxd_report_case2 {
  }
  .yxd_report_case3 {
  }
}
</style>
