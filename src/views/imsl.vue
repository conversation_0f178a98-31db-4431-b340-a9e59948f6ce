<template>
  <div class="containers">
    <el-tabs type="border-card">
      <el-tab-pane label="iMSL设置">
          <imsl-set></imsl-set>
      </el-tab-pane>
      <el-tab-pane label="会话记录">
          <imsl-list></imsl-list>
      </el-tab-pane>
      <el-tab-pane label="知识库管理">
          <knowledge></knowledge>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import  knowledge from "@/components/imsl/imslkonw.vue"
import  imslSet from "@/components/imsl/imslSet.vue"
import  imslList from "@/components/imsl/imslList.vue"
export default {
  data() {
    return {
      tableData:[
          {id:1,name:"测",question:"阿叔u爱仕达UI大海对啊卜",time:"2020-2121"},
           {id:1,name:"测",question:"阿叔u爱仕达UI大海对啊卜",time:"2020-2121"},
            {id:1,name:"测",question:"阿叔u爱仕达UI大海对啊卜",time:"2020-2121"}
      ]
    };
  },
  components:{
      knowledge,
      imslSet,
      imslList
  }
};
</script>

<style lang="scss" scoped>
.containers {
  padding-bottom: 20px;
  .search {
    background: #f8f8f8;
    padding: 10px 20px;
    margin-top: 30px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    .s_content {
      display: flex;
      align-items: flex-end;
      background: #fff;
      padding: 10px 20px;
      width: 100%;
      justify-content: space-between;
      flex-wrap: nowrap;
      .s_item {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }
      .search_btn {
        width: 130px;
      }
      ::v-deep .el-button {
        width: 60px;
        height: 20px !important;
        font-size: 12px;
        text-align: center;
        padding: 0;
      }
      ._mb {
        margin-right: 10px;
      }
    }
  }
  
}
</style>