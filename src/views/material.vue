<template>
  <div class="yxd_information">
    <material-index ref="material" @go="goEdit" v-if="isIndex"></material-index>
    <material-create
      :edit="current"
      @back="goBack"
      v-else
    ></material-create>
  </div>
</template>

<script>
import materialIndex from "@/components/material/index";
import materialCreate from "@/components/material/create";
export default {
  name: "material",
  components: {
    materialIndex,
    materialCreate,
  },
  data() {
    return {
      isIndex: true,
      current: {},
    };
  },
  mounted() {},
  methods: {
    goEdit(obj) {
      this.isIndex = false;
      this.current = obj;
    },
    goBack(){
      this.isIndex = true;
      this.$refs.material.init()
    }
  },
};
</script>

<style scoped lang="scss">
.yxd_information {
}
</style>
