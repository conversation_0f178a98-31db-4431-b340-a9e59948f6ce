<template>
  <div class="yxd_group">
    <group-index v-if="isIndex" @edit="editManage" :accountType="accountType"></group-index>
    <group-detail
      :info="currentData"
      :accountType="accountType"
      @back="backManage"
      v-else
    ></group-detail>
  </div>
</template>

<script>
import groupIndex from "@/components/Group/index";
import groupDetail from "@/components/Group/detail";
export default {
  name: "group",
  components: {
    groupIndex,
    groupDetail,
  },
  data() {
    return {
      isIndex: true,
      currentData: [],
      accountType: 1
    };
  },
  mounted() {},
  methods: {
    editManage(item) {
      this.currentData = item;
      this.isIndex = false;
    },
    backManage() {
      this.isIndex = true;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_group {
}
</style>
