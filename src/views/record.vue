<template>
  <div class="yxd_comment">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="keyword" suffix-icon="el-icon-search" placeholder="输入姓名查询" @keydown.enter="init('inquire')"></el-input>
          </div>
          <div class="col">
            <el-input v-model="mobile" placeholder="请输入手机号码" @keydown.enter="init('inquire')"></el-input>
          </div>
          <div class="col">
            <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <div class="col">
            <el-button icon="el-icon-search" @click="init('inquire')">查询</el-button>
            <el-button  @click="reset">重置</el-button>
            <el-button type="primary" v-if="btnList.artificial_send" @click="reissueDialog">补发</el-button>
            <el-button type="primary" v-if="btnList.intergral_log_export" @click="deriveData">导出</el-button>
          </div>
        </div>
      </div>

      <div class="table">
        <el-table
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column
            prop="userName"
            label="用户名"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="realName"
            label="真实姓名"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="roleName"
            label="用户组"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="type"
            label="类型"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="taskId"
            label="任务ID"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="taskName"
            label="任务名称"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="taskType"
            label="任务类型"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column
            :formatter="formatObjectType"
            prop="taskModule"
            label="任务模块"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="moduleId"
            label="模块ID"
            sortable
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="integral"
            label="梅花数量"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="操作时间"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="lastUme"
            label="梅花余额"
            align="center"
          >
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination" v-if="tableData.length>0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <!-- 补发弹框 -->
      <el-dialog title="人工补发积分" v-model="reissueVisible" width="30%" :before-close="resetReissue">
         <el-form
            ref="formReissue"
            label-width="100px"
            :model="formReissue"
            style="max-width: 460px"
            :rules="rules"
          >
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="formReissue.mobile"/>
            </el-form-item>
            <el-form-item  label="用户姓名" prop="name">
              <el-input disabled v-model="formReissue.name" />
            </el-form-item>
            <el-form-item label="补发数量" prop="ReissueAmount">
               <el-input-number :precision="0" v-model="formReissue.ReissueAmount" controls-position="right"  :min="1" :max="10000"></el-input-number>
            </el-form-item>
            <el-form-item label="补发原因" prop="ReissueAmountCause">
              <el-select v-model="formReissue.ReissueAmountCause" placeholder="请选择">
                <el-option label="社群发言" :value="'社群发言'"></el-option>
                <el-option label="有处方行为" :value="'有处方行为'"></el-option>
                <el-option label="其他" :value="'其他'"></el-option>
              </el-select>
            </el-form-item>  
            <el-form-item v-if="formReissue.ReissueAmountCause==='其他'" prop="cause">
                <el-input  type="textarea" placeholder="请输入补发原因" v-model="formReissue.cause"></el-input>
            </el-form-item>
          </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="resetReissue" 
              >取 消</el-button
            >
            <el-button type="primary" @click="reissueSave" 
              >提 交</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import { integralList, batchDealComment , getUserList , artificialSend} from "@/request/service.js";
import { integralExport} from "@/request/formdata.js";
import classify  from  '@/constant/classify'
import { validPhone } from '@/utils/validate.js'
const { classifyType } = classify
export default {
  name: "record",
  mixins: [initModule, initScroll],
  data() {
    let validateMobile = (rule, value, callback) => {
       if(!value){
        return callback(new Error('手机号码不能为空'));
       }else if(!validPhone(value)){
        return callback(new Error('请输入正确的11位手机号码！'));
       }else{
        callback()
        let params = {
        mobile: this.formReissue.mobile,
        pageIndex: 1,
        pageSize: 20,
        realName:'',
        roleId:0,
        userName:'',
        };
        getUserList(params).then((res) => {
          if(res.status===200){
            this.formReissue.name = res.data[0].userName
            this.formReissue.userId = res.data[0].userId
          }else{
            this.formReissue.name=''
            this.formReissue.userId=''
          }
        })
       }
    }
    return {
      keyword: "",
      time: "",
      deleteVisible: false,
      currentChose: {},
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      mobile:"",
      reissueVisible:false,
      formReissue:{
        mobile:'',
        name:'',
        ReissueAmount:1,
        ReissueAmountCause:'',
        cause:'',
        userId:''
      },
      rules: {
      mobile: [
         {required: true, validator: validateMobile,  trigger: 'blur' },
      ],
      name:[
         {required: true , message: '请输入姓名'},
      ],
      ReissueAmount:[
         {required: true},
      ],
      ReissueAmountCause:[
         {required: true ,  message: '请选择补发原因', trigger: 'change'},
      ],
      cause:[
          { required: true, message: '请输入补发原因', trigger: 'blur' },
          { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
      ],
    }
    };
  },
  watch: {
    'formReissue.ReissueAmountCause':{
      handler(newValue,oldValue){
          this.formReissue.cause=''
      },
      innediate:true
     },
  },
  mounted() {
    this.init();
  },
  methods: {
    //  格式化模块类型
    formatObjectType(row, column, cellValue){
       const obj = classifyType.find(item=>item.name === cellValue)
       return obj ? obj.value :''
    },
    init(i) {
      if(i){
        if(this.keyword.length===1){
        this.$message.error("真实姓名不能小于2个字符");
         return
      }
      }
      let beginTime;
      let endTime;
      if (this.time && this.time.length > 0) {
        beginTime = this.time[0];
        endTime = this.time[1];
      }
      let params = {
        mobile: this.mobile,
        keyword: this.keyword,
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        beginDate: beginTime,
        endDate: endTime,
      };
      integralList(params).then((res) => {
        if (res.status == 200) {
          this.tableData =res.data;
          this.total = res.totalSize;
          this.initScroll();
          this.tableData.forEach((val) => {
            if (val.hasAttachment) {
              val.hasAttachment = "是";
            } else {
              val.hasAttachment = "否";
            }
          });
        }
      });
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset(){
      this.keyword = "";
      this.mobile = "";
      this.time = "";
    },
    deriveData(){
      let beginTime;
      let endTime;
      if (this.time && this.time.length > 0) {
        beginTime = this.time[0];
        endTime = this.time[1];
      }
      let params = {
        mobile: this.mobile,
        keyword: this.keyword,
        pageIndex: -1,
        pageSize: -1,
        beginDate: beginTime,
        endDate: endTime,
      }
      // if(this.mobile){
      //   params.mobile = this.mobile
      // }
      // if(this.keyword){
      //   params.keyword = this.keyword
      // }
      integralExport(params).then((res) => {
        if (res.size > 57) {
          let name = `查询数据详情.xlsx`
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
        } else {
          this.$message.warning("暂无数据导出");
        }
      });
    },
    // 补发弹框
    reissueDialog(){
      this.reissueVisible = true
      this.$refs['formReissue'].clearValidate()
    },
    // 人工补发积分提交
    reissueSave(){
       this.$refs['formReissue'].validate((valid)=>{
        if(valid){
          let params = {
            integral: this.formReissue.ReissueAmount,
            mobile: this.formReissue.mobile,
            reason: this.formReissue.ReissueAmountCause,
            userId: this.formReissue.userId,
          };
          if(this.formReissue.ReissueAmountCause==='其他'){
              params.reason = this.formReissue.cause
          }
          artificialSend(params).then((res) => {
            if (res.status == 200) {
              this.formReissue={
                  mobile:'',
                  name:'',
                  ReissueAmount:1,
                  ReissueAmountCause:'',
                  cause:'',
                  userId:''
              },
              this.reissueVisible = false
              this.init()
              this.$message.success("补发成功");
            }
          });
          
        }
       })
    },
  resetReissue(){
    this.formReissue={
        mobile:'',
        name:'',
        ReissueAmount:1,
        ReissueAmountCause:'',
        cause:'',
        userId:''
    },
    
    this.reissueVisible = false
  }
  },
};
</script>

<style scoped lang="scss">
.yxd_comment {
  .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      flex:1;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
      display: flex;
      // width: 500px;
      .tl{
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 40px;
          margin-right: 20px;
          white-space: nowrap;
      }
    }
  }
  .operate {
    .right{
      flex: 1;
      &>button{
         float: right;
      }
    }
  }
  .total{
    margin-top: 30px;
  }
  .notable{
    margin-top: 10px;
    ::v-deep{
      .el-table--fit,.el-table::before{
        width: 0px;
      }
      // .el-table__header-wrapper{
      //   height: 45px;
      // }
      // .el-table__header{
      //   height: 45px;
      // }
    }
      display: flex;
      flex-direction: column;
      justify-content:center;
      align-items: center;
    .content{
      width: 300px;
      height: 300px;
      display: flex;
      flex-direction: column;
      justify-content:center;
      align-items: center;
      button{
        margin-top: 20px;
      }
    }
  }
}
</style>
