<template>
  <div class="yxd_reportcase">
    <div id="container" class="container">
      <el-tabs v-model="activeName">
        <el-tab-pane v-if="btnList.case_list" label="病例征集" name="first">
          <div class="yxd_report_case1" :type="activeName">
            <case-1></case-1>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="btnList.survey_list" label="病例管理" name="second">
          <div class="yxd_report_case2" :type="activeName">
            <case-2></case-2>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane v-if="btnList.post_list" label="帖子管理" name="third">
          <div class="yxd_report_case3" :type="activeName">
            <case-3></case-3>
          </div>
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import Case1 from "@/components/ReportCase/case1.vue";
import Case2 from "@/components/ReportCase/case2.vue";
// import Case3 from "@/components/ReportCase/case3.vue";
export default {
  name: "reportCase",
  mixins: [initModule, initScroll],
  components: {
    Case1,
    Case2,
    // Case3,
  },
  data() {
    return {
      // activeName: "second",
    };
  },
  computed:{
    activeName:function(){
      if(!this.btnList.survey_list&&this.btnList.case_list){
         return  "first"
      }else if(!this.btnList.survey_list&&!this.btnList.case_list){
         return  "third"
      }
      return "second"
    }
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.yxd_reportcase {
  .yxd_report_case1 {
  margin-top: 20px;

    .table {
      cursor: pointer;
    }
  }
  .yxd_report_case2 {
  margin-top: 20px;

  }
  .yxd_report_case3 {
  margin-top: 20px;

  }
}
</style>
