<template>
  <div class="yxd_identity">
    <div class="container">
      <h3>登录背景</h3>
      <div class="word">可以上传一张项目相关的图片，在登录页面展示。</div>
      <div class="upload">
          <!-- 上传PC图片 -->
        <div>
          <div class="words">图片尺寸： 600px * 480px</div>
          <div class="pc">
             <div>上传PC图片：</div>
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :http-request="customRequest"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="cover" :src="cover" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </div>
        <!-- 上传H5图片 -->
        <div>
          <div class="words">图片尺寸： 750px * 350px(图片底部40px高度内不要放重要信息，会被遮挡)</div>
          <div class="h5">
            <div>上传H5图片：</div>
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :http-request="customRequestH5"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="h5cover" :src="h5cover" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </div>

      </div>
      <div class="footer">
        <el-button size="mini" @click="save" type="primary">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, onMounted, toRefs, ref } from "vue";
import { uploadAvatar } from "@/request/service.js";
import { ElMessage } from "element-plus";
import { uploadBackground, getBackground } from "@/request/service.js";
import store from "@/store/index";

export default {
  name: "identity",
  setup: () => {
    const cover = ref("");
    const h5cover = ref("");
    const state = reactive({});

    const beforeAvatarUpload = (file) => {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        ElMessage.error("上传头像图片大小不能超过 2MB!");
        return false;
      } else {
        return true;
      }
    };

    const filetoDataUrl = (file, callback) => {
      var reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file); //FileReader对象的方法，可以读取Blob或者File对象的数据，转化为dataURL格式
    };

    const customRequest = (item) => {
      filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            cover.value = res.data.url;
          }
        });
      });
    };
    const customRequestH5 = (item) => {
      filetoDataUrl(item.file, function(img) {
        let params = {
          base64: img,
        };
        uploadAvatar(params).then((res) => {
          if (res.status == 200) {
            h5cover.value = res.data.url;
          }
        });
      });
    };
    // 初始化
    const init = async () => {
      const res = await getBackground({
        projectId: store.state.projectInfo.id,
      });
      cover.value = res.data.url;
      h5cover.value = res.data.h5Url;
    };

    const save = async () => {
      await uploadBackground({
        url: cover.value,
        h5Url: h5cover.value,
        projectId: store.state.projectInfo.id,
      });
      ElMessage.success("保存成功");
    };

    onMounted(() => {
      init();
    });

    return {
      cover,
      h5cover,
      ...toRefs(state),

      beforeAvatarUpload,
      filetoDataUrl,
      customRequest,
      customRequestH5,
      save,
    };
  },
};
</script>

<style scoped lang="scss">
.container{
  padding-bottom: 20px;
}
.yxd_identity {
  h3 {
    margin-bottom: 30px;
  }
  .word {
    opacity: 1;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
  }

  .words {
    color: #666666;
    margin: 10px 0;
  }
}
::v-deep .avatar-uploader {
  .el-upload--text {
    width: 300px;
    height: 240px;
    line-height: 240px;
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.avatar {
  width: 100%;
  height: 100%;
}
.upload {
  display: flex;
  .pc{
    width: 600px;
    display: flex;
    align-items: center;
  }
  .h5{
    display: flex;
    align-items: center;
  }
}
</style>
