<template>
  <div class="yxd_itemset">
    <div class="container">
      <div style="display: flex">
        <div class="title" style="width: 13%;font-size:14px">个人中心图标设置</div>
        <div class="title" style="width: 13%"></div>
        <div class="title" style="width: 13%"></div>
        <div
          class="title"
          style="
            width: 61%;
            display: flex;
            justify-content: space-between;
            color: #909399;
            font-size: 14px;
          "
        >
          <span  style="font-size:14px">打开后，个人中心展示该图标入口；上下拖动可调整顺序</span>
          <el-button type="primary" size="medium" @click="submit">保存</el-button>
        </div>
      </div>

      <div class="table">
        <div v-for="(item, index) in tableData" :key="index">
          <div class="td1">{{ item.sort }}</div>
          <div class="td1">{{ item.name }}</div>
          <div class="td1">
            <el-switch v-model="item.status" active-value="0"
      inactive-value="1"> </el-switch>
          </div>
          <div class="td1 td2">
            <el-input
              type="text"
              placeholder="自定义图标文本"
              style="height: 30px; width: 30%"
              v-model="item.customName"
            >
            </el-input>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Sortable from "sortablejs";
import { reactive, onMounted, toRefs, ref, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { projectList , projectSave } from "@/request/service.js";
import store from "@/store/index";
let tableData = ref([]);
onMounted(async () => {
  rowDrop();
  await getList()
});
// 查询列表
const getList =async  ()=>{
    let params = {
        projectId:store.state.projectInfo.id,
        type:0
    }
    let res = await projectList(params)
    res.data.forEach(element => {
            element.status=JSON.stringify(element.status)
        });
        tableData.value = res.data
}
// 提交修改
const submit =async ()=>{
   let res =await projectSave({projectId:store.state.projectInfo.id,type:0,configs:[...tableData.value]})
   if(res.status==200){
       ElMessage.success("保存成功！");
   }
}
const rowDrop = () => {
  const tbody = document.querySelector(".table");
  Sortable.create(tbody, {
    onEnd: function (evt) {
      //拖拽结束发生该事件
      tableData.value.splice(
        evt.newIndex,
        0,
        tableData.value.splice(evt.oldIndex, 1)[0]
      );
        var newArray = tableData.value.slice(0);
        tableData.value = [];
        nextTick(() => {
            tableData.value = newArray;
            tableData.value.forEach((e,index)=>{
                e.sort=index+1
            })
        });
    },
  });
};
</script>

<style scoped lang="scss">
.yxd_itemset {
  .container {
    .word {
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #7f7f7f;
      // line-height: 16px;
    }
  }

  .table {
    width: 100%;
    margin-top: 0;
    > div {
      line-height: 45px;
      width: auto;
      display: flex;
      > div {
        width: auto;
        font-size: 14px;
        opacity: 0.8;
        text-align: center;
      }
      .td1 {
        width: 13%;
        text-align: center;
        vertical-align: middle;
      }

      .td2 {
        width: 61%;
        text-align: left;
        padding-left: 20px;
        box-sizing: border-box;
      }
    }
  }
}
.dialog-content {
  display: flex;
  span {
    width: 30%;
    line-height: 40px;
  }
  .el-input {
    width: 70%;
  }
}
.title {
  line-height: 40px;
  background: #f2f2f2;
  padding-left: 10px;
  box-sizing: border-box;
  font-size: 17px;
  width: 100%;
  margin-top: 20px;
}
</style>
