<template>
  <div class="yxd_itemset">
    <div class="container">
      <div class="word">
        项目设置是当前项目的全局设置，开关打开时，项目前台生效，关闭时项目前台失效。
      </div>
      <div class="title">
        <div class="d_block1">登录设置</div>
        <div class="d_block2"></div>
        <div class="d_block3"></div>
      </div>
      <table>
        <tr v-if="btnList.login_expire_time">
          <td class="td1">设置项目登录失效天数</td>
          <td class="td2">
            <el-select class="select" v-model="expireDay" placeholder="请选择">
              <el-option
                v-for="item in categoryList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                @click="setTokenDate"
              >
              </el-option>
            </el-select>
          </td>
          <!-- <td class="td3" v-show="!switch2">30天</td> -->
        </tr>
        <tr v-if="btnList.bound_wx">
          <td class="td1">隐藏顶部导航栏</td>
          <td class="td2">
            <el-switch @change="toolBarChange" size="large" v-model="switch9" />
          </td>
        </tr>
        <tr v-if="btnList.bound_wx">
          <td class="td1">绑定公众号</td>
          <td class="td2">
            <el-switch @change="wxOpen" size="large" v-model="switch6" />
          </td>
          <td class="td3">打开后，用户在微信中使用网站可以免登录</td>
        </tr>
      </table>
      <div class="title">
        <div class="d_block1">内容设置</div>
        <div class="d_block2"></div>
        <div class="d_block3"></div>
      </div>
      <table>
        <tr v-if="btnList.view_count_switch">
          <td class="td1">内容列表及详情页浏览量</td>
          <td class="td2">
            <el-switch @change="contentNews" size="large" v-model="switch1" />
          </td>
        </tr>
        <tr v-if="btnList.source_switch">
          <td class="td1">内容列表及详情页显示"来源"</td>
          <td class="td2">
            <el-switch @change="sourceChange" size="large" v-model="switch7" />
          </td>
        </tr>
        <tr v-if="DirectionalSwitch && btnList.role_menu_switch">
          <td class="td1">分用户组设置查看权限</td>
          <td class="td2">
            <el-switch @change="userSelePower" size="small" v-model="switch2" />
          </td>
          <td class="td3">打开后，需要给内容做分组权限设置</td>
        </tr>
        <tr v-if="btnList.recommend_switch">
          <td class="td1">相关推荐</td>
          <td class="td2">
            <el-switch @change="recommend" size="small" v-model="switch3" />
          </td>
          <td class="td3">
            打开后，详情页推荐tab展示跟当前内容关键词一致的内容
          </td>
        </tr>
        <tr v-if="btnList.home_switch">
          <td class="td1">仅配置一项分类时不显示首页</td>
          <td class="td2">
            <el-switch @change="setStatus" size="small" v-model="switch8" />
          </td>
          <td class="td3">
            打开后，用户访问该项目时默认展示此分类的列表页。
          </td>
        </tr>
        <tr>
          <td class="td1">是否隐藏积分商城</td>
          <td class="td2">
            <el-switch @change="IntegralMallStatus" size="small" v-model="switch10" />
          </td>
        </tr>
      </table>
      <div class="title">
        <div class="d_block1">积分设置</div>
        <div class="d_block2"></div>
        <div class="d_block3"></div>
      </div>
      <table>
        <tr v-if="btnList.integral_num_switch">
          <td class="td1">完成任务得积分</td>
          <td class="td2">
            <el-switch
              @click="setIntegralDialog"
              size="large"
              v-model="switch4"
            />
          </td>
        </tr>
        <tr v-if="btnList.integral_mall_switch">
          <td class="td1">项目积分商城</td>
          <td class="td2">
            <el-switch @change="storeOpen" size="large" v-model="switch5" />
          </td>
          <td class="td3">
            打开后，项目中产生的积分，仅限在本项目的积分商城中兑换
          </td>
        </tr>
      </table>
    </div>
    <!-- 积分弹框 -->
    <!-- 编辑弹出框 -->
    <el-dialog
      title="积分设置"
      v-model="IntegralDialog"
      width="30%"
      :before-close="IntegralSetCancel"
    >
      <div>
        <span>本项目积分预算为</span>&nbsp;
        <el-input-number
          v-model="usableIntegral"
          :precision="0"
          controls-position="right"
          :min="1"
          max="2147483647"
        ></el-input-number
        >&nbsp;
        <span>梅花</span>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="IntegralSetCancel" size="mini">取 消</el-button>
          <el-button type="primary" @click="savePower" size="mini"
            >提 交</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 微信公众号设置弹框-->
    <el-dialog
      title="公众号设置"
      v-model="wxDialog"
      width="30%"
      :before-close="wxCancel"
    >
      <div>
        <div class="dialog-content">
          <span><span style="color: red">*</span>本项目公众号绑定为</span>
          <el-input
            v-model.trim="wxData.appid"
            placeholder="请输入有效的公众号ID"
            controls-position="right"
          ></el-input>
        </div>
        <br />
        <div class="dialog-content">
          <span><span style="color: red">*</span>公众号秘钥为</span>
          <el-input
            v-model.trim="wxData.secret"
            placeholder="请输入对应的密钥"
            controls-position="right"
          ></el-input>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="wxCancel" size="mini">取 消</el-button>
          <el-button type="primary" @click="saveWxAppid" size="mini"
            >提 交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { reactive, onMounted, toRefs, ref } from "vue";
import { ElMessage } from "element-plus";
import {
  setDirectional,
  setViewCountShow,
  ViewCountShow,
  Directional,
  getTokenExpire,
  setTokenExpire,
  setRecommendStatus,
  RecommendStatus,
  directionalMenuStatus,
  setIntegralMenuStatus,
  IntegralMenuStatus,
  IntegralStatus,
  setIntegral,
  wxConfigAddWx,
  wxConfigDelWx,
  wxConfigGetWx,
  getSourceShow,
  setSourceShow,
  setHomeSettingStatus,
  getHomeSettingStatus,
  getToolBarStatus,
  setToolbarStatus,
  setIntegralMallStatus,
  getIntegralMallStatus
} from "@/request/service.js";
import store from "@/store/index";
const switch1 = ref(false);
const switch2 = ref(false);
const switch3 = ref(false);
const switch4 = ref(false);
const switch5 = ref(false);
const switch6 = ref(false);
const switch8 = ref(false);
const switch9 = ref(false);
const switch10 = ref(false);
const IntegralDialog = ref(false);
const wxDialog = ref(false);
const DirectionalSwitch = ref(true);
const expireDay = ref(30);
const usableIntegral = ref(0);
const executedIntegral = ref(0);
const switch7 = ref(false);
const wxData = reactive({
  appid: "",
  secret: "",
  id: 0,
});
const categoryList = ref([
  {
    label: "1天",
    value: 1,
  },
  {
    label: "3天",
    value: 3,
  },
  {
    label: "7天",
    value: 7,
  },
  {
    label: "30天",
    value: 30,
  },
  {
    label: "180天",
    value: 180,
  },
]);
// 内容列表及详情页浏览量开关
const contentNews = (v) => {
  let params = {
    type: switch1.value,
  };
  setViewCountShow(params).then((res) => {
    if (res.status == 200) {
      if (switch1.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 导航栏开关
const  toolBarChange= (v) => {
  let params = {
    type: switch9.value,
  };
  setToolbarStatus(params).then((res) => {
    if (res.status == 200) {
      if (switch9.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 来源修改
const sourceChange = () => {
  let params = {
    type: switch7.value,
  };
  setSourceShow(params).then((res) => {
    if (res.status == 200) {
      if (switch7.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 来源修改
const getSource = () => {
  getSourceShow().then((res) => {
    if (res.status == 200) {
      switch7.value = res.data;
    }
  });
};
// 分用户组设置查看权限开关
const userSelePower = (v) => {
  let params = {
    type: switch2.value,
  };
  setDirectional(params).then((res) => {
    if (res.status == 200) {
      if (switch2.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 仅配置一项分类时不显示首页
const setStatus = () => {
  // 相关推荐接口
  let params = {
    type: switch8.value,
  };
  setHomeSettingStatus(params).then((res) => {
    if (res.status == 200) {
      if (switch8.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 隐藏积分商城入口
const IntegralMallStatus = () => {
  // 相关推荐接口
  let params = {
    type: switch10.value,
  };
  setIntegralMallStatus(params).then((res) => {
    if (res.status == 200) {
      if (switch10.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 相关推荐
const recommend = () => {
  // 相关推荐接口
  let params = {
    type: switch3.value,
  };
  setRecommendStatus(params).then((res) => {
    if (res.status == 200) {
      if (switch3.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 项目积分发放数量限制
// const integralRestrict = (v) => {
//     let params = {
//         "integral":usableIntegral.value,
//         "operationType": switch4.value
//     }
//     setIntegral(params).then((res) => {
//         if (res.status == 200) {
//             if (switch4.value) {
//                 ElMessage({
//                     message: '开关已打开',
//                     type: 'success',
//                 })
//             } else {
//                 ElMessage({
//                     message: '开关已关闭',
//                     type: 'success',
//                 })
//             }
//         }
//     });
// };
// 设置积分数量弹框
const setIntegralDialog = () => {
  if (switch4.value) {
    IntegralDialog.value = true;
  } else {
    let params = {
      operationType: switch4.value,
      integral: usableIntegral.value,
    };
    setIntegral(params).then((res) => {
      if (res.status == 200) {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    });
  }
  //    设置接口
};
// 保存配置
const savePower = () => {
  if (usableIntegral.value < executedIntegral.value) {
    ElMessage({
      message: "设置梅花小于项目已发放梅花总数",
      type: "warning",
    });
    return;
  }
  let params = {
    operationType: switch4.value,
    integral: usableIntegral.value,
  };
  setIntegral(params).then((res) => {
    if (res.status == 200) {
      ElMessage({
        message: "开关已打开",
        type: "success",
      });
    }
  });
  IntegralDialog.value = false;
};
const IntegralSetCancel = () => {
  IntegralDialog.value = false;
  switch4.value = false;
};
const wxCancel = () => {
  wxDialog.value = false;
  switch6.value = false;
};
// 项目积分商城
const storeOpen = (v) => {
  let params = {
    type: switch5.value,
  };
  setIntegralMenuStatus(params).then((res) => {
    if (res.status == 200) {
      if (switch5.value) {
        ElMessage({
          message: "开关已打开",
          type: "success",
        });
      } else {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    }
  });
};
// 绑定公众号
const wxOpen = (v) => {
  if (switch6.value) {
    wxDialog.value = true;
  } else {
    let params = {
      id: wxData.id,
      status: 1,
      // 'projectId':store.state.projectInfo.id,
    };
    wxConfigDelWx(params).then((res) => {
      if (res.status == 200) {
        ElMessage({
          message: "开关已关闭",
          type: "success",
        });
      }
    });
  }
};
// 保存配置
const saveWxAppid = () => {
  if (!wxData.appid || !wxData.secret) {
    ElMessage({
      message: "输入的内容不能为空",
      type: "warning",
    });
    return;
  }
  let params = {
    appid: wxData.appid,
    projectId: store.state.projectInfo.id,
    secret: wxData.secret,
    status: 0,
  };
  if (wxData.id) {
    params.id = wxData.id;
  }
  wxConfigAddWx(params).then((res) => {
    if (res.status == 200) {
      ElMessage({
        message: "开关已打开",
        type: "success",
      });
    }
  });
  wxDialog.value = false;
};
// 设置项目token过期时间配置
const setTokenDate = async () => {
  let data = {
    expireDay: expireDay.value,
    projectId: store.state.projectInfo.id,
  };
  await setTokenExpire(data).then(() => {
    ElMessage.success("保存成功");
  });
  await getTokenDate();
};
onMounted(() => {
  getViewCountShow();
  getDirectional();
  getRecommendStatus();
  getTokenDate();
  getIntegral();
  getStore();
  getWx();
  getSource();
  getStatus();
  toolbarStatus();
  gIntegralMallStatus()
});
// 获取状态
const getStatus = () => {
  getHomeSettingStatus().then((res) => {
    switch8.value = res.data;
  });
};
// 获取状态
const gIntegralMallStatus = () => {
  getIntegralMallStatus().then((res) => {
    switch10.value = res.data;
  });
};
const toolbarStatus = () => {
  getToolBarStatus().then((res) => {
    switch9.value = res.data;
  });
};
// 获取内容列表及详情页浏览量开关
const getViewCountShow = () => {
  ViewCountShow().then((res) => {
    switch1.value = res.data;
  });
};
const getDirectional = () => {
  Directional().then((res) => {
    switch2.value = res.data;
  });
};
const getRecommendStatus = () => {
  RecommendStatus().then((res) => {
    switch3.value = res.data;
  });
};
// 获取项目过期时间配置
const getTokenDate = () => {
  let projectId = store.state.projectInfo.id;
  getTokenExpire(projectId).then((res) => {
    categoryList.value.some((item) => item.value === res.data)
      ? (expireDay.value = res.data)
      : (expireDay.value = 30);
  });
};
// 获取积分发放数量限制开关
const getIntegral = () => {
  IntegralStatus().then((res) => {
    switch4.value = res.data.deleted === 0;
    usableIntegral.value = res.data.integral;
    executedIntegral.value = res.data.integral - res.data.remainingIntegral;
  });
};
// 获取积分商城设置开关
const getStore = () => {
  IntegralMenuStatus().then((res) => {
    switch5.value = res.data;
  });
};
// 获取公众号id
const getWx = () => {
  wxConfigGetWx().then((res) => {
    if (res.data) {
      wxData.appid = res.data.appid;
      wxData.secret = res.data.secret;
      wxData.id = res.data.id;
      switch6.value = res.data.status === 0;
    }
  });
};
</script>
<script>
import initModule from "@/mixins/initModule.js";
export default {
  name: "project_settings",
  mixins: [initModule],
};
</script>
<style scoped lang="scss">
.yxd_itemset {
  .container {
    .word {
      opacity: 1;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #7f7f7f;
      // line-height: 16px;
    }
  }
  table {
    width: 100%;
    tr {
      line-height: 45px;
      width: auto;
      display: flex;
      td {
        width: auto;
        font-size: 14px;
        opacity: 0.8;
        .select {
          // width: 80px;
          ::v-deep {
            div.el-input.el-input--suffix {
              width: 80px !important;
            }
          }
        }
      }
      .td1 {
        width: 190px;
      }
      .td2 {
        width: 140px;
        text-align: center;
      }
    }
  }
  //    .td2 .select .el-input {
  //         width: 1000px !important;
  //         background-color: #000000 !important;
  //     }
}
.dialog-content {
  display: flex;
  span {
    width: 30%;
    line-height: 40px;
  }
  .el-input {
    width: 70%;
  }
}
.title {
  line-height: 40px;
  box-sizing: border-box;
  font-size: 14px;
  width: 100%;
  margin-top: 20px;
  display: flex;
  height: 40px;
  .d_block1 {
    width: 190px;
    background: #f2f2f2;
    padding-left: 20px;
  }
  .d_block2 {
    width: 140px;
    text-align: center;
    background: #f2f2f2;
  }
  .d_block3 {
    flex: 1;
    text-align: center;
    background: #f2f2f2;
  }
}
</style>