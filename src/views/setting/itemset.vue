<template>
  <div class="container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="常用设置" name="first">
        <sets></sets>
      </el-tab-pane>
      <el-tab-pane label="个人中心" name="second"> 
        <personal-center></personal-center>
      </el-tab-pane>
      <el-tab-pane label="搜索" name="third">
          <search></search>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from "vue";
import sets from "./components/set.vue";
import personalCenter from "./components/personal_center.vue";
import search from "./components/search.vue";
let activeName = ref("first");
const handleClick = (val, e) => {
  // activeName.value = val
};
</script>

<style>
</style>
