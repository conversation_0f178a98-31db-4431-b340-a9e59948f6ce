<template>
    <div class="container">
       <!-- 删除单条 -->
      <el-dialog  title="删除提示" v-model="deleteCourse" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.title }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteCourse = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog :title="lineText" v-model="dialogVisible" width="70%">
      <span
        >是否{{ lineText == "审核提示" ? "启用" : "禁用" }}
        <el-tag>{{ currentChose.title }}</el-tag>
      </span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="lineSave" size="mini"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
       <el-dialog title="批量删除" v-model="settingVisible" width="70%">
        <h3>确定删除这些内容吗？</h3>
        <el-tag class="delete-box" v-for="(item, index) in offLineData" :key="index">
          {{ item.title }}
        </el-tag>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="settingVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="saveDeleteEdit" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
       <!-- 删除单条 -->
      <el-dialog  title="删除提示" v-model="deleteCourse" width="70%">
        <span>是否删除 <el-tag>{{ currentChose.title }}</el-tag></span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteCourse = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="deleteSave" size="mini">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入专题名称"></el-input>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="apiInit">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <el-button type="primary" @click="add" v-if="btnList.special_topic_add">创建</el-button>
        <el-button  @click="deleteClass" icon="el-icon-close" v-if="btnList.special_topic_batch_delete">批量删除</el-button>
      </div>
       <el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column fixed prop="id" label="Id" width="150" align="center"/>
        <el-table-column prop="title" label="专题标题" align="center" >
           <template #default="scope">
             <a  target="_blank" :href="toUrl+scope.row.id+'-1'">{{scope.row.title}}</a>
           </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="220" align="center"/>
        <el-table-column prop="createdName" label="创建人" align="center"/>
        <el-table-column prop="status" label="状态" align="center" >
           <template #default="scope">
            {{scope.row.status==0?'待发布':'已发布'}}
          </template>
        </el-table-column>
        <el-table-column prop="publishedTime" label="发布时间"  width="220"  align="center"/>
        <el-table-column prop="zip" label="PV/UV"  align="center">
          <template #default="scope">
            {{scope.row.pv}}/{{scope.row.uv}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="300">
          <template #default="scope">
              <el-button v-if="btnList.special_topic_edit" size="mini" @click="goTab(scope.$index, scope.row)">
                编辑
              </el-button>
              <el-button v-if="btnList.special_topic_audit" size="mini" @click="handleDown(scope.$index, scope.row)">{{
                  scope.row.status == 1 ? "去审" : "审核"
              }}</el-button>
              <el-button v-if="btnList.special_topic_copy_url" id="copy" size="mini" @click="copy(scope.row)">复制</el-button>
              <el-button v-if="btnList.special_topic_delete" size="mini" type="danger" plain
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
        </el-table-column>
      </el-table>
       <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
</template>

<script>
import {medsciSpecialTopic,batchDeal,medsciSpecialTopicAudit,medsciSpecialTopicDelete,medsciSpecialTopicRemoveAudit} from "@/request/service.js"
import initModule from "@/mixins/initModule.js";
import { ElMessage } from "element-plus";
import clipboard from 'clipboard'

export default {
  name: "ms-special-manage",
  data () {
    return {
      title:"",
      tableData:[],
      ids:[],
      settingVisible:false,
      offLineData:[],
      lineText:"",
      dialogVisible:false,
      deleteCourse:false,
      currentPage:1,
      pageSize: 20,
      toUrl:""
    }
  },
  mixins:[initModule],
  created() {

    this.apiInit()
  },
  mounted(){
    this.toUrl=location.origin+"/special?id="
    console.log(location)
  },
  methods: {
    // 复制
    copy(row){
      let _this = this
      let clipborad = new clipboard('#copy',{
         text: function(trigger) {
			        return location.origin+"/special?id="+row.id+'-1';
			    }
      })
      //复制成功
      clipborad.on('success', function(e) {
        ElMessage.success('复制成功！')
      clipborad.destroy()
      })
      //复制失败
      clipborad.on('error', function(e) {
        ElMessage.error('复制失败！')
        clipborad.destroy()
      })
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage=1
      this.apiInit();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.apiInit();
    },
    // 重置
    reset(){
      this.title=""
    },
      // 单条删除
    deleteSave() {
      medsciSpecialTopicDelete({id:this.currentChose.id}).then((res) => {
        if (res.status == 200) {
          this.deleteCourse = false;  
          this.apiInit();
        }
      });
    },
    // 审核
    lineSave() {
      // 上线操作
      let params = {
        id: this.currentChose.id,
      };
      if(this.lineText=='审核提示'){
        medsciSpecialTopicAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("成功");
          this.apiInit();
          this.dialogVisible = false;
        }
      });
      }else{
        medsciSpecialTopicRemoveAudit(params).then((res) => {
        if (res.status == 200) {
          ElMessage.success("成功");
          this.apiInit();
          this.dialogVisible = false;
        }
      });
      }
      
   
      
    },
    handleSelectionChange(val){
      this.offLineData=val
      this.ids= val.map((item)=>{
        return item.id
      })
    },
       // 批量删除
    saveDeleteEdit() {
      let params = {
        ids:this.ids
      }
      batchDeal(params).then((res) => {
        if (res.status == 200) {
          this.apiInit()
          this.settingVisible = false
        }
      })
    },
    // 批量删除
     deleteClass() {
      if (this.ids.length == 0) {
        ElMessage.warning("请至少选择一条数据");
        return;
      }
      this.settingVisible = true;
    },
    // 审核
     handleDown(index, row) {
      if (row.status == "0") {
        this.lineText = "审核提示";
      } else {
        this.lineText = "去审提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    // 删除单条权限
    handleDelete(index,row){ 
        this.deleteCourse = true;
      this.currentChose = row;
    },
    goTab(index,row){
        this.$router.push("/special_topic/special-operation?id="+row.id)
    },
    add(){
      this.$router.push("/special_topic/special-operation")
    },
    apiInit () {
      let searchParams = {
        pageIndex:this.currentPage,
        pageSize:this.pageSize,
        title:this.title
      }
      medsciSpecialTopic(searchParams).then(res => {
        console.log(res)
        this.loading = false
        this.total = res.totalSize || 0;
        this.tableData = res.data || []
      }).catch(() => this.loading = false)
    }
  }
};
</script>
<style lang="scss" scoped>
    .operate{
      margin-bottom: 10px;
    }
    .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
    .header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
    .right {
    }
  }
</style>
