<template>
  <div class="class_ify">
    <classify-create
      :type="type"
      @back="isIndex = true"
    ></classify-create>
  </div>
</template>
<script>
import ClassifyCreate from "@/components/store/Classify";
export default {
  name: "store",
  components: {
    ClassifyCreate,
  },
  data() {
    return {
      isIndex: true,
      currentData: {},
      custom:{},
      type: "",
    };
  },
  mounted() {},
  methods: {
    sortClass(category) {
      this.type = category;
      this.isIndex = false;
    },
    // editUser(item) {
    //   this.type = "edit";
    //   this.currentData = item;
    //   this.isIndex = false;
    // },
  },
};
</script>
<style scoped lang="scss">
.class_ify {
}
</style>