<template>
  <div class="yxd_task">
    <task-index @go="goEdit" v-if="isIndex"></task-index>
    <task-dialog
      :edit="currentId"
      @back="isIndex = true"
      v-else
    ></task-dialog>
  </div>
</template>

<script>
import taskIndex from "@/components/task/index";
import taskDialog from "@/components/task/taskDialog";
export default {
  name: "tasks",
  components: {
    taskIndex,
    taskDialog,
  },
  data() {
    return {
      isIndex: true,
      currentId: "",
    };
  },
  mounted() {},
  methods: {
    goEdit(obj) {
      this.isIndex = false;
      this.currentId = obj;
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_information {
}
</style>
