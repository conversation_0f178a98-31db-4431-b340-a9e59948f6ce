<template>
  <div class="p_box" v-if="$route.query.tem != 'exam_paper'">
    <div
      class="p_center"
      v-if="
        $route.query.tem == 'article' ||
        $route.query.tem == 'eda' ||
        $route.query.tem == 'case'
      "
    >
      <div class="nav_minabao">
        <span
          class="cate"
          @click="
            () => {
              $router.go(-1);
            }
          "
          >分类</span
        >/{{
          $route.query.tem == "article"
            ? "资讯"
            : $route.query.tem == "eda"
            ? "医讯达"
            : $route.query.tem == "case"
            ? "病例"
            : ""
        }}
      </div>
      <el-radio-group v-model="radio">
        <div
          @click="radio = 1"
          :class="[
            radio == 1 &&
            ($route.query.tem == 'article' ||
              $route.query.tem == 'eda' ||
              $route.query.tem == 'case')
              ? 'active'
              : '',
          ]"
        >
          <img src="@/assets/img/list1.png" alt="" />
          <el-radio :label="1">默认列表样式</el-radio>
          <div>图文/视频组成的列表页面</div>
        </div>
        <div
          @click="radio = 2"
          :class="[
            radio == 2 &&
            ($route.query.tem == 'article' ||
              $route.query.tem == 'eda' ||
              $route.query.tem == 'case')
              ? 'active'
              : '',
          ]"
        >
          <img src="@/assets/img/list2.png" alt="" />
          <el-radio :label="2">大图模式</el-radio>
          <div>封面与多内容组成的页面</div>
        </div>
      </el-radio-group>
    </div>
    <div class="p_center" v-if="$route.query.tem == 'live_info'">
      <div class="nav_minabao">
        <span
          class="cate"
          @click="
            () => {
              $router.go(-1);
            }
          "
          >分类</span
        >/直播
      </div>
      <el-radio-group v-model="radio">
        <div
          @click="radio = 1"
          :class="[
            radio == 1 && $route.query.tem == 'live_info' ? 'active' : '',
          ]"
        >
          <img src="@/assets/img/list2/list1.png" alt="" />
          <el-radio :label="1">默认列表样式</el-radio>
          <div>显示直播状态、观看引导按钮</div>
        </div>
        <div
          @click="radio = 2"
          :class="[
            radio == 2 && $route.query.tem == 'live_info' ? 'active' : '',
          ]"
        >
          <img src="@/assets/img/list2/list2.png" alt="" />
          <el-radio :label="2">大封面模式</el-radio>
          <div>显示直播状态、观看引导按钮</div>
        </div>
      </el-radio-group>
    </div>
    <div class="p_center" v-if="$route.query.tem == 'course'">
      <div class="nav_minabao">
        <span
          class="cate"
          @click="
            () => {
              $router.go(-1);
            }
          "
          >分类</span
        >/课程
      </div>
      <el-radio-group v-model="radio">
        <div
          @click="radio = 1"
          :class="[radio == 1 && $route.query.tem == 'course' ? 'active' : '']"
        >
          <img src="@/assets/img/list3/list1.png" alt="" />
          <el-radio :label="1">默认列表样式</el-radio>
          <div></div>
        </div>
        <div
          @click="radio = 2"
          :class="[radio == 2 && $route.query.tem == 'course' ? 'active' : '']"
        >
          <img src="@/assets/img/list3/list2.png" alt="" />
          <el-radio :label="2">大封面模式</el-radio>
          <div>进入详情页播放</div>
        </div>
        <div
          @click="radio = 3"
          :class="[radio == 3 && $route.query.tem == 'course' ? 'active' : '']"
        >
          <img src="@/assets/img/list3/list3.png" alt="" />
          <el-radio :label="3">多列模式</el-radio>
          <div>进入详情页播放</div>
        </div>
      </el-radio-group>
    </div>
    <div class="btn">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </div>
  <div class="p_box" v-else>
    <el-tabs type="border-card">
      <el-tab-pane label="首页样式">
        <div class="p_center">
          <div class="nav_minabao">
            <span
              class="cate"
              @click="
                () => {
                  $router.go(-1);
                }
              "
              >分类</span
            >/考试
          </div>
          <div class="exam_flex">
            <el-radio-group v-model="radio">
            <div @click="radio = 1" class="">
              <img src="@/assets/img/list4/list1.png" alt="" />
              <el-radio :label="1">默认列表样式</el-radio>
              <div>文本组成的列表页面</div>
            </div>
          </el-radio-group>
          </div>
        </div>
        <div class="btn">
          <el-button type="primary" @click="submit(1)">提交</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="二级页面样式">
        <div class="p_center">
          <div class="nav_minabao">
            <span
              class="cate"
              @click="
                () => {
                  $router.go(-1);
                }
              "
              >分类</span
            >/考试
          </div>
          <div class="exam_flex">
            <el-radio-group v-model="radioTwo">
              <div @click="radioTwo = 1" class="">
                <img src="@/assets/img/list4/list2.png" alt="" />
                <el-radio :label="1">默认列表</el-radio>
                <div>文本组成的列表页面</div>
              </div>
              <div @click="radioTwo = 2" class="">
                <img src="@/assets/img/list4/list3.png" alt="" />
                <el-radio :label="2">分类说明列表</el-radio>
                <div>适用于分类有解释说明的场景</div>
              </div>
               <div @click="radioTwo = 3" class="">
                <img src="@/assets/img/list4/list4.png" alt="" />
                <el-radio :label="3">内容平铺列表</el-radio>
                <div>适用于内容不多的场景</div>
              </div>
              <div @click="radioTwo = 4" class="">
                <img src="@/assets/img/list4/list5.png" alt="" />
                <el-radio :label="4">分类平铺列表</el-radio>
                <div>适用于内容不多的场景</div>
              </div>
            </el-radio-group>
          </div>
        </div>
        <div class="btn">
          <el-button type="primary" @click="submit(2)">提交</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { setTagUi } from "@/request/service.js";
export default {
  data() {
    return {
      radio: false,
      row: {},
      radioTwo:false,
    };
  },
  mounted() {
    this.row = JSON.parse(this.$route.query.row);
    if (this.row.h5TagUi) {
      this.radio = this.row.h5TagUi.uiId;
    }else if(this.row.childTagUi){
       this.radioTwo = this.row.childTagUi.uiId;
    } else {
      this.radio = 1;
      this.radioTwo = 1;
    }
  },
  methods: {
    submit(type) {
      if(type==1){

      }else if(type==2){
      let params = {
        source: 0,
        tagId: this.row.id,
        tagType: this.row.tagType,
        uiId: this.radioTwo,
        scope:1
      };
      setTagUi(params).then((res) => {
        this.$emit("close");
      });
      }else{
        let params = {
        source: 0,
        tagId: this.row.id,
        tagType: this.row.tagType,
        uiId: this.radio,
      };
      setTagUi(params).then((res) => {
        this.$emit("close");
      });
      }
      
    },
  },
};
</script>

<style lang="scss" scoped>
.p_box {
  .exam_flex {
    display: flex;
    flex-wrap: wrap;
    .el-radio-group {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      flex-wrap: wrap;
      > div {
        background: #fff;
        width: 48%;
        margin: 0 10px 0 0;
        cursor: pointer;

        display: flex;
        flex-direction: column;
        align-items: center;
        div {
          color: #999999;
        }
        img {
          width: 300px;
          margin: 30px auto;
        }
        div {
          height: 30px;
          margin: 10px auto 30px;
        }
      }
      > div:last-child {
        margin-right: 0;
      }
    }
  }
  .p_center {
    width: 100%;
    .cate {
      cursor: pointer;
    }
    .nav_minabao {
      margin-bottom: 10px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
  }
  .active {
    border: solid 1px #1890ff;
    box-sizing: border-box;
  }
  ::v-deep {
    .btn {
      display: flex;
    }
    .el-button {
      margin: 30px auto;
      font-size: 16px;
    }
    .el-radio__label {
      font-weight: 600;
    }
    .el-radio-group {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      justify-content: center;
      margin-bottom: 20px;
      > div {
        background: #fff;
        width: 50%;
        margin: 0 10px 0 0;
        cursor: pointer;

        display: flex;
        flex-direction: column;
        align-items: center;
        div {
          color: #999999;
        }
        img {
          width: 500px;
          margin: 30px auto;
        }
        div {
          height: 30px;
          margin: 10px auto 30px;
        }
      }
      > div:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>