<template>
  <div class="yxd_live">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <el-input v-model="title" placeholder="请输入标题"></el-input>
          </div>
          <div class="col">
            <el-input v-model="createdName" placeholder="请输入创建人"></el-input>
          </div>
          <!-- 筛选分类 -->
          <div class="col" v-if="true">
            <el-cascader
              placeholder="请选择分类"
              :options="ClassifyData"
              ref="Classify"
              collapse-tags
              @change="ClassifyChange"
              :props="{
                checkStrictly: true,
                value: 'id',
                label: 'tagName',
                children: 'childTags',
                multiple: true,
              }"
              clearable
            >
            </el-cascader>
          </div>
          <div class="col">
            <el-select v-model="state" placeholder="请选择审核状态" clearable>
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="审核通过" :value="1"></el-option>
              <el-option label="审核未通过" :value="2"></el-option>
            </el-select>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <span class="button-group">
          <el-button v-if="btnList.batch_ugc_audit" @click="batch_audit">批量审核</el-button>
          <el-button v-if="btnList.batch_ugc_audit" @click="setClass">批量设置分类</el-button>
          <el-button @click="filterList()" :type="btnStatus ? 'primary' : ''">未设置分类</el-button>
          <el-button @click="exportUgc" :loading="exportLoading">批量导出</el-button>
          <el-button @click="openRandomSettingDialog">设置阅读量</el-button>
        </span>
        <div class="operate_title">
        <div>说明：</div>
审核通过后，点"上线"按钮，即可在专区上线；<br/>
去审后，需点"下线"按钮，方可从专区下线。
        </div>
      </div>
      <div class="table">
        <el-table
          ref="multipleTable"
          border
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column
            sortable
            prop="id"
            label="ID"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="title"
            label="标题"
            align="center"
            min-width="120"
          >
            <template #default="scope">
              <span
                v-if="scope.row.url && scope.row.auditState === 1 && scope.row.onlineState === 1"
                @click="openUrl(scope.row.url)"
                class="title-link"
                :title="scope.row.title"
              >
                {{ scope.row.title }}
              </span>
              <span v-else :title="scope.row.title">
                {{ scope.row.title }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="playUrl"
            label="PV/UV"
            min-width="70"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.hits }}/{{ scope.row.userHits }}
            </template>
          </el-table-column>
          <el-table-column
            prop="mobile"
            label="手机号"
            align="center"
            width="120"
          >
          </el-table-column>
          <el-table-column
            prop="accountType"
            label="身份类型"
            align="center"
            width="100"
          >
            <template #default="scope">
              {{ scope.row.accountType === 0 ? '医生' : scope.row.accountType === 1 ? '游客' : '未知' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="tags"
            label="分类"
            align="center"
            min-width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.tags || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="sensitiveWords"
            label="内含敏感词"
            width="120"
            align="center"
          >
            <template #default="scope">
              <span style="color: red;">{{ scope.row.sensitiveWords || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            sortable
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            sortable
            min-width="150"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="onlineState"
            label="状态"
            width="120"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.auditState==0?'待审核':scope.row.auditState==1?'审核通过':'审核不通过' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="randomVal"
            label="阅读量基础值"
            width="100"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="280"
            fixed="right"
          >
            <template #default="scope">
              <el-button v-if="btnList.ugc_detail" @click="searchDetail(scope.row)"
                >查看</el-button
              >
              <el-button
                v-if="btnList.ugc_audit"
                @click="goToEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="btnList.ugc_audit"
                @click="audit(scope.row)"
                >{{scope.row.auditState==1?'去审':'审核'}}</el-button
              >
              <el-button
                v-if="btnList.end_btn"
                size="mini"
                @click="handleUp(scope.$index, scope.row)"
                >{{ scope.row.onlineState==1?"下线":"上线" }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <!-- 上/下线 -->
      <el-dialog :title="lineText" v-if="dialogVisible" v-model="dialogVisible" width="70%">
        <span
          >是否{{ lineText == "上线提示" ? "上线" : "下线" }}
          <el-tag>{{ currentChose.title }}</el-tag>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="lineSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 查看详情/单个审核 -->
      <el-dialog title="查看详情" v-model="dialogDetail" width="80%">
        <div class="detail_content">
          <!-- 添加敏感词字段 - 移动到最顶部 -->
          <div class="detail_sensitive" v-if="detailData.sensitiveWords">
            <span class="detail_sp">内含敏感词:</span>
            <span style="color: red;">{{ detailData.sensitiveWords }}</span>
          </div>
          <div class="detail_create">
            <div><span class="detail_sp">创建人:</span>  {{detailData.createdName}}</div>
            <div><span class="detail_sp">创建时间:</span>  {{detailData.createdTime}}</div>
          </div>
          <div class="detail_title"><span class="detail_sp">标题:</span>  {{detailData.title}}</div>
          <div class="detail_text"><span class="detail_sp">正文: </span>  <div v-html="processedContent" class="content-wrapper"></div></div>
          <div class="detail_imags">
              <el-image
              :src="item"
              v-for="(item,index) in detailData.imageUrls"
              :key="index"
            ></el-image>
            <video v-if="detailData.videoUrl" ref="videoPlayer" controls>
          </video>
            <div v-if="20%3==2"></div>
          </div>
          <!-- 添加封面字段 -->
          <div class="detail_cover" v-if="detailData.ugcCover">
            <span class="detail_sp">封面:</span>
            <div class="cover-container">
              <el-image :src="detailData.ugcCover" class="cover-image"></el-image>
            </div>
          </div>
          <!-- 添加附件字段 -->
          <div class="detail_attachment" v-if="detailData.attachment">
            <span class="detail_sp">附件:</span>
            <div class="attachment-container">
              <a :href="detailData.attachment" target="_blank" class="attachment-link">{{detailData.attachmentName || '点击下载附件'}}</a>
            </div>
          </div>

          <div class="detail_radio" v-if="shenhe">
              <span class="detail_sp">审核操作： </span>
              <el-radio-group v-model="radio">
                <el-radio :label="1">审核通过</el-radio>
                <el-radio :label="2">审核不通过</el-radio>
            </el-radio-group>
          </div>
          <div class="detail_textarea" v-if="radio==2">
            <span class="detail_sh">说明： </span>
            <el-input
                type="textarea"
                :rows="3"
                placeholder="请填写不通过原因"
                v-model="text">
                </el-input>
        </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogDetail = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="examine" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
       <!-- 批量审核 -->
      <el-dialog title="批量审核" v-model="dialogAuditAll" width="800">
            <el-table
        :data="choseData"
        @row-click="singleElection"
        class="tables_deep"
        highlight-current-row
      >
        <el-table-column align="center" prop="title" label="标题">
        </el-table-column>
        <el-table-column align="center" prop="createdName" label="创建人">
        </el-table-column>
        <el-table-column align="center" prop="createdTime" label="创建时间">
        </el-table-column>
      </el-table>
       <span class="detail_sh">审核操作： </span>
              <el-radio-group v-model="radio1">
                <el-radio :label="1">审核通过</el-radio>
                <el-radio :label="2">审核不通过</el-radio>
        </el-radio-group>
        <div class="detail_textarea" v-if="radio1==2">
            <span class="detail_sh">说明： </span>
            <el-input
                type="textarea"
                :rows="3"
                placeholder="请填写不通过原因"
                v-model="textAll">
                </el-input>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogAuditAll = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="examineAll" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>

      <!-- 设置阅读量随机值 -->
      <el-dialog title="设置阅读量随机值" v-model="randomSettingDialog" width="500">
        <div class="random-setting-form">
          <div class="form-item">
            <span class="label">最小随机数：</span>
            <el-input v-model.number="minRandomValue" type="number" placeholder="请输入最小随机数"></el-input>
          </div>
          <div class="form-item">
            <span class="label">最大随机数：</span>
            <el-input v-model.number="maxRandomValue" type="number" placeholder="请输入最大随机数"></el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="randomSettingDialog = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="submitRandomSetting" size="mini"
              >提 交</el-button
            >
          </span>
        </template>
      </el-dialog>

      <!-- 批量设置分类弹框 -->
      <classify-dialog ref="classifyRef" :choseData="choseData" @init="set"></classify-dialog>
    </div>
  </div>
</template>

<script>
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import filterList from "@/mixins/filterList.js";
import {
  query,
  listTag,
  batchLineModule,
  ugc,
  ugcDetail,
  setRandomRange,
  ugcBatchAudit,
  ugcExport
} from "@/request/service.js";
import { ElMessage } from "element-plus";
import Hls from 'hls.js';
import htmlMethods from "@/utils/htmlFunction.js";
import classifyDialog from "../components/classifyDialog.vue";
export default {
  name: "ugc",
  mixins: [initModule, initScroll, filterList],
  components: {
    classifyDialog,
  },
  data() {
    return {
      radio:1,
      dialogVisible: false,
      dialogDetail: false,
      detailData:{},
      dialogAuditAll:false,
      title: "",
      createdName: "", // 创建人搜索条件
      state: "", // 审核状态搜索条件
      time: "",
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      // powerVisible: false,
      selectPower: "",
      lineText: "",
      checkList: [],
      selectvalue: [],
      RoleList: [],
      choseData: [],
      ClassifyData: [],
      ClassifyValue: [],
      offLineData: "",
      shenhe:false,
      text:"",
      textAll:"",
      radio1:1,
      randomSettingDialog: false, // 设置阅读量随机值弹窗
      minRandomValue: 0, // 最小随机数
      maxRandomValue: 1000, // 最大随机数
      btnStatus: false, // 未设置分类按钮状态
      filterData: [], // 过滤前的原始数据
      exportLoading: false, // 导出加载状态
    };
  },
  mounted() {
    this.init();
    this.getPowerData();
    this.getClassifyData();
  },
  computed: {
    processedContent() {
      return this.detailData && this.detailData.content ? htmlMethods.unexcapeHtml(this.detailData.content) : '';
    }
  },
  watch:{
      radio:{
          handler(val){
              if(val==1){
                  this.text=""
              }
          }
      }
  },
  methods: {
    // 打开URL链接
    openUrl(url) {
      if (url) {
        window.open(url, '_blank');
      }
    },
    // 打开设置阅读量随机值弹窗
    openRandomSettingDialog() {
      this.randomSettingDialog = true;
      this.minRandomValue = 0;
      this.maxRandomValue = 1000;
    },
    
    // 提交设置阅读量随机值
    submitRandomSetting() {
      if (this.minRandomValue > this.maxRandomValue) {
        this.$message.warning('最小随机数不能大于最大随机数');
        return;
      }
      
      setRandomRange(this.minRandomValue, this.maxRandomValue).then(res => {
        if (res.status == 200) {
          this.$message.success('设置成功');
          this.randomSettingDialog = false;
          this.init(); // 刷新列表
        } else {
          this.$message.error(res.message || '设置失败');
        }
      }).catch(err => {
        this.$message.error('设置失败：' + err.message);
      });
    },
    //查看
    searchDetail(row){
        this.dialogDetail=true
        this.text=""
        this.shenhe=false
        ugcDetail(row.id).then((res)=>{
          this.detailData=null
          this.detailData = res.data
          setTimeout(() => {
            const video = this.$refs.videoPlayer;
          if (Hls.isSupported()) {
            const hls = new Hls();
            hls.loadSource(this.detailData.videoUrl);
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED, function () {
              video.play();
            });
          } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = this.detailData.videoUrl;
            video.addEventListener('loadedmetadata', function () {
              video.play();
            });
          }
          }, 1000);
        })
    },
    // 去审
    audit(row){
        this.radio=1
        this.text=""
        this.dialogDetail=true
        this.shenhe=true
         ugcDetail(row.id).then((res)=>{
          this.detailData=null
          this.detailData = res.data
          setTimeout(() => {
            const video = this.$refs.videoPlayer;
          if (Hls.isSupported()) {
            const hls = new Hls();
            hls.loadSource(this.detailData.videoUrl);
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED, function () {
              video.play();
            });
          } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = this.detailData.videoUrl;
            video.addEventListener('loadedmetadata', function () {
              video.play();
            });
          }
          }, 1000);
        })
    },
    batch_audit(){
       if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.textAll=""
      this.radio1=1
        this.dialogAuditAll=true
    },
    // 批量设置分类
    setClass() {
      if (this.choseData.length == 0) {
        ElMessage.warning("请至少选择一条数据！");
        return;
      }
      this.$refs.classifyRef.openDialog("ugc");
      this.$refs.classifyRef.gitClassList();
    },
    // 分类设置回调
    set() {
      this.init();
    },
    // 批量导出
    exportUgc() {
      this.exportLoading = true;
      // 构建导出参数，与搜索接口一样但不需要分页参数
      let params = {
        classifyIds: this.ClassifyValue,
        title: this.title,
        createdName: this.createdName,
        state: this.state,
        ugcIds: []
      };

      ugcExport(params).then((res) => {
        this.exportLoading = false;
        if (res.size > 57) {
          // 创建下载链接
          let name = `UGC数据导出_${new Date().toLocaleDateString()}.xlsx`;
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = name;
          link.click();
          ElMessage.success("导出成功");
        } else {
          ElMessage.warning("暂无数据导出");
        }
      }).catch((error) => {
        this.exportLoading = false;
        ElMessage.error("导出失败：" + (error.message || "未知错误"));
      });
    },
    init() {
      this.tableData = [];
      let params = {
        classifyIds: this.ClassifyValue,
        pageIndex: this.currentPage,
        pageSize:this.pageSize,
        title: this.title,
        createdName: this.createdName, // 创建人搜索条件
        state: this.state, // 审核状态搜索条件
        ugcIds: []
      };
      ugc(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.filterData = this.tableData;
          this.btnStatus = false;
          this.initScroll();
        }
      });
    },
    // 权限下拉列表
    getPowerData() {
      let params = {
        title: "",
      };
      query(params).then((res) => {
        if (res.status == 200) {
          this.RoleList = this.getList(res.data);
        }
      });
    },
    // 分类下拉列表
    getClassifyData() {
      let params = {
        // tagName: this.title,
        tagIds: [],
        tagType: "ugc",
      };
      listTag(params).then((res) => {
        this.ClassifyData = this.removeClassifyChild(res.data);
      });
    },
    getList(data) {
      return data.filter((item) => {
        if (item.childRoles.length === 0) {
          delete item.childRoles;
          return item;
        } else {
          return this.getList(item.childRoles);
        }
      });
    },
    // 清除空的子类
    removeClassifyChild(data) {
      return data.filter((item) => {
        if (item.childTags.length === 0) {
          delete item.childTags;
          return item;
        } else {
          return this.removeClassifyChild(item.childTags);
        }
      });
    },

    // 上/下线
    handleUp(_, row) {
      if (row.onlineState) {
        this.lineText = "下线提示";
      } else {
        this.lineText = "上线提示";
      }
      this.dialogVisible = true;
      this.currentChose = row;
    },
    // 上/下线按钮
    lineSave() {
      let ids = [];
      ids.push(this.currentChose.id);
      let params = {
        operationType: this.lineText == "上线提示" ? 1 : 0,
        moduleType: "ugc",

        moduleIds: ids,
      };
      batchLineModule(params).then((res) => {
        if (res.status == 200) {
          this.lineText === "上线提示"
            ? ElMessage.success("上线成功")
            : ElMessage.success("下线成功");
          this.init();
          this.dialogVisible = false;
        }
      });
    },
    // 审核
    examine(){
      let params={
        "operationType": this.radio,
        "reason": this.text,
        "ugcIds": [this.detailData.id]
      }
      ugcBatchAudit(params).then(() => {
        this.dialogDetail=false
        this.init()
      })
    },
    // 跳转到编辑页面
    goToEdit(row){
      this.$router.push({
        path: '/ugc_edit',
        query: {
          id: row.id
        }
      })
    },
    // 批量审核
    examineAll(){
      let arr =  this.choseData.map((res)=>{
       return   res.id
      })
      let params={
        "operationType": this.radio1,
        "reason": this.textAll,
        "ugcIds": arr
      }
      ugcBatchAudit(params).then(() => {
        this.dialogAuditAll=false
        this.init()
      })
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.time = "";
      this.title = "";
      this.createdName = ""; // 重置创建人搜索条件
      this.state = ""; // 重置审核状态搜索条件
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {};
      obj.stopPropagation = () => {};
      try {
        // this.$refs.cascader.clearValue(obj);
        this.$refs.Classify.clearValue(obj);
      } catch (err) {
        // this.$refs.cascader.handleClear(obj);
        this.$refs.Classify.handleClear(obj);
      }
    },

    // 勾选框
    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    //  点击权限多选框选项
    change() {
      let nodesObj = this.$refs["cascader"].getCheckedNodes();
      this.selectvalue = [];
      nodesObj.forEach((item) => this.selectvalue.push(item.data.id));
    },
    //  点击分类多选框选项
    ClassifyChange() {
      let nodesObj = this.$refs["Classify"].getCheckedNodes();
      this.ClassifyValue = [];
      nodesObj.forEach((item) => this.ClassifyValue.push(item.data.id));
    },
  },
};
</script>

<style scoped lang="scss">
.operate{
  display: flex;
  justify-content: space-between;
  button{
    height: 29px;
  }
  .button-group {
    display: flex;
    button {
      margin-right: 0;
    }
  }
  .operate_title{
    width: 320px;
    font-size: 14px;
    color: #999999;
  }
}

.random-setting-form {
  padding: 0 20px;
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .label {
      width: 100px;
      text-align: right;
      margin-right: 10px;
    }
  }
}
.detail_text{
 display: flex;
 div:last-child{
   flex:1
 }
}

.content-wrapper {
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: hidden;
  white-space: normal;
  text-align: justify;
  line-height: 1.6;
}
.detail_textarea{
    display: flex;
    margin-top: 20px;
    margin-left: 10px;
    .detail_sh{
        margin-top: 0;
    }
}
.detail_sh{
    width: 80px;
    text-align: right;
    margin-top: 20px;
    display: inline-block;
}
.detail_content {
  padding-right: 10%;
  .detail_create {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
  }
  .detail_title {
    margin-bottom: 30px;
  }
  .detail_sp{
      width: 80px;
      text-align: right;
      display: inline-block;
  }
  .detail_imags {
    width: 330px;
    padding-left: 85px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 10px;
    margin-bottom: 20px;
    video{
      width: 500px;
      height: 200px;
    }
    ::v-deep{
        .el-image{
                margin-bottom: 20px;
            img {
                width: 100px !important;
                height: 100px !important;
                }
        }

    }
    div{
         width: 100px !important;
         height: 100px !important;
         margin-bottom: 20px;

    }
    
  }
  
  // 封面样式
  .detail_cover {
    margin-bottom: 20px;
    .cover-container {
      padding-left: 85px;
      margin-top: 10px;
      .cover-image {
        max-width: 300px;
        max-height: 200px;
      }
    }
  }
  
  // 附件样式
  .detail_attachment {
    margin-bottom: 20px;
    .attachment-container {
      padding-left: 85px;
      margin-top: 10px;
      .attachment-link {
        color: #409eff;
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }

  // 敏感词样式
  .detail_sensitive {
    margin-bottom: 20px;
  }
}
.yxd_live {
  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .table {
    cursor: pointer;

    .title-link {
      color: #409eff;
      cursor: pointer;
      text-decoration: underline;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      width: 58.333%;
      display: flex;
      align-items: flex-start;

      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }

  .edit-main {
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        width: 70px;
        font-size: 12px;
        color: #409eff;
        text-align: right;
        margin-right: 20px;
        font-weight: 600;
        margin-top: 3px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}

.power-head {
  table {
    .line {
      height: 45px;
    }

    tr {
      padding: 30px 0;

      .title {
        width: 70px;
        // font-size: 20px;
      }
    }
  }
}
</style>
