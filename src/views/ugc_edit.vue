<template>
  <div class="ugc-edit">
    <div class="edit-container">
      <div class="header">
        <h2>内容编辑</h2>
        <div class="actions">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="submitContent">保存</el-button>
        </div>
      </div>

      <el-form :model="formData" label-width="80px" class="edit-form">
        <!-- 添加敏感词字段 - 移动到最顶部 -->
        <el-form-item label="" v-if="formData.sensitiveWords">
          <div class="sensitive-info">
            <div class="info-item">
              <span class="info-label">内含敏感词：</span>
              <span class="info-value" style="color: red;">{{ formData.sensitiveWords }}</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="标题">
          <el-input v-model="formData.title" placeholder="请输入标题"></el-input>
        </el-form-item>

        <el-form-item label="正文">
          <div class="editor-container">
            <ms-editor v-model="formData.content" :min-height="400" />
          </div>
        </el-form-item>

        <el-form-item label="">
          <div class="resources-row">
            <div class="resource-item">
              <div class="resource-label">封面：</div>
              <div class="upload-container">
                <el-upload
                  class="cover-uploader"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :http-request="customRequest">
                  <img v-if="formData.ugcCover" :src="formData.ugcCover" class="cover-image">
                  <div v-else class="upload-placeholder">
                    <div>上传封面</div>
                  </div>
                </el-upload>
              </div>
            </div>
            
            <div class="resource-item" v-if="formData.attachment">
              <div class="resource-label">附件：</div>
              <div class="attachment-container">
                <div class="attachment-info">
                  <a :href="formData.attachment" target="_blank" class="attachment-link">
                    {{ formData.attachmentName || '查看附件' }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="">
          <div class="creator-info">
            <div class="info-item">
              <span class="info-label">编辑者：</span>
              <span class="info-value">{{ formData.createdName || '未知' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">编辑时间：</span>
              <span class="info-value">{{ formData.createdTime || '未知' }}</span>
            </div>
          </div>
        </el-form-item>


      </el-form>
    </div>
  </div>
</template>

<script>
import { ugcDetail, updateUgc, uploadAvatar } from "@/request/service.js";
import { ElMessage } from "element-plus";
import htmlMethods from "@/utils/htmlFunction.js";
import MsEditor from "@/components/ms-editor/index"; // 确保正确导入ms-editor组件

export default {
  name: "ugcEdit",
  components: {
    MsEditor
  },
  data() {
    return {
      formData: {
        id: null,
        title: "",
        content: "",
        ugcCover: "",
        attachment: "",
        attachmentName: ""
      },
      attachmentList: []
    };
  },
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getUgcDetail(id);
    }
  },
  methods: {
    getUgcDetail(id) {
      ugcDetail(id).then((res) => {
        this.formData = res.data;
        // Unescape HTML content for the editor
        if (this.formData.content) {
          this.formData.content = htmlMethods.unexcapeHtml(this.formData.content);
        }
        
        // Update attachment list for display
        if (this.formData.attachment) {
          this.attachmentList = [{
            name: this.formData.attachmentName || '附件',
            url: this.formData.attachment
          }];
        }
      }).catch(err => {
        ElMessage.error('获取内容详情失败：' + err.message);
      });
    },
    
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        ElMessage.error('只能上传图片格式文件!');
        return false;
      }
      if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    
    filetoDataUrl(file, callback) {
      const reader = new FileReader();
      reader.onload = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(file);
    },
    
    customRequest(item) {
      const _this = this;
      this.filetoDataUrl(item.file, function(img) {
        const params = {
          base64: img
        };
        uploadAvatar(params).then((res) => {
          if (res.status === 200) {
            _this.formData.ugcCover = res.data.url;
            ElMessage.success('封面上传成功');
          } else {
            ElMessage.error(res.message || '封面上传失败');
          }
        }).catch(err => {
          ElMessage.error('上传失败: ' + err.message);
        });
      });
    },
    
    goBack() {
      this.$router.push('/ugc');
    },
    
    submitContent() {
      if (!this.formData.title) {
        ElMessage.warning('请输入标题');
        return;
      }
      
      if (!this.formData.content) {
        ElMessage.warning('请输入正文内容');
        return;
      }
      
      const params = {
        id: this.formData.id,
        title: this.formData.title,
        content: htmlMethods.excapeHtml(this.formData.content),
        ugcCover: this.formData.ugcCover
      };
      
      updateUgc(params).then(res => {
        if (res.status === 200) {
          ElMessage.success('保存成功');
          this.$router.push('/ugc');
        } else {
          ElMessage.error(res.message || '保存失败');
        }
      }).catch(err => {
        ElMessage.error('保存失败：' + err.message);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.ugc-edit {
  background-color: #fff;
  
  .edit-container {
    padding: 20px;
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
      
      h2 {
        margin: 0;
        font-weight: 500;
      }
      
      .actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .edit-form {
      max-width: 1000px;
      
      .editor-container {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
      
      .resources-row {
        display: flex;
        width: 100%;
        
        .resource-item {
          width: 50%;
          box-sizing: border-box;
          padding-right: 20px;
          
          &:last-child {
            padding-right: 0;
          }
          
          .resource-label {
            font-size: 14px;
            margin-bottom: 10px;
            color: #606266;
          }
        }
      }
      
      .upload-container {
        width: 100%;
        
        .cover-image {
          width: 120px;
          height: 120px;
          object-fit: cover;
        }
        
        .upload-placeholder {
          width: 120px;
          height: 120px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dashed #d9d9d9;
          border-radius: 4px;
          background-color: #fafafa;
          color: #8c939d;
          
          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }
      }
    }
  }
}

.attachment-container {
  width: 100%;
  
  .attachment-info {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #f8f8f8;
    border-radius: 4px;
    box-sizing: border-box;
    
    .attachment-link {
      color: #409eff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.creator-info {
  display: flex;
  margin-top: 10px;
  width: 100%;

  .info-item {
    width: 50%;
    box-sizing: border-box;
    padding-right: 20px;

    &:last-child {
      padding-right: 0;
    }

    .info-label {
      color: #606266;
      margin-right: 8px;
    }

    .info-value {
      color: #303133;
    }
  }
}

.sensitive-info {
  display: flex;
  margin-top: 10px;
  width: 100%;

  .info-item {
    width: 100%;
    box-sizing: border-box;

    .info-label {
      color: #606266;
      margin-right: 8px;
    }

    .info-value {
      color: #303133;
    }
  }
}
</style>
