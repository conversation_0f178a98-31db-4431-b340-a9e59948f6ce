<template>
  <div class="yxd_yxd">
    <div class="container">
      <div class="header">
        <div class="left">
          <div class="col">
            <el-date-picker
              v-model="lineTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="查询开始时间"
              end-placeholder="查询结束时间"
              :default-time="defaultTime"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
            <el-tooltip
              popper-class="atooltip"
              effect="light"
              placement="top"
              style="margin-left:15px"
            >
              <i
                class="el-icon-question"
                style="font-size: 14px; vertical-align: middle"
              ></i>
              <template #content>
                <div>
                  <div>默认筛选并展示当天的操作日志</div>
                </div>
              </template>
            </el-tooltip>
          </div>
        </div>
        <div class="right">
          <el-button @click="init" icon="el-icon-search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div>
        <div class="table">
          <el-table
            ref="multipleTable"
            border
            :data="tableData"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <el-table-column prop="userId" label="操作人Id" align="center">
            </el-table-column>
            <el-table-column prop="username" label="操作人名称" align="center">
            </el-table-column>

            <el-table-column prop="type" label="日记类型" align="center">
              <template #default="scope">
                {{
                  scope.row.type == 1
                    ? "系统操作日志"
                    : scope.row.type == 2
                    ? "异常记录日志"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="requestIp"
              label="操作用户的ip"
              align="center"
            >
            </el-table-column>
            <el-table-column prop="requestUrl" label="请求路径" align="center">
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="createTime"
              align="center"
            >
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import {
  edaGetAriticlePage,
  edaDeleteTag,
  edaBatchEdit,
  tagTypeList,
  query,
  listTag,
  batchLineModule,
  yxdlog,
} from "@/request/service.js";
import { exportUserEda, edaList } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import controlDialog from "../components/controlDialog.vue";
import classifyDialog from "../components/classifyDialog.vue";
export default {
  name: "meet",
  mixins: [initModule],
  components: {
    controlDialog,
    classifyDialog,
  },
  data() {
    return {
      lineTime: [],
      currentPage: 1,
      pageSize: 20,
      tableData: [],
    };
  },
  mounted() {
    this.defa();
  },
  computed: {},
  methods: {
    defa() {
      var now = new Date();
      var year = now.getFullYear(); //得到年份
      var month = now.getMonth() + 1; //得到月份
      if (month < 9) {
        month = "0" + month;
      }
      var date = now.getDate(); //得到日期
      // var day = now.getDay();//得到周几
      var hour = now.getHours(); //得到小时数
      var minute = now.getMinutes(); //得到分钟数
      var second = now.getSeconds(); //得到秒数
      this.lineTime = [
        `${year}-${month}-${date} 00:00:00`,
        `${year}-${month}-${date} 23:59:59`,
      ];
      this.init();
    },
    reset() {
      this.defa();
    },
    init() {
      this.tableData = [];
      let params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        startTime:this.lineTime.length&& this.lineTime[0]?this.lineTime[0]:"",
        endTime: this.lineTime.length&&this.lineTime[1]?this.lineTime[1]:"",
      };
      yxdlog(params).then((res) => {
        this.total = res.totalSize;
        this.tableData = res.data;
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
        this.init()
    },
    handleCurrentChange(val) {
      this.currentPage = val;
        this.init()
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_yxd {
  .header {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    .left {
      display: flex;
      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }
}
.edit-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    width: 58.333%;
    display: flex;
    align-items: center;
    span {
      white-space: nowrap;
      margin-right: 10px;
    }
    .edit-tag {
      min-height: 30px;
      min-width: 80px;
      border-bottom: 1px solid #dcdfe6;
      .el-tag--mini {
        margin-bottom: 3px;
      }
    }
  }
  .right {
  }
}
.edit-main {
  display: flex;
  margin-top: 30px;
  .title {
    display: flex;
    margin-top: 8px;
    margin-bottom: 6px;
    overflow: hidden;
    height: auto;
    span {
      // width: 70px;
      // font-size: 12px;
      // color: #409eff;
      // text-align: right;
      // margin-right: 20px;
      // font-weight: 600;
      // margin-top: 3px;
      margin-right: 10px;
    }
    .group {
      .btn {
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color: #f0f2f5;
        font-size: 11px;
        font-weight: 400;
        display: inline-block;
        cursor: pointer;
        margin-right: 20px;
        margin-bottom: 6px;
      }
      .checked {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}
</style>
