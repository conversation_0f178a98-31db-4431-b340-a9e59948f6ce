<template>
  <div class="yxd_order">
    <div
      id="container"
      class="container"
      :class="{ isScroll: isScroll }"
      ref="container"
    >
      <div class="header">
        <div class="left">
          <div class="col">
            <span class="tl">券码名称</span>
            <el-input v-model="title" placeholder="请输入"></el-input>
          </div>
          <div class="col">
            <span class="tl">创建时间</span>
            <el-date-picker
              v-model="lineTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="right">
          <el-button icon="el-icon-search" @click="init()">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
      <div class="operate">
        <div class="operate-right">
          <el-button @click="add" type="primary" class="article-total"
            >创建券码</el-button
          >
        </div>
      </div>

      <div class="table">
        <el-table
          ref="multipleTable"
          border
          :data="tableData"
          :default-sort="{ prop: 'date', order: 'descending' }"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55" align="center">
          </el-table-column> -->
          <el-table-column
            prop="id"
            label="券码ID"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="title"
            label="券码名称"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="price"
            label="券码面值（元）"
            min-width="80"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="num"
            label="券码数量（个）"
            min-width="80"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="remaining"
            label="剩余可预支数量（个）"
            min-width="100"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="formName"
            label="券码有效期"
            min-width="180"
            align="center"
          >
          <template #default="scope">
            {{scope.row.expirationStartTime}}-{{scope.row.expirationEndTime}}
          </template>
          </el-table-column>
          <el-table-column
            prop="createdName"
            label="创建人"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="createdTime"
            label="创建时间"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="220"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                size="mini"
                @click="goTab( scope.row)"
              >
                编辑
              </el-button>
              <el-button
              plain
              :type="scope.row.status ? 'danger' : 'primary'"
              size="mini"
              @click="handleStatus(scope.row)"
              >{{ scope.row.status ? "禁用" : "启用" }}</el-button
            >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <classify-dialog
        ref="classifyRef"
        :choseData="choseData"
      ></classify-dialog>
      <el-dialog title="删除提示" v-model="deleteVisible" width="70%">
        <span
          >是否删除 <el-tag>{{ currentChose.formName }}</el-tag></span
        >
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deleteVisible = false" size="mini"
              >取 消</el-button
            >
            <el-button type="primary" @click="deleteSave" size="mini"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 添加/编辑 -->
      <el-dialog :title="titleValue" width="1000px" v-model="addVisible">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
          <el-col :span="12">
          <el-form-item label="券码名称" prop="title">
            <el-input v-model="form.title" show-word-limit maxlength="64"  placeholder="请输入券码名称"  clearable></el-input>
          </el-form-item>
          </el-col>
          <el-col :span="12">
          <el-form-item label="券码数量" prop="title" v-if="state=='edit'">
            <el-input v-model="form.num"  placeholder="请输入券码数量" :disabled="state=='edit'"  clearable></el-input>
          </el-form-item>
          </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="券码面值" prop="price">
            <el-input-number v-model="form.price"  placeholder="" :min="1" :step="1"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="券码有效期" prop="TimeList">
            <el-date-picker
              v-model="form.TimeList"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              unlink-panels
              value-format="YYYY-MM-DD HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="券码描述" prop="description">
        <el-input type="textarea" v-model="form.description" placeholder="请输入券码说明，如用途等 " :rows="4" ></el-input>
      </el-form-item>
      <el-form-item label="上传券码" prop="excelFile" v-if="state!=='edit'">
        <el-upload
          class="upload-demo"
          accept=".xls, .xlsx, .csv"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :on-remove="handleRemove"
        >
          <el-button icon="el-icon-upload2" >点击上传</el-button>
          <div  class="upload-tip" >支持扩展名：.xls .xlsx .csv</div>
        </el-upload>
        <a class="down" href="https://static.medsci.cn/doc/%E5%88%B8%E7%A0%81%E5%8D%A1%E5%AF%86.xlsx">下载模板</a>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="addVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </div>
  </el-dialog>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import {
 voucherList,
 voucherAdd,
 voucherEdit,
 voucherOperate,
 voucherDetail
} from "@/request/service.js";
import { exportUserBehavior } from "@/request/download.js";
import initModule from "@/mixins/initModule.js";
import initScroll from "@/mixins/initScroll.js";
import formMixin from "@/mixins/form-mixin";
import tableMixins from "@/mixins/table";
import controlDialog from "@/components/controlDialog.vue";
import classifyDialog from "@/components/classifyDialog.vue";
import Template from './template.vue';
export default {
  name: "yxd_order",
  mixins: [initModule, initScroll, formMixin, tableMixins],
  components: {
    controlDialog,
    classifyDialog,
  },
  data() {
    return {
      title: "",
      lineTime: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      state:"",
      tableData: [],
      // powerVisible: true,
      deleteVisible: false,
      addVisible: false,
      lineText: "上线提示",
      currentChose: {},
      RoleList: [],
      choseData: [],
      ClassifyData: [],
      radio: 1,
      selectPower: "",
      // checkList: [],
      selectvalue: [],
      ClassifyValue: [],
      createUserId: "",
      classGroup: [],
      form: {},
      fileList:[],
      titleValue:"",
      rules: {
        title: [
          { required: true, message: "请输入券码名称", trigger: "blur" },
        ],
        price: [
          { required: true, message: "请输入券码面值", trigger: "blur" },
        ],
        TimeList: [
          {
            type: "array",
            required: true,
            message: "请选择券码有效期",
            trigger: "change",
          },
        ],
        excelFile: [{ required: true, message: '请上传券码文件', trigger: 'change' }]
      },
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    tags() {
      return this.classGroup.filter((val) => {
        return val.checked;
      });
    },
    projectId() {
      return this.$store.state.projectInfo.id;
    },
  },
  methods: {
    down(){
     location.herf = "https://static.medsci.cn/doc/%E5%88%B8%E7%A0%81%E5%8D%A1%E5%AF%86.xlsx"
    },
    //   上传
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = ["xls", "xlsx", "csv"];
      const isValidExtension = allowedExtensions.includes(extension);
      if (!isValidExtension) {
        this.$message.error("只支持上传 .xls, .xlsx, .csv 格式的文件");
      }
      this.form.excelFile = file
      this.fileList=[{name:file.name,url:''}]
      return isValidExtension
    },
    handleRemove(){
      this.fileList=[]
       this.form.excelFile=""
    },
     // 修改状态
    handleStatus(row){
      let params={
        id: row.id,
        status: row.status == 0 ? 1 : 0
      }
      voucherOperate(params).then((res) => {
          if (res.status == 200) {
            this.init()
          }
        })
    },
    // 提交
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.title.length>64){
            ElMessage.warning("券码名称不能超过64个字");
            return;
           }
           this.form.expirationEndTime = this.form.TimeList[1].replace("00:00:00","23:59:59")
          this.form.expirationStartTime = this.form.TimeList[0]
           let data = new FormData();
            data.append("expirationEndTime", this.form.expirationEndTime);
            data.append("expirationStartTime", this.form.expirationStartTime);
            data.append("title", this.form.title);
            data.append("description", this.form.description?this.form.description:'');
            data.append("excelFile", this.form.excelFile);
            data.append("price", this.form.price);

         if(this.state =='add'){
           voucherAdd(data).then((res) => {
            if (res.status == 200) {
              this.init()
              this.addVisible = false
            }else{
              this.addVisible = false
            }
          });
         }else{
           voucherEdit(this.form).then((res) => {
            if (res.status == 200) {
              this.init()
              this.addVisible = false
            }else{
              this.addVisible = false
            }
          });
         }
          this.dialogVisible = false;
        }
      });
    },
    //初始化
    init() {
      this.tableData = [];
      let publishedStartTime;
      let publishedEndTime;
      if (this.lineTime && this.lineTime.length > 0) {
        publishedStartTime = this.lineTime[0];
        publishedEndTime = this.lineTime[1];
      }
      let params = {
        title: this.title,
        createdStartTime: publishedStartTime,
        createdEndTime: publishedEndTime,
        currentPageNo: this.currentPage,
        pageSize: this.pageSize,
      };
      // if(!this.selectvalue){
      //   params.roleIds=[]
      // }else if(this.selectvalue.length===1){
      //   params.roleIds=[this.selectvalue[0]]
      // }else if(this.selectvalue.length===2){
      //   params.roleIds=[this.selectvalue[1]]
      // }else{
      //   params.roleIds=[this.selectvalue[2]]
      // }
      voucherList(params).then((res) => {
        if (res.status == 200) {
          this.total = res.totalSize;
          this.tableData = res.data;
          this.initScroll();
          // this.tableData.forEach((val) => {
          //   if (val.approvalStatus == 1) {
          //     val.approvalStatus = "已上线";
          //   } else {
          //     val.approvalStatus = "已下线";
          //   }
          //   val.gotoUrl =
          //     `https://` +
          //     window.location.host +
          //     `/news/detail/${val.encodeId}`;
          // });
        }
      });
    },
    // 创建券码
    add(index, item) {
      this.addVisible = true;
      this.state="add"
      this.form = {}
      this.fileList=[]
      this.titleValue = "添加券码"
    },
        // 编辑
    goTab(row){
      this.addVisible = true;
      this.state="edit"
      this.titleValue = "编辑券码"
      let params={
        id:row.id
      }
      voucherDetail(params).then((res) => {
        this.form = res.data
        this.form.TimeList =this.form.expirationStartTime?[this.form.expirationStartTime ,this.form.expirationEndTime]:[]
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.init();
    },
    reset() {
      this.title = "";
      this.lineTime = "";
      this.$refs.multipleTable.clearSelection();
      this.selectvalue = [];
      this.ClassifyValue = [];
      let obj = {};
      obj.stopPropagation = () => {};
      try {
        this.$refs.cascader.clearValue(obj);
        this.$refs.Classify.clearValue(obj);
      } catch (err) {
        this.$refs.cascader.handleClear(obj);
        this.$refs.Classify.handleClear(obj);
      }
    },

    handleDown(index, row) {
      this.currentChose = row;
    },

    handleSelectionChange(selection) {
      this.choseData = selection;
    },
    // 单条删除
    deleteSave() {
      let params = {
        projectId: this.$store.state.projectInfo.id,
        id: this.currentChose.id,
        formId: this.currentChose.formId,
      };
      formBatchDelete(params).then((res) => {
        if (res.status == 200) {
          this.deleteVisible = false;
          this.init();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.yxd_order {
  .down{
    margin-top: 5px;
  }
  //  ::v-deep {
  //     .el-cascader-panel {
  //       background-color: #ff0;
  //     }
  //   }
  .default {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: #7f7f7f;
    margin-right: 5px;
  }

  .start {
    border-radius: 50%;
    width: 10px;
    height: 10px;
    background: rgb(62, 201, 119);
    margin-right: 5px;
  }

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .col {
        display: flex;
        align-items: center;
        margin-right: 20px;
        white-space: nowrap;

        .tl {
          opacity: 1;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          margin-right: 20px;
          white-space: nowrap;
        }
      }
    }
  }

  .operate {
    .operate-right {
      flex: 1;
      .article-total {
        float: right;
        border-radius: 4px;
      }
    }
  }

  .table {
    .status-label {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 6px;
      display: inline-block;
      position: relative;
      top: 1px;
    }
  }

  .power-head {
    table {
      .line {
        height: 45px;
      }

      tr {
        padding: 30px 0;

        .title {
          width: 70px;
          // font-size: 20px;
        }
      }
    }
  }

  .edit-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      width: 58.333%;
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        margin-right: 10px;
      }

      .edit-tag {
        min-height: 30px;
        min-width: 80px;
        border-bottom: 1px solid #dcdfe6;

        .el-tag--mini {
          margin-bottom: 3px;
        }
      }
    }
  }

  .edit-main {
    display: flex;
    margin-top: 30px;

    .title {
      display: flex;
      margin-top: 8px;
      margin-bottom: 6px;
      overflow: hidden;
      height: auto;

      span {
        // width: 70px;
        // font-size: 12px;
        // color: #409eff;
        // text-align: right;
        // margin-right: 20px;
        // font-weight: 600;
        // margin-top: 3px;
        margin-right: 10px;
      }

      .group {
        .btn {
          height: 24px;
          line-height: 24px;
          padding: 0 15px;
          border-radius: 5px;
          background-color: #f0f2f5;
          font-size: 11px;
          font-weight: 400;
          display: inline-block;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 6px;
        }

        .checked {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }

  .delete-box {
    margin-top: 10px;
    margin-right: 10px;
  }
}
.filecolor{
  color: #409eff;
}
.dialog-footer{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="scss">
.el-input--suffix {
  width: 350px !important;
}
.el-select{
  .el-input--suffix {
  width: auto !important;
}
}

.el-date-editor--datetimerange {
  width: 260px !important;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
}
.el-upload{
    text-align: left;
}
</style>
