const path = require("path");
function resolve(dir) {
  return path.join(__dirname, dir);
}
module.exports = {
  lintOnSave: false,
  publicPath: process.env.NODE_ENV == "production" ? "././" : "/",
  chainWebpack: (config) => {
    config.resolve.alias.set("@", resolve("src"));
  },
  productionSourceMap: false,
  devServer: {
    open: false,
    host: "0.0.0.0",
    https: false,
    hot: true,
    disableHostCheck: true,
    proxy: {
      // "/apis/yxd/"
      "/apis/yxd/": {
        ws: true,
        
        
        // 开发环境
        // target: "http://**********:9997",
        target: "https://medsci-gateway.medon.com.cn",
        // target: "http://*********:17091",
        changeOrigin: true,
        pathRewrite: {
          "^/apis/yxd/": "/api/yxd/",
          // "^/apis/yxd/": "/yxd/",
        },
      },
    },
  },
};
